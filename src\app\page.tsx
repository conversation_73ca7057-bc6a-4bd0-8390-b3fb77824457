import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100" dir="rtl">
      <div className="container mx-auto px-4 py-16">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-800 mb-4">
            🤖 منصة بناء المواقع الذكية
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            ابني موقعك أو تطبيقك بسهولة باستخدام الذكاء الاصطناعي.
            اسحب العناصر، اكتب وصف الوظيفة، ودع الذكاء الاصطناعي يولد الكود لك!
          </p>
        </div>

        {/* Features */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <div className="bg-white p-6 rounded-lg shadow-lg text-center">
            <div className="text-4xl mb-4">🎨</div>
            <h3 className="text-xl font-bold mb-2">سحب وإفلات بسيط</h3>
            <p className="text-gray-600">
              اسحب العناصر من اللوحة وضعها في المكان المناسب بسهولة تامة
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-lg text-center">
            <div className="text-4xl mb-4">🧠</div>
            <h3 className="text-xl font-bold mb-2">ذكاء اصطناعي متقدم</h3>
            <p className="text-gray-600">
              اكتب وصف الوظيفة بلغة طبيعية ودع الذكاء الاصطناعي يولد الكود
            </p>
          </div>

          <div className="bg-white p-6 rounded-lg shadow-lg text-center">
            <div className="text-4xl mb-4">⚡</div>
            <h3 className="text-xl font-bold mb-2">سريع ومرن</h3>
            <p className="text-gray-600">
              احفظ مشروعك، شاركه، أو صدره كموقع جاهز للنشر
            </p>
          </div>
        </div>

        {/* CTA */}
        <div className="text-center">
          <Link
            href="/editor"
            className="inline-block bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg text-xl transition-colors shadow-lg hover:shadow-xl transform hover:scale-105"
          >
            🚀 ابدأ البناء الآن
          </Link>
          <p className="text-sm text-gray-500 mt-4">
            مجاني تماماً • لا يتطلب تسجيل دخول
          </p>
        </div>

        {/* How it works */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-center mb-12 text-gray-800">
            كيف يعمل؟
          </h2>
          <div className="grid md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-blue-600">1</span>
              </div>
              <h4 className="font-bold mb-2">اسحب العناصر</h4>
              <p className="text-sm text-gray-600">اختر الأزرار والنصوص والصور</p>
            </div>

            <div className="text-center">
              <div className="bg-green-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-green-600">2</span>
              </div>
              <h4 className="font-bold mb-2">اكتب الوظيفة</h4>
              <p className="text-sm text-gray-600">صف ما تريد أن يفعله العنصر</p>
            </div>

            <div className="text-center">
              <div className="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-purple-600">3</span>
              </div>
              <h4 className="font-bold mb-2">الذكاء يولد الكود</h4>
              <p className="text-sm text-gray-600">يفهم الطلب وينشئ الكود تلقائياً</p>
            </div>

            <div className="text-center">
              <div className="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl font-bold text-orange-600">4</span>
              </div>
              <h4 className="font-bold mb-2">احفظ وشارك</h4>
              <p className="text-sm text-gray-600">صدر موقعك أو احفظه للتطوير لاحقاً</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
