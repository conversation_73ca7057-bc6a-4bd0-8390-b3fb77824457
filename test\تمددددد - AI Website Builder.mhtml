From: <Saved by Blink>
Snapshot-Content-Location: http://localhost:3000/editor
Subject: =?utf-8?Q?=D8=AA=D9=85=D8=AF=D8=AF=D8=AF=D8=AF=D8=AF=20-=20AI=20Website?=
 =?utf-8?Q?=20Builder?=
Date: Mon, 21 Jul 2025 22:48:14 +0300
MIME-Version: 1.0
Content-Type: multipart/related;
	type="text/html";
	boundary="----MultipartBoundary--fMhVSzyTwHauVOsjgCwnDvnHSyaD7mUIrpPPqdaxBT----"


------MultipartBoundary--fMhVSzyTwHauVOsjgCwnDvnHSyaD7mUIrpPPqdaxBT----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: http://localhost:3000/editor

<!DOCTYPE html><html lang=3D"en" style=3D"overflow: hidden;"><head><meta ht=
tp-equiv=3D"Content-Type" content=3D"text/html; charset=3DUTF-8"><link rel=
=3D"stylesheet" type=3D"text/css" href=3D"cid:css-2ab1236d-40c1-40e3-b924-7=
<EMAIL>" /><link rel=3D"stylesheet" type=3D"text/css" href=
=3D"cid:<EMAIL>" /><meta name=
=3D"viewport" content=3D"width=3Ddevice-width, initial-scale=3D1"><link rel=
=3D"stylesheet" href=3D"http://localhost:3000/_next/static/chunks/%5Broot-o=
f-the-server%5D__8ebb6d4b._.css" data-precedence=3D"next_static/chunks/[roo=
t-of-the-server]__8ebb6d4b._.css"><link rel=3D"preload" as=3D"script" fetch=
priority=3D"low" href=3D"http://localhost:3000/_next/static/chunks/%5Bturbo=
pack%5D_browser_dev_hmr-client_hmr-client_ts_17e42fcf._.js"><meta name=3D"n=
ext-size-adjust" content=3D""><link rel=3D"icon" href=3D"http://localhost:3=
000/favicon.ico?favicon.45db1c09.ico" sizes=3D"256x256" type=3D"image/x-ico=
n"><link rel=3D"preload" href=3D"http://localhost:3000/_next/static/media/g=
yByhwUxId8gMEwcGFWNOITd-s.p.da1ebef7.woff2" as=3D"font" crossorigin=3D"" ty=
pe=3D"font/woff2"><link rel=3D"preload" href=3D"http://localhost:3000/_next=
/static/media/or3nQ6H_1_WfwkMZI_qYFrcdmhHkjko-s.p.be19f591.woff2" as=3D"fon=
t" crossorigin=3D"" type=3D"font/woff2"></head><body class=3D"geist_e531dab=
c-module__QGiZLq__variable geist_mono_68a01160-module__YLcDdW__variable ant=
ialiased" style=3D"overflow: hidden;"><div dir=3D"rtl" class=3D"jsx-2792527=
110 h-screen w-screen flex flex-col overflow-hidden"><div class=3D"jsx-2792=
527110 bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 shadow-l=
g"><div class=3D"jsx-********** flex items-center justify-between"><div cla=
ss=3D"jsx-********** flex items-center gap-4"><h1 class=3D"jsx-********** t=
ext-xl font-bold">=F0=9F=8E=A8 AI Website Builder</h1><div class=3D"jsx-279=
2527110 flex items-center gap-2 text-sm"><input type=3D"text" placeholder=
=3D"=D8=A7=D8=B3=D9=85 =D8=A7=D9=84=D9=85=D8=B4=D8=B1=D9=88=D8=B9" class=3D=
"jsx-********** bg-white/20 text-white placeholder-white/70 px-3 py-1 round=
ed border-none outline-none w-48" value=3D"=D8=AA=D9=85=D8=AF=D8=AF=D8=AF=
=D8=AF=D8=AF"><input type=3D"text" placeholder=3D"=D9=88=D8=B5=D9=81 =D8=A7=
=D9=84=D9=85=D8=B4=D8=B1=D9=88=D8=B9 (=D8=A7=D8=AE=D8=AA=D9=8A=D8=A7=D8=B1=
=D9=8A)" class=3D"jsx-********** bg-white/20 text-white placeholder-white/7=
0 px-3 py-1 rounded border-none outline-none w-64" value=3D""></div></div><=
div class=3D"jsx-********** flex items-center gap-3"><div class=3D"jsx-2792=
527110 flex items-center gap-2 border-r border-white/20 pr-3"><button title=
=3D"=D9=85=D8=B4=D8=B1=D9=88=D8=B9 =D8=AC=D8=AF=D9=8A=D8=AF" class=3D"jsx-2=
792527110 px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded font-m=
edium transition-colors flex items-center gap-2"><svg width=3D"16" height=
=3D"16" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-=
width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D""><=
path d=3D"M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></pat=
h><polyline points=3D"14,2 14,8 20,8"></polyline><line x1=3D"12" y1=3D"11" =
x2=3D"12" y2=3D"17"></line><line x1=3D"9" y1=3D"14" x2=3D"15" y2=3D"14"></l=
ine></svg><span class=3D"jsx-********** text-sm">=D8=AC=D8=AF=D9=8A=D8=AF</=
span></button><button title=3D"=D8=A7=D8=B3=D8=AA=D9=8A=D8=B1=D8=A7=D8=AF =
=D9=85=D8=B4=D8=B1=D9=88=D8=B9" class=3D"jsx-********** px-3 py-2 bg-green-=
500 hover:bg-green-600 text-white rounded font-medium transition-colors fle=
x items-center gap-2"><svg width=3D"16" height=3D"16" viewBox=3D"0 0 24 24"=
 fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D=
"round" stroke-linejoin=3D"round" class=3D""><path d=3D"M21 15v4a2 2 0 0 1-=
2 2H5a2 2 0 0 1-2-2v-4"></path><polyline points=3D"17,8 12,3 7,8"></polylin=
e><line x1=3D"12" y1=3D"3" x2=3D"12" y2=3D"15"></line></svg><span class=3D"=
jsx-********** text-sm">=D8=A7=D8=B3=D8=AA=D9=8A=D8=B1=D8=A7=D8=AF</span></=
button><button title=3D"=D8=AA=D8=B5=D8=AF=D9=8A=D8=B1 =D8=A7=D9=84=D9=85=
=D8=B4=D8=B1=D9=88=D8=B9" class=3D"jsx-********** px-3 py-2 bg-orange-500 h=
over:bg-orange-600 text-white rounded font-medium transition-colors flex it=
ems-center gap-2"><svg width=3D"16" height=3D"16" viewBox=3D"0 0 24 24" fil=
l=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"rou=
nd" stroke-linejoin=3D"round" class=3D""><path d=3D"M21 15v4a2 2 0 0 1-2 2H=
5a2 2 0 0 1-2-2v-4"></path><polyline points=3D"7,10 12,15 17,10"></polyline=
><line x1=3D"12" y1=3D"15" x2=3D"12" y2=3D"3"></line></svg><span class=3D"j=
sx-********** text-sm">=D8=AA=D8=B5=D8=AF=D9=8A=D8=B1</span></button></div>=
<button title=3D"=D9=85=D9=84=D8=A1 =D8=A7=D9=84=D8=B4=D8=A7=D8=B4=D8=A9" c=
lass=3D"jsx-********** px-3 py-2 rounded font-medium transition-colors bg-w=
hite/10 hover:bg-white/30">=E2=9B=B6</button><button class=3D"jsx-279252711=
0 px-4 py-2 rounded font-medium transition-colors bg-blue-500 hover:bg-blue=
-600"><div class=3D"jsx-********** flex items-center gap-2"><svg width=3D"1=
6" height=3D"16" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor=
" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" cla=
ss=3D""><rect x=3D"3" y=3D"11" width=3D"18" height=3D"10" rx=3D"2" ry=3D"2"=
></rect><circle cx=3D"12" cy=3D"5" r=3D"2"></circle><path d=3D"M12 7v4"></p=
ath><line x1=3D"8" y1=3D"16" x2=3D"8" y2=3D"16"></line><line x1=3D"16" y1=
=3D"16" x2=3D"16" y2=3D"16"></line></svg><span class=3D"jsx-**********">=D8=
=A7=D8=B3=D8=AA=D8=AE=D8=AF=D8=A7=D9=85 AI</span></div></button><button tit=
le=3D"=D9=85=D8=B9=D8=A7=D9=8A=D9=86=D8=A9 =D9=88=D8=AA=D9=86=D8=B2=D9=8A=
=D9=84 =D8=A7=D9=84=D9=85=D8=B4=D8=B1=D9=88=D8=B9 =D8=A8=D8=AF=D9=88=D9=86 =
AI" class=3D"jsx-********** px-4 py-2 rounded font-medium transition-colors=
 text-white bg-green-500 hover:bg-green-600"><div class=3D"jsx-********** f=
lex items-center gap-2"><svg width=3D"16" height=3D"16" viewBox=3D"0 0 24 2=
4" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=
=3D"round" stroke-linejoin=3D"round" class=3D""><path d=3D"M1 12s4-8 11-8 1=
1 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx=3D"12" cy=3D"12" r=3D"3"></=
circle></svg><span class=3D"jsx-**********">=D9=85=D8=B9=D8=A7=D9=8A=D9=86=
=D8=A9 =D9=88=D8=AA=D9=86=D8=B2=D9=8A=D9=84</span></div></button></div></di=
v></div><div style=3D"min-height:100vh" class=3D"jsx-********** flex-1 flex=
"><div class=3D"jsx-********** flex"><div class=3D"w-64 bg-white border-r b=
order-gray-200 flex flex-col h-full transition-all duration-300 ease-in-out=
"><div class=3D"p-2 border-b border-gray-200 bg-gradient-to-r from-blue-50 =
to-purple-50 flex-shrink-0"><div class=3D"flex items-center justify-between=
"><button class=3D"w-6 h-6 flex items-center justify-center hover:bg-white/=
50 rounded transition-colors" title=3D"=D8=B7=D9=8A =D9=82=D8=A7=D8=A6=D9=
=85=D8=A9 =D8=A7=D9=84=D8=B7=D8=A8=D9=82=D8=A7=D8=AA"><svg width=3D"14" hei=
ght=3D"14" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stro=
ke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"=
text-blue-600 transition-transform duration-300 rotate-0"><polyline points=
=3D"6,9 12,15 18,9"></polyline></svg></button><h3 class=3D"text-sm font-bol=
d text-right text-gray-800 flex items-center gap-2"><svg width=3D"16" heigh=
t=3D"16" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke=
-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"te=
xt-blue-600"><rect x=3D"3" y=3D"3" width=3D"7" height=3D"7"></rect><rect x=
=3D"14" y=3D"3" width=3D"7" height=3D"7"></rect><rect x=3D"14" y=3D"14" wid=
th=3D"7" height=3D"7"></rect><rect x=3D"3" y=3D"14" width=3D"7" height=3D"7=
"></rect></svg>=D8=B7=D8=A8=D9=82=D8=A7=D8=AA =D8=A7=D9=84=D8=B5=D9=81=D8=
=AD=D8=A9</h3></div></div><div class=3D"flex-1 overflow-y-auto"><div class=
=3D"py-1"><div class=3D"select-none  transition-all duration-200"><div clas=
s=3D"flex items-center gap-1 px-2 py-1 text-xs hover:bg-gray-100 cursor-poi=
nter transition-colors  " style=3D"padding-left: 8px;"><button class=3D"w-4=
 h-4 flex items-center justify-center hover:bg-gray-200 rounded"><svg width=
=3D"12" height=3D"12" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"current=
Color" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round=
" class=3D"text-gray-500 transition-transform -rotate-90"><polyline points=
=3D"6,9 12,15 18,9"></polyline></svg></button><div class=3D"text-purple-600=
"><svg width=3D"16" height=3D"16" viewBox=3D"0 0 24 24" fill=3D"none" strok=
e=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linej=
oin=3D"round" class=3D"text-gray-600"><rect x=3D"3" y=3D"3" width=3D"7" hei=
ght=3D"7"></rect><rect x=3D"14" y=3D"3" width=3D"7" height=3D"7"></rect><re=
ct x=3D"14" y=3D"14" width=3D"7" height=3D"7"></rect><rect x=3D"3" y=3D"14"=
 width=3D"7" height=3D"7"></rect></svg></div><span class=3D"flex-1 truncate=
 text-gray-700">container 4932</span><div class=3D"flex items-center gap-1"=
><button class=3D"w-4 h-4 flex items-center justify-center hover:bg-gray-20=
0 rounded" title=3D"=D8=A5=D8=AE=D9=81=D8=A7=D8=A1"><svg width=3D"12" heigh=
t=3D"12" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke=
-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"te=
xt-gray-600"><path d=3D"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></pat=
h><circle cx=3D"12" cy=3D"12" r=3D"3"></circle></svg></button><button class=
=3D"w-4 h-4 flex items-center justify-center hover:bg-gray-200 rounded" tit=
le=3D"=D9=82=D9=81=D9=84"><svg width=3D"12" height=3D"12" viewBox=3D"0 0 24=
 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-lineca=
p=3D"round" stroke-linejoin=3D"round" class=3D"text-gray-400"><circle cx=3D=
"12" cy=3D"12" r=3D"3"></circle><path d=3D"M12 1v6m0 6v6m11-7h-6m-6 0H1m17-=
4a4 4 0 0 0-8 0m8 8a4 4 0 0 0-8 0"></path></svg></button><div draggable=3D"=
true" class=3D"w-4 h-4 flex items-center justify-center cursor-grab active:=
cursor-grabbing hover:bg-gray-200 rounded text-gray-400 hover:text-gray-600=
" title=3D"=D8=A7=D8=B3=D8=AD=D8=A8 =D9=84=D8=A5=D8=B9=D8=A7=D8=AF=D8=A9 =
=D8=AA=D8=B1=D8=AA=D9=8A=D8=A8 =D8=A7=D9=84=D8=B7=D8=A8=D9=82=D8=A9"><div c=
lass=3D"flex flex-col gap-0.5"><div class=3D"w-3 h-0.5 bg-current rounded">=
</div><div class=3D"w-3 h-0.5 bg-current rounded"></div><div class=3D"w-3 h=
-0.5 bg-current rounded"></div></div></div></div></div></div></div></div></=
div><div class=3D"w-72 bg-slate-50 border-r border-gray-300 overflow-y-auto=
 flex flex-col transition-all duration-300 ease-in-out" style=3D"height:cal=
c(100vh + 20px);border-bottom:none"><div class=3D"p-2 border-b border-gray-=
200 bg-gradient-to-r from-blue-50 to-purple-50 flex-shrink-0"><div class=3D=
"flex items-center justify-between"><button class=3D"w-6 h-6 flex items-cen=
ter justify-center hover:bg-white/50 rounded transition-colors" title=3D"=
=D8=B7=D9=8A =D9=85=D9=83=D8=AA=D8=A8=D8=A9 =D8=A7=D9=84=D8=B9=D9=86=D8=A7=
=D8=B5=D8=B1"><svg width=3D"14" height=3D"14" viewBox=3D"0 0 24 24" fill=3D=
"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" =
stroke-linejoin=3D"round" class=3D"text-blue-600 transition-transform durat=
ion-300 rotate-0"><polyline points=3D"6,9 12,15 18,9"></polyline></svg></bu=
tton><h3 class=3D"text-sm font-bold text-right text-gray-800 flex items-cen=
ter gap-2"><svg width=3D"16" height=3D"16" viewBox=3D"0 0 24 24" fill=3D"no=
ne" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" str=
oke-linejoin=3D"round" class=3D"text-blue-600"><rect x=3D"3" y=3D"3" width=
=3D"7" height=3D"7"></rect><rect x=3D"14" y=3D"3" width=3D"7" height=3D"7">=
</rect><rect x=3D"14" y=3D"14" width=3D"7" height=3D"7"></rect><rect x=3D"3=
" y=3D"14" width=3D"7" height=3D"7"></rect></svg>=D9=85=D9=83=D8=AA=D8=A8=
=D8=A9 =D8=A7=D9=84=D8=B9=D9=86=D8=A7=D8=B5=D8=B1</h3></div></div><div clas=
s=3D"overflow-y-auto flex-1 p-2 space-y-4"><div class=3D"space-y-2"><h4 cla=
ss=3D"text-sm font-semibold text-gray-800 text-right flex items-center just=
ify-end gap-2"><span>=D8=A3=D8=B3=D8=A7=D8=B3=D9=8A=D8=A7=D8=AA</span><svg =
width=3D"16" height=3D"16" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"cu=
rrentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"=
round" class=3D"text-blue-600"><rect x=3D"3" y=3D"3" width=3D"7" height=3D"=
7"></rect><rect x=3D"14" y=3D"3" width=3D"7" height=3D"7"></rect><rect x=3D=
"14" y=3D"14" width=3D"7" height=3D"7"></rect><rect x=3D"3" y=3D"14" width=
=3D"7" height=3D"7"></rect></svg></h4><div class=3D"grid grid-cols-2 gap-2"=
><button class=3D"bg-blue-500 text-white p-3 rounded-lg hover:opacity-90 tr=
ansition-all transform hover:scale-105" title=3D"=D8=A5=D8=B6=D8=A7=D9=81=
=D8=A9 =D8=B2=D8=B1"><div class=3D"mb-1 flex justify-center"><svg width=3D"=
16" height=3D"16" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColo=
r" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" cl=
ass=3D"text-white"><rect x=3D"3" y=3D"8" width=3D"18" height=3D"8" rx=3D"2"=
 ry=3D"2"></rect><path d=3D"M7 12h10"></path></svg></div><div class=3D"text=
-xs font-medium">=D8=B2=D8=B1</div></button><button class=3D"bg-green-500 t=
ext-white p-3 rounded-lg hover:opacity-90 transition-all transform hover:sc=
ale-105" title=3D"=D8=A5=D8=B6=D8=A7=D9=81=D8=A9 =D9=86=D8=B5"><div class=
=3D"mb-1 flex justify-center"><svg width=3D"16" height=3D"16" viewBox=3D"0 =
0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-li=
necap=3D"round" stroke-linejoin=3D"round" class=3D"text-white"><polyline po=
ints=3D"4,7 4,4 20,4 20,7"></polyline><line x1=3D"9" y1=3D"20" x2=3D"15" y2=
=3D"20"></line><line x1=3D"12" y1=3D"4" x2=3D"12" y2=3D"20"></line></svg></=
div><div class=3D"text-xs font-medium">=D9=86=D8=B5</div></button><button c=
lass=3D"bg-purple-500 text-white p-3 rounded-lg hover:opacity-90 transition=
-all transform hover:scale-105" title=3D"=D8=A5=D8=B6=D8=A7=D9=81=D8=A9 =D8=
=AD=D8=A7=D9=88=D9=8A"><div class=3D"mb-1 flex justify-center"><svg width=
=3D"16" height=3D"16" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"current=
Color" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round=
" class=3D"text-white"><rect x=3D"3" y=3D"3" width=3D"7" height=3D"7"></rec=
t><rect x=3D"14" y=3D"3" width=3D"7" height=3D"7"></rect><rect x=3D"14" y=
=3D"14" width=3D"7" height=3D"7"></rect><rect x=3D"3" y=3D"14" width=3D"7" =
height=3D"7"></rect></svg></div><div class=3D"text-xs font-medium">=D8=AD=
=D8=A7=D9=88=D9=8A</div></button><button class=3D"bg-cyan-500 text-white p-=
3 rounded-lg hover:opacity-90 transition-all transform hover:scale-105" tit=
le=3D"=D8=A5=D8=B6=D8=A7=D9=81=D8=A9 =D9=85=D9=86=D8=B7=D9=82=D8=A9 =D9=85=
=D8=AD=D8=AA=D9=88=D9=89"><div class=3D"mb-1 flex justify-center"><svg widt=
h=3D"16" height=3D"16" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"curren=
tColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"roun=
d" class=3D"text-white"><polyline points=3D"4,7 4,4 20,4 20,7"></polyline><=
line x1=3D"9" y1=3D"20" x2=3D"15" y2=3D"20"></line><line x1=3D"12" y1=3D"4"=
 x2=3D"12" y2=3D"20"></line></svg></div><div class=3D"text-xs font-medium">=
=D9=85=D9=86=D8=B7=D9=82=D8=A9 =D9=85=D8=AD=D8=AA=D9=88=D9=89</div></button=
><button class=3D"bg-pink-500 text-white p-3 rounded-lg hover:opacity-90 tr=
ansition-all transform hover:scale-105" title=3D"=D8=A5=D8=B6=D8=A7=D9=81=
=D8=A9 =D8=B5=D9=88=D8=B1=D8=A9"><div class=3D"mb-1 flex justify-center"><s=
vg width=3D"16" height=3D"16" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D=
"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=
=3D"round" class=3D"text-white"><rect x=3D"3" y=3D"3" width=3D"18" height=
=3D"18" rx=3D"2" ry=3D"2"></rect><circle cx=3D"8.5" cy=3D"8.5" r=3D"1.5"></=
circle><polyline points=3D"21,15 16,10 5,21"></polyline></svg></div><div cl=
ass=3D"text-xs font-medium">=D8=B5=D9=88=D8=B1=D8=A9</div></button></div></=
div><div class=3D"space-y-2"><h4 class=3D"text-sm font-semibold text-gray-8=
00 text-right flex items-center justify-end gap-2"><span>=D9=86=D9=85=D8=A7=
=D8=B0=D8=AC</span><svg width=3D"16" height=3D"16" viewBox=3D"0 0 24 24" fi=
ll=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"ro=
und" stroke-linejoin=3D"round" class=3D"text-orange-600"><rect x=3D"3" y=3D=
"6" width=3D"18" height=3D"12" rx=3D"2" ry=3D"2"></rect><line x1=3D"7" y1=
=3D"12" x2=3D"17" y2=3D"12"></line></svg></h4><div class=3D"grid grid-cols-=
2 gap-2"><button class=3D"bg-orange-500 text-white p-3 rounded-lg hover:opa=
city-90 transition-all transform hover:scale-105" title=3D"=D8=A5=D8=B6=D8=
=A7=D9=81=D8=A9 =D8=AD=D9=82=D9=84 =D8=A5=D8=AF=D8=AE=D8=A7=D9=84"><div cla=
ss=3D"mb-1 flex justify-center"><svg width=3D"16" height=3D"16" viewBox=3D"=
0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-=
linecap=3D"round" stroke-linejoin=3D"round" class=3D"text-white"><rect x=3D=
"3" y=3D"6" width=3D"18" height=3D"12" rx=3D"2" ry=3D"2"></rect><line x1=3D=
"7" y1=3D"12" x2=3D"17" y2=3D"12"></line></svg></div><div class=3D"text-xs =
font-medium">=D8=AD=D9=82=D9=84 =D8=A5=D8=AF=D8=AE=D8=A7=D9=84</div></butto=
n><button class=3D"bg-yellow-500 text-white p-3 rounded-lg hover:opacity-90=
 transition-all transform hover:scale-105" title=3D"=D8=A5=D8=B6=D8=A7=D9=
=81=D8=A9 =D9=86=D8=B5 =D8=B7=D9=88=D9=8A=D9=84"><div class=3D"mb-1 flex ju=
stify-center"><svg width=3D"16" height=3D"16" viewBox=3D"0 0 24 24" fill=3D=
"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" =
stroke-linejoin=3D"round" class=3D"text-white"><polyline points=3D"4,7 4,4 =
20,4 20,7"></polyline><line x1=3D"9" y1=3D"20" x2=3D"15" y2=3D"20"></line><=
line x1=3D"12" y1=3D"4" x2=3D"12" y2=3D"20"></line></svg></div><div class=
=3D"text-xs font-medium">=D9=86=D8=B5 =D8=B7=D9=88=D9=8A=D9=84</div></butto=
n><button class=3D"bg-indigo-500 text-white p-3 rounded-lg hover:opacity-90=
 transition-all transform hover:scale-105" title=3D"=D8=A5=D8=B6=D8=A7=D9=
=81=D8=A9 =D9=82=D8=A7=D8=A6=D9=85=D8=A9 =D8=A7=D8=AE=D8=AA=D9=8A=D8=A7=D8=
=B1"><div class=3D"mb-1 flex justify-center"><svg width=3D"16" height=3D"16=
" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=
=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D"text-whi=
te"><rect x=3D"3" y=3D"8" width=3D"18" height=3D"8" rx=3D"2" ry=3D"2"></rec=
t><path d=3D"M7 12h10"></path></svg></div><div class=3D"text-xs font-medium=
">=D9=82=D8=A7=D8=A6=D9=85=D8=A9 =D8=A7=D8=AE=D8=AA=D9=8A=D8=A7=D8=B1</div>=
</button><button class=3D"bg-teal-500 text-white p-3 rounded-lg hover:opaci=
ty-90 transition-all transform hover:scale-105" title=3D"=D8=A5=D8=B6=D8=A7=
=D9=81=D8=A9 =D9=86=D9=85=D9=88=D8=B0=D8=AC"><div class=3D"mb-1 flex justif=
y-center"><svg width=3D"16" height=3D"16" viewBox=3D"0 0 24 24" fill=3D"non=
e" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stro=
ke-linejoin=3D"round" class=3D"text-white"><rect x=3D"3" y=3D"3" width=3D"7=
" height=3D"7"></rect><rect x=3D"14" y=3D"3" width=3D"7" height=3D"7"></rec=
t><rect x=3D"14" y=3D"14" width=3D"7" height=3D"7"></rect><rect x=3D"3" y=
=3D"14" width=3D"7" height=3D"7"></rect></svg></div><div class=3D"text-xs f=
ont-medium">=D9=86=D9=85=D9=88=D8=B0=D8=AC</div></button></div></div><div c=
lass=3D"space-y-2"><h4 class=3D"text-sm font-semibold text-gray-800 text-ri=
ght flex items-center justify-end gap-2"><span>=D8=A8=D9=8A=D8=A7=D9=86=D8=
=A7=D8=AA</span><svg width=3D"16" height=3D"16" viewBox=3D"0 0 24 24" fill=
=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"roun=
d" stroke-linejoin=3D"round" class=3D"text-gray-600"><rect x=3D"3" y=3D"3" =
width=3D"7" height=3D"7"></rect><rect x=3D"14" y=3D"3" width=3D"7" height=
=3D"7"></rect><rect x=3D"14" y=3D"14" width=3D"7" height=3D"7"></rect><rect=
 x=3D"3" y=3D"14" width=3D"7" height=3D"7"></rect></svg></h4><div class=3D"=
grid grid-cols-2 gap-2"><button class=3D"bg-gray-600 text-white p-3 rounded=
-lg hover:opacity-90 transition-all transform hover:scale-105" title=3D"=D8=
=A5=D8=B6=D8=A7=D9=81=D8=A9 =D8=AC=D8=AF=D9=88=D9=84"><div class=3D"mb-1 fl=
ex justify-center"><svg width=3D"16" height=3D"16" viewBox=3D"0 0 24 24" fi=
ll=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"ro=
und" stroke-linejoin=3D"round" class=3D"text-white"><rect x=3D"3" y=3D"3" w=
idth=3D"7" height=3D"7"></rect><rect x=3D"14" y=3D"3" width=3D"7" height=3D=
"7"></rect><rect x=3D"14" y=3D"14" width=3D"7" height=3D"7"></rect><rect x=
=3D"3" y=3D"14" width=3D"7" height=3D"7"></rect></svg></div><div class=3D"t=
ext-xs font-medium">=D8=AC=D8=AF=D9=88=D9=84</div></button></div></div><div=
 class=3D"space-y-2"><h4 class=3D"text-sm font-semibold text-gray-800 text-=
right flex items-center justify-end gap-2"><span>=D8=A3=D8=B4=D9=83=D8=A7=
=D9=84</span><svg width=3D"16" height=3D"16" viewBox=3D"0 0 24 24" fill=3D"=
none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" s=
troke-linejoin=3D"round" class=3D"text-red-600"><line x1=3D"12" y1=3D"5" x2=
=3D"12" y2=3D"19"></line><line x1=3D"5" y1=3D"12" x2=3D"19" y2=3D"12"></lin=
e></svg></h4><div class=3D"grid grid-cols-2 gap-2"><button class=3D"bg-red-=
500 text-white p-3 rounded-lg hover:opacity-90 transition-all transform hov=
er:scale-105" title=3D"=D8=A5=D8=B6=D8=A7=D9=81=D8=A9 =D8=AF=D8=A7=D8=A6=D8=
=B1=D8=A9"><div class=3D"mb-1 flex justify-center"><div class=3D"w-4 h-4 ro=
unded-full bg-white border-2 border-white"></div></div><div class=3D"text-x=
s font-medium">=D8=AF=D8=A7=D8=A6=D8=B1=D8=A9</div></button><button class=
=3D"bg-blue-500 text-white p-3 rounded-lg hover:opacity-90 transition-all t=
ransform hover:scale-105" title=3D"=D8=A5=D8=B6=D8=A7=D9=81=D8=A9 =D9=85=D8=
=B1=D8=A8=D8=B9"><div class=3D"mb-1 flex justify-center"><div class=3D"w-4 =
h-4 bg-white border-2 border-white"></div></div><div class=3D"text-xs font-=
medium">=D9=85=D8=B1=D8=A8=D8=B9</div></button><button class=3D"bg-green-50=
0 text-white p-3 rounded-lg hover:opacity-90 transition-all transform hover=
:scale-105" title=3D"=D8=A5=D8=B6=D8=A7=D9=81=D8=A9 =D8=B3=D9=87=D9=85"><di=
v class=3D"mb-1 flex justify-center"><div class=3D"text-white text-xs">=E2=
=86=92</div></div><div class=3D"text-xs font-medium">=D8=B3=D9=87=D9=85</di=
v></button><button class=3D"bg-purple-500 text-white p-3 rounded-lg hover:o=
pacity-90 transition-all transform hover:scale-105" title=3D"=D8=A5=D8=B6=
=D8=A7=D9=81=D8=A9 =D9=86=D8=AC=D9=85=D8=A9"><div class=3D"mb-1 flex justif=
y-center"><div class=3D"text-white text-xs">=E2=98=85</div></div><div class=
=3D"text-xs font-medium">=D9=86=D8=AC=D9=85=D8=A9</div></button></div></div=
><div style=3D"height:100px"></div></div></div></div><div style=3D"min-heig=
ht:calc(100vh - 80px)" class=3D"jsx-********** flex-1 bg-gradient-to-br fro=
m-gray-50 to-gray-100 relative overflow-hidden p-8"><div style=3D"backgroun=
d-image:linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px), linear-gradi=
ent(90deg, rgba(0,0,0,0.1) 1px, transparent 1px);background-size:20px 20px;=
cursor:default" class=3D"jsx-********** absolute inset-0 opacity-30 canvas-=
background"></div><div style=3D"height:calc(100vh - 80px);cursor:auto;scrol=
l-behavior:smooth;scrollbar-width:auto;-ms-overflow-style:auto" class=3D"js=
x-********** relative w-full overflow-auto editor-scroll"><div style=3D"wid=
th: 2775px; min-height: 1867px; transform: translate(0px, 0px) scale(0.7); =
transform-origin: center center; transition: transform 0.1s ease-out; heigh=
t: 1024px !important; min-width: 375px !important; max-width: 375px !import=
ant;" class=3D"jsx-********** relative"><div style=3D"width:2000px;min-heig=
ht:100vh;padding:50vh 50vw;min-width:max-content" class=3D"jsx-********** f=
lex items-start justify-center gap-8"><div class=3D"jsx-********** flex ite=
ms-start gap-4"><div class=3D"jsx-********** flex flex-col items-center" st=
yle=3D"padding-top: 120px;"><button title=3D"=D8=A5=D8=B6=D8=A7=D9=81=D8=A9=
 =D8=B5=D9=81=D8=AD=D8=A9 =D8=AC=D8=AF=D9=8A=D8=AF=D8=A9" class=3D"jsx-2792=
527110 w-12 h-12 bg-blue-500 hover:bg-blue-600 text-white rounded-full flex=
 items-center justify-center text-xl font-bold transition-all duration-200 =
shadow-lg hover:shadow-xl hover:scale-105 flex-shrink-0">+</button></div><d=
iv class=3D"jsx-********** flex-shrink-0 relative"><div class=3D"jsx-279252=
7110 absolute -top-8 left-0 z-10"><div title=3D"=D8=A7=D9=86=D9=82=D8=B1 =
=D9=84=D9=84=D8=A7=D9=86=D8=AA=D9=82=D8=A7=D9=84 =D8=A5=D9=84=D9=89 =D8=A7=
=D9=84=D8=B5=D9=81=D8=AD=D8=A9 =D9=88=D8=B9=D8=B1=D8=B6 =D8=B7=D8=A8=D9=82=
=D8=A7=D8=AA=D9=87=D8=A7: index (7 =D8=B9=D9=86=D8=B5=D8=B1)" class=3D"jsx-=
********** px-3 py-1 rounded-t-lg text-sm font-medium shadow-md cursor-poin=
ter bg-blue-500 text-white"><div class=3D"jsx-********** flex items-center =
gap-1"><svg width=3D"14" height=3D"14" viewBox=3D"0 0 24 24" fill=3D"none" =
stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"round" stroke-=
linejoin=3D"round" class=3D"text-white"><path d=3D"M14 2H6a2 2 0 0 0-2 2v16=
a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path><polyline points=3D"14,2 14,8 20=
,8"></polyline><line x1=3D"16" y1=3D"13" x2=3D"8" y2=3D"13"></line><line x1=
=3D"16" y1=3D"17" x2=3D"8" y2=3D"17"></line><line x1=3D"10" y1=3D"9" x2=3D"=
8" y2=3D"9"></line></svg><span class=3D"jsx-**********">index (7)</span></d=
iv></div></div><div class=3D"jsx-********** relative bg-white shadow-2xl bo=
rder-4 cursor-pointer transition-all duration-200 overflow-visible border-b=
lue-500 shadow-blue-200" style=3D"width: 375px; height: 667px; background-c=
olor: rgb(255, 255, 255); background-size: cover; background-position: cent=
er center; background-repeat: no-repeat; color: rgb(0, 0, 0);"><div class=
=3D"jsx-********** absolute pointer-events-none opacity-20" style=3D"inset:=
 20px; background-image: linear-gradient(to right, rgb(221, 221, 221) 1px, =
transparent 1px), linear-gradient(rgb(221, 221, 221) 1px, transparent 1px);=
 background-size: 20px 20px;"></div><div class=3D"absolute select-none curs=
or-grab  " draggable=3D"true" title=3D"=D8=A7=D8=B3=D8=AD=D8=A8 =D9=84=D8=
=AA=D8=AD=D8=B1=D9=8A=D9=83 =D8=A7=D9=84=D8=B9=D9=86=D8=B5=D8=B1 =D8=A3=D9=
=88 =D8=A7=D8=B3=D8=AA=D8=AE=D8=AF=D9=85 =D8=A7=D9=84=D9=86=D9=82=D8=A7=D8=
=B7 =D8=A7=D9=84=D8=B2=D8=B1=D9=82=D8=A7=D8=A1 =D9=84=D8=AA=D8=BA=D9=8A=D9=
=8A=D8=B1 =D8=A7=D9=84=D8=AD=D8=AC=D9=85" style=3D"left: 0px; top: 0px; wid=
th: 366px; height: 200px; transform: rotate(0deg); transition: transform 0.=
2s; opacity: 1; transform-origin: center center; z-index: 1;"><div class=3D=
"border border-dashed border-gray-200 rounded bg-transparent relative" styl=
e=3D"width: 100%; height: 100%; display: flex; flex-direction: row; justify=
-content: center; align-items: stretch; gap: 8px; padding: 8px; overflow: h=
idden;"><div class=3D"relative" title=3D"container - 0390" style=3D"width: =
auto; height: auto; min-width: 40px; min-height: 30px; flex: 1 0 auto; posi=
tion: relative; left: auto; top: auto; z-index: 4; display: block; align-it=
ems: initial; justify-content: initial;"><div class=3D"border border-dashed=
 border-blue-300 rounded bg-blue-50/30 relative" style=3D"width: 100%; heig=
ht: 100%; min-height: 60px; display: flex; flex-direction: column; justify-=
content: flex-start; align-items: stretch; gap: 8px; padding: 4px; overflow=
: visible;"><div class=3D"relative" style=3D"width: auto; height: auto; min=
-width: 30px; min-height: 30px; flex-grow: 1; flex-shrink: 0; position: rel=
ative; left: auto; top: auto; z-index: 5; display: block; align-items: init=
ial; justify-content: initial;"><button class=3D"rounded transition-all cur=
sor-default" style=3D"width: 100%; height: 100%; background-color: rgb(59, =
130, 246); color: rgb(255, 255, 255); font-size: 8px; min-height: 24px; bor=
der: none; display: flex; align-items: center; justify-content: center;">=
=D8=B2=D8=B1 =D8=AC=D8=AF=D9=8A=D8=AF</button></div><div class=3D"relative"=
 style=3D"width: auto; height: auto; min-width: 30px; min-height: 30px; fle=
x-grow: 1; flex-shrink: 0; position: relative; left: auto; top: auto; z-ind=
ex: 6; display: block; align-items: initial; justify-content: initial;"><bu=
tton class=3D"rounded transition-all cursor-default" style=3D"width: 100%; =
height: 100%; background-color: rgb(59, 130, 246); color: rgb(255, 255, 255=
); font-size: 8px; min-height: 24px; border: none; display: flex; align-ite=
ms: center; justify-content: center;">=D8=B2=D8=B1 =D8=AC=D8=AF=D9=8A=D8=AF=
</button></div><div class=3D"relative" style=3D"width: auto; height: auto; =
min-width: 30px; min-height: 30px; flex-grow: 1; flex-shrink: 0; position: =
relative; left: auto; top: auto; z-index: 7; display: block; align-items: i=
nitial; justify-content: initial;"><button class=3D"rounded transition-all =
cursor-default" style=3D"width: 100%; height: 100%; background-color: rgb(5=
9, 130, 246); color: rgb(255, 255, 255); font-size: 8px; min-height: 24px; =
border: none; display: flex; align-items: center; justify-content: center;"=
>=D8=B2=D8=B1 =D8=AC=D8=AF=D9=8A=D8=AF</button></div></div></div><div class=
=3D"relative" title=3D"container - 3140" style=3D"width: auto; height: auto=
; min-width: 40px; min-height: 30px; flex: 1 0 auto; position: relative; le=
ft: auto; top: auto; z-index: 8; display: block; align-items: initial; just=
ify-content: initial;"><div class=3D"border border-dashed border-blue-300 r=
ounded bg-blue-50/30 relative" style=3D"width: 100%; height: 100%; min-heig=
ht: 60px; display: flex; flex-direction: column; justify-content: flex-star=
t; align-items: stretch; gap: 8px; padding: 4px; overflow: visible;"><div c=
lass=3D"relative" style=3D"width: auto; height: auto; min-width: 30px; min-=
height: 30px; flex-grow: 1; flex-shrink: 0; position: relative; left: auto;=
 top: auto; z-index: 3; display: block; align-items: initial; justify-conte=
nt: initial;"><div class=3D"border border-gray-300 rounded overflow-hidden =
relative" style=3D"width: 100%; height: 100%; background-color: rgb(248, 24=
9, 250); display: flex; align-items: center; justify-content: center; min-h=
eight: 40px; min-width: 40px;"><img alt=3D"=D8=B5=D9=88=D8=B1=D8=A9" class=
=3D"max-w-full max-h-full" src=3D"https://creativeschoolarabia.com/wp-conte=
nt/uploads/2019/02/Moon-lunar-full-moon-amazing-creative-school-arabia-%D8%=
B5%D9%88%D8%B1%D8%A9-%D9%84%D9%84%D9%82%D9%85%D8%B1-%D8%AA%D8%B5%D9%88%D9%8=
A%D8%B1-%D8%A7%D9%84%D9%82%D9%85%D8%B1-%D8%AA%D8%B5%D9%88%D9%8A%D8%B1-%D9%8=
1%D9%88%D8%AA%D9%88%D8%BA%D8%B1%D8%A7%D9%81%D9%8A-%D8%B5%D9%88%D8%B1%D8%A9-=
%D9%84%D9%84%D9%82%D9%85%D8%B1-%D9%85%D9%83%D9%88%D9%86%D8%A9-%D9%85%D9%86-=
50-%D8%A7%D9%84%D9%81-%D8%B5%D9%88%D8%B1%D8%A9-%D8%AA%D9%85-%D8%AA%D8%AC%D9=
%85%D9%8A%D8%B9%D9%87%D8%A7-%D8%A8%D8%A7%D9%84%D9%81%D9%88%D8%AA%D9%88%D8%B=
4%D9%88%D8%A83-780x470.jpg" style=3D"object-fit: fill; width: 100%; height:=
 100%; max-width: 100%; max-height: 100%;"></div></div></div></div></div></=
div><div title=3D"=D8=A7=D8=B3=D8=AD=D8=A8 =D9=84=D8=AA=D8=BA=D9=8A=D9=8A=
=D8=B1 =D8=A7=D8=B1=D8=AA=D9=81=D8=A7=D8=B9 =D8=A7=D9=84=D8=B5=D9=81=D8=AD=
=D8=A9" class=3D"jsx-********** absolute left-0 right-0 h-4 cursor-ns-resiz=
e bg-transparent hover:bg-blue-100 hover:bg-opacity-70 transition-all durat=
ion-200 group border-t-2 border-dashed border-blue-300 hover:border-blue-50=
0" style=3D"top: 667px;"><div class=3D"jsx-********** absolute left-1/2 top=
-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-500 text-white px-=
3 py-1 rounded-full text-xs font-medium opacity-70 group-hover:opacity-100 =
transition-opacity shadow-md">=E2=87=85 =D8=A7=D8=B3=D8=AD=D8=A8 =D9=84=D8=
=AA=D8=BA=D9=8A=D9=8A=D8=B1 =D8=A7=D9=84=D8=A7=D8=B1=D8=AA=D9=81=D8=A7=D8=
=B9</div></div></div></div></div><div style=3D"padding-top:120px" class=3D"=
jsx-********** flex flex-col items-center"><button title=3D"=D8=A5=D8=B6=D8=
=A7=D9=81=D8=A9 =D8=B5=D9=81=D8=AD=D8=A9 =D8=AC=D8=AF=D9=8A=D8=AF=D8=A9" cl=
ass=3D"jsx-********** w-12 h-12 bg-blue-500 hover:bg-blue-600 text-white ro=
unded-full flex items-center justify-center text-xl font-bold transition-al=
l duration-200 shadow-lg hover:shadow-xl hover:scale-105 flex-shrink-0">+</=
button></div></div></div></div><div class=3D"jsx-********** absolute top-4 =
left-4 z-40"><div class=3D"jsx-********** flex items-start gap-2"><div clas=
s=3D"jsx-********** flex items-center gap-1 bg-gray-300 text-gray-700 px-2 =
py-1 rounded shadow"><svg width=3D"12" height=3D"12" viewBox=3D"0 0 24 24" =
fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"=
round" stroke-linejoin=3D"round" class=3D""><circle cx=3D"11" cy=3D"11" r=
=3D"8"></circle><path d=3D"M21 21l-4.35-4.35"></path></svg><span class=3D"j=
sx-********** text-xs font-medium min-w-[35px]">70<!-- -->%</span><button t=
itle=3D"=D8=AA=D8=B5=D8=BA=D9=8A=D8=B1 (Alt + =D8=AF=D9=88=D9=84=D8=A7=D8=
=A8 =D8=A7=D9=84=D9=85=D8=A7=D9=88=D8=B3)" class=3D"jsx-********** w-4 h-4 =
bg-gray-400 hover:bg-gray-500 text-gray-700 rounded text-xs flex items-cent=
er justify-center transition-colors"><svg width=3D"10" height=3D"10" viewBo=
x=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" st=
roke-linecap=3D"round" stroke-linejoin=3D"round" class=3D""><circle cx=3D"1=
1" cy=3D"11" r=3D"8"></circle><line x1=3D"8" y1=3D"11" x2=3D"14" y2=3D"11">=
</line><path d=3D"M21 21l-4.35-4.35"></path></svg></button><button title=3D=
"=D8=A5=D8=B9=D8=A7=D8=AF=D8=A9 =D8=AA=D8=B9=D9=8A=D9=8A=D9=86 =D8=A7=D9=84=
=D8=B2=D9=88=D9=85 =D8=A5=D9=84=D9=89 100%" class=3D"jsx-********** px-1 h-=
4 bg-gray-400 hover:bg-gray-500 text-gray-700 rounded text-xs flex items-ce=
nter justify-center transition-colors">100%</button><button title=3D"=D8=AA=
=D9=83=D8=A8=D9=8A=D8=B1 (Alt + =D8=AF=D9=88=D9=84=D8=A7=D8=A8 =D8=A7=D9=84=
=D9=85=D8=A7=D9=88=D8=B3)" class=3D"jsx-********** w-4 h-4 bg-gray-400 hove=
r:bg-gray-500 text-gray-700 rounded text-xs flex items-center justify-cente=
r transition-colors"><svg width=3D"10" height=3D"10" viewBox=3D"0 0 24 24" =
fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=3D"=
round" stroke-linejoin=3D"round" class=3D""><circle cx=3D"11" cy=3D"11" r=
=3D"8"></circle><line x1=3D"11" y1=3D"8" x2=3D"11" y2=3D"14"></line><line x=
1=3D"8" y1=3D"11" x2=3D"14" y2=3D"11"></line><path d=3D"M21 21l-4.35-4.35">=
</path></svg></button></div><div class=3D"jsx-********** relative"><button =
title=3D"=D8=B9=D8=B1=D8=B6 =D9=85=D8=B9=D9=84=D9=88=D9=85=D8=A7=D8=AA =D9=
=88=D8=B1=D9=82=D8=A9 =D8=A7=D9=84=D8=B9=D9=85=D9=84" class=3D"jsx-27925271=
10 bg-gray-300 hover:bg-gray-400 text-gray-700 px-2 py-1 rounded shadow tra=
nsition-colors flex items-center gap-1"><svg width=3D"12" height=3D"12" vie=
wBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2"=
 stroke-linecap=3D"round" stroke-linejoin=3D"round" class=3D""><circle cx=
=3D"12" cy=3D"12" r=3D"10"></circle><line x1=3D"12" y1=3D"16" x2=3D"12" y2=
=3D"12"></line><line x1=3D"12" y1=3D"8" x2=3D"12.01" y2=3D"8"></line></svg>=
<span class=3D"jsx-********** text-xs font-medium">=D9=85=D8=B9=D9=84=D9=88=
=D9=85=D8=A7=D8=AA</span></button></div><div class=3D"jsx-********** flex i=
tems-center gap-1"><div class=3D"jsx-********** relative"><button class=3D"=
jsx-********** bg-gray-300 hover:bg-gray-400 text-gray-700 px-2 py-1 rounde=
d text-xs font-medium shadow opacity-75 hover:opacity-100 transition-all fl=
ex items-center gap-1"><svg width=3D"12" height=3D"12" viewBox=3D"0 0 24 24=
" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-linecap=
=3D"round" stroke-linejoin=3D"round" class=3D""><rect x=3D"5" y=3D"2" width=
=3D"14" height=3D"20" rx=3D"2" ry=3D"2"></rect><line x1=3D"12" y1=3D"18" x2=
=3D"12.01" y2=3D"18"></line></svg><span class=3D"jsx-**********">=D9=85=D9=
=88=D8=A8=D8=A7=D9=8A=D9=84</span><svg width=3D"10" height=3D"10" viewBox=
=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" str=
oke-linecap=3D"round" stroke-linejoin=3D"round" class=3D""><polyline points=
=3D"6,9 12,15 18,9"></polyline></svg></button></div><div class=3D"jsx-27925=
27110 flex items-center gap-1 px-2 py-1 rounded text-xs font-mono shadow tr=
ansition-colors bg-gray-300"><span class=3D"jsx-********** text-gray-700">3=
75<!-- -->=C3=97</span><input type=3D"number" min=3D"667" max=3D"3000" titl=
e=3D"=D8=A7=D9=86=D9=82=D8=B1 =D9=84=D8=AA=D8=AD=D8=B1=D9=8A=D8=B1 =D8=A7=
=D9=84=D8=A7=D8=B1=D8=AA=D9=81=D8=A7=D8=B9" class=3D"jsx-********** w-12 bg=
-transparent text-gray-700 text-center text-[10px] font-mono border-none ou=
tline-none focus:bg-white focus:border focus:border-blue-400 focus:rounded =
px-1" value=3D"667"></div></div></div></div></div><div class=3D"w-64 bg-sla=
te-50 border-l border-gray-300 overflow-y-auto flex flex-col transition-all=
 duration-300 ease-in-out" style=3D"height:calc(100vh + 20px);border-bottom=
:none"><div class=3D"p-2 bg-slate-50 border-b border-gray-200 flex-shrink-0=
"><div class=3D"flex items-center justify-between"><h3 class=3D"text-sm fon=
t-bold text-left text-gray-800 flex items-center gap-2"><svg width=3D"16" h=
eight=3D"16" viewBox=3D"0 0 24 24" fill=3D"none" stroke=3D"currentColor" st=
roke-width=3D"2" stroke-linecap=3D"round" stroke-linejoin=3D"round" class=
=3D"text-blue-600"><circle cx=3D"12" cy=3D"12" r=3D"3"></circle><path d=3D"=
M12 1v6m0 6v6m11-7h-6m-6 0H1m17-4a4 4 0 0 0-8 0m8 8a4 4 0 0 0-8 0"></path><=
/svg>=D8=A7=D9=84=D8=AE=D8=B5=D8=A7=D8=A6=D8=B5</h3><button class=3D"w-6 h-=
6 flex items-center justify-center hover:bg-white/50 rounded transition-col=
ors" title=3D"=D8=B7=D9=8A =D9=82=D8=A7=D8=A6=D9=85=D8=A9 =D8=A7=D9=84=D8=
=AE=D8=B5=D8=A7=D8=A6=D8=B5"><svg width=3D"14" height=3D"14" viewBox=3D"0 0=
 24 24" fill=3D"none" stroke=3D"currentColor" stroke-width=3D"2" stroke-lin=
ecap=3D"round" stroke-linejoin=3D"round" class=3D"text-blue-600 transition-=
transform duration-300 rotate-0"><polyline points=3D"6,9 12,15 18,9"></poly=
line></svg></button></div></div><div class=3D"p-2"><p class=3D"text-xs text=
-gray-700 text-right">=D8=A7=D8=AE=D8=AA=D8=B1 =D8=B9=D9=86=D8=B5=D8=B1=D8=
=A7=D9=8B =D8=A3=D9=88 =D8=A7=D9=86=D9=82=D8=B1 =D8=B9=D9=84=D9=89 =D9=85=
=D9=83=D8=A7=D9=86 =D9=81=D8=A7=D8=B1=D8=BA =D9=84=D8=AA=D8=AD=D8=B1=D9=8A=
=D8=B1 =D8=AE=D8=B5=D8=A7=D8=A6=D8=B5 =D8=A7=D9=84=D8=B5=D9=81=D8=AD=D8=A9<=
/p></div></div></div></div><!--$--><!--/$--><title>=D8=AA=D9=85=D8=AF=D8=AF=
=D8=AF=D8=AF=D8=AF - AI Website Builder</title><meta name=3D"description" c=
ontent=3D"Generated by create next app"><next-route-announcer style=3D"posi=
tion: absolute;"><template shadowmode=3D"open"><div aria-live=3D"assertive"=
 id=3D"__next-route-announcer__" role=3D"alert" style=3D"position: absolute=
; border: 0px; height: 1px; margin: -1px; padding: 0px; width: 1px; clip: r=
ect(0px, 0px, 0px, 0px); overflow: hidden; white-space: nowrap; overflow-wr=
ap: normal;"></div></template></next-route-announcer><nextjs-portal><templa=
te shadowmode=3D"open"><div data-nextjs-toast=3D"true" class=3D"nextjs-toas=
t" style=3D"--animate-out-duration-ms: 200ms; --animate-out-timing-function=
: cubic-bezier(0.175, 0.885, 0.32, 1.1); box-shadow: none; z-index: 2147483=
647; bottom: 20px; left: 20px;"><div data-nextjs-toast-wrapper=3D"true"><di=
v data-next-badge-root=3D"true" style=3D"--size: 36px; --duration-short: 15=
0ms; display: block;"><div data-next-badge=3D"true" data-error=3D"false" da=
ta-error-expanded=3D"false" data-animate=3D"false" style=3D"width: 36px;"><=
div><button data-next-mark=3D"true" data-next-mark-loading=3D"false" aria-h=
aspopup=3D"menu" aria-expanded=3D"false" aria-controls=3D"nextjs-dev-tools-=
menu" aria-label=3D"Open Next.js Dev Tools" data-nextjs-dev-tools-button=3D=
"true"><svg width=3D"40" height=3D"40" viewBox=3D"0 0 40 40" fill=3D"none" =
data-next-mark-loading=3D"false"><g transform=3D"translate(8.5, 13)"><path =
class=3D"paused" d=3D"M13.3 15.2 L2.34 1 V12.6" fill=3D"none" stroke=3D"url=
(#next_logo_paint0_linear_1357_10853)" stroke-width=3D"1.86" mask=3D"url(#n=
ext_logo_mask0)" stroke-dasharray=3D"29.6" stroke-dashoffset=3D"29.6"></pat=
h><path class=3D"paused" d=3D"M11.825 1.5 V13.1" stroke-width=3D"1.86" stro=
ke=3D"url(#next_logo_paint1_linear_1357_10853)" stroke-dasharray=3D"11.6" s=
troke-dashoffset=3D"11.6"></path></g><defs><linearGradient id=3D"next_logo_=
paint0_linear_1357_10853" x1=3D"9.95555" y1=3D"11.1226" x2=3D"15.4778" y2=
=3D"17.9671" gradientUnits=3D"userSpaceOnUse"><stop stop-color=3D"white"></=
stop><stop offset=3D"0.604072" stop-color=3D"white" stop-opacity=3D"0"></st=
op><stop offset=3D"1" stop-color=3D"white" stop-opacity=3D"0"></stop></line=
arGradient><linearGradient id=3D"next_logo_paint1_linear_1357_10853" x1=3D"=
11.8222" y1=3D"1.40039" x2=3D"11.791" y2=3D"9.62542" gradientUnits=3D"userS=
paceOnUse"><stop stop-color=3D"white"></stop><stop offset=3D"1" stop-color=
=3D"white" stop-opacity=3D"0"></stop></linearGradient><mask id=3D"next_logo=
_mask0"><rect width=3D"100%" height=3D"100%" fill=3D"white"></rect><rect wi=
dth=3D"5" height=3D"1.5" fill=3D"black"></rect></mask></defs></svg></button=
></div></div><div aria-hidden=3D"true" data-dot=3D"true"></div></div></div>=
</div></template></nextjs-portal></body></html>
------MultipartBoundary--fMhVSzyTwHauVOsjgCwnDvnHSyaD7mUIrpPPqdaxBT----
Content-Type: image/webp
Content-Transfer-Encoding: base64
Content-Location: https://creativeschoolarabia.com/wp-content/uploads/2019/02/Moon-lunar-full-moon-amazing-creative-school-arabia-%D8%B5%D9%88%D8%B1%D8%A9-%D9%84%D9%84%D9%82%D9%85%D8%B1-%D8%AA%D8%B5%D9%88%D9%8A%D8%B1-%D8%A7%D9%84%D9%82%D9%85%D8%B1-%D8%AA%D8%B5%D9%88%D9%8A%D8%B1-%D9%81%D9%88%D8%AA%D9%88%D8%BA%D8%B1%D8%A7%D9%81%D9%8A-%D8%B5%D9%88%D8%B1%D8%A9-%D9%84%D9%84%D9%82%D9%85%D8%B1-%D9%85%D9%83%D9%88%D9%86%D8%A9-%D9%85%D9%86-50-%D8%A7%D9%84%D9%81-%D8%B5%D9%88%D8%B1%D8%A9-%D8%AA%D9%85-%D8%AA%D8%AC%D9%85%D9%8A%D8%B9%D9%87%D8%A7-%D8%A8%D8%A7%D9%84%D9%81%D9%88%D8%AA%D9%88%D8%B4%D9%88%D8%A83-780x470.jpg
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------MultipartBoundary--fMhVSzyTwHauVOsjgCwnDvnHSyaD7mUIrpPPqdaxBT----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: http://localhost:3000/_next/static/chunks/%5Broot-of-the-server%5D__8ebb6d4b._.css

@charset "utf-8";

@font-face { font-family: Geist; font-style: normal; font-weight: 100 900; =
font-display: swap; src: url("../media/gyByhwUxId8gMEwYGFWNOITddY4-s.b7d310=
ad.woff2") format("woff2"); unicode-range: U+301, U+400-45F, U+490-491, U+4=
B0-4B1, U+2116; }

@font-face { font-family: Geist; font-style: normal; font-weight: 100 900; =
font-display: swap; src: url("../media/gyByhwUxId8gMEwSGFWNOITddY4-s.81df3a=
5b.woff2") format("woff2"); unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC,=
 U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF=
2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;=
 }

@font-face { font-family: Geist; font-style: normal; font-weight: 100 900; =
font-display: swap; src: url("../media/gyByhwUxId8gMEwcGFWNOITd-s.p.da1ebef=
7.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2=
BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, =
U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }

@font-face { font-family: "Geist Fallback"; src: local("Arial"); ascent-ove=
rride: 95.94%; descent-override: 28.16%; line-gap-override: 0%; size-adjust=
: 104.76%; }

.geist_e531dabc-module__QGiZLq__className { font-family: Geist, "Geist Fall=
back"; font-style: normal; }

.geist_e531dabc-module__QGiZLq__variable { --font-geist-sans: "Geist", "Gei=
st Fallback"; }

@font-face { font-family: "Geist Mono"; font-style: normal; font-weight: 10=
0 900; font-display: swap; src: url("../media/or3nQ6H_1_WfwkMZI_qYFrMdmhHkj=
kotbA-s.cb6bbcb1.woff2") format("woff2"); unicode-range: U+301, U+400-45F, =
U+490-491, U+4B0-4B1, U+2116; }

@font-face { font-family: "Geist Mono"; font-style: normal; font-weight: 10=
0 900; font-display: swap; src: url("../media/or3nQ6H_1_WfwkMZI_qYFrkdmhHkj=
kotbA-s.e32db976.woff2") format("woff2"); unicode-range: U+100-2BA, U+2BD-2=
C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E=
00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F=
, U+A720-A7FF; }

@font-face { font-family: "Geist Mono"; font-style: normal; font-weight: 10=
0 900; font-display: swap; src: url("../media/or3nQ6H_1_WfwkMZI_qYFrcdmhHkj=
ko-s.p.be19f591.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+15=
2-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+=
20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }

@font-face { font-family: "Geist Mono Fallback"; src: local("Arial"); ascen=
t-override: 74.67%; descent-override: 21.92%; line-gap-override: 0%; size-a=
djust: 134.59%; }

.geist_mono_68a01160-module__YLcDdW__className { font-family: "Geist Mono",=
 "Geist Mono Fallback"; font-style: normal; }

.geist_mono_68a01160-module__YLcDdW__variable { --font-geist-mono: "Geist M=
ono", "Geist Mono Fallback"; }

@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or =
((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
  *, ::before, ::after, ::backdrop { --tw-translate-x: 0; --tw-translate-y:=
 0; --tw-translate-z: 0; --tw-scale-x: 1; --tw-scale-y: 1; --tw-scale-z: 1;=
 --tw-rotate-x: initial; --tw-rotate-y: initial; --tw-rotate-z: initial; --=
tw-skew-x: initial; --tw-skew-y: initial; --tw-space-y-reverse: 0; --tw-bor=
der-style: solid; --tw-gradient-position: initial; --tw-gradient-from: #000=
0; --tw-gradient-via: #0000; --tw-gradient-to: #0000; --tw-gradient-stops: =
initial; --tw-gradient-via-stops: initial; --tw-gradient-from-position: 0%;=
 --tw-gradient-via-position: 50%; --tw-gradient-to-position: 100%; --tw-lea=
ding: initial; --tw-font-weight: initial; --tw-shadow: 0 0 #0000; --tw-shad=
ow-color: initial; --tw-shadow-alpha: 100%; --tw-inset-shadow: 0 0 #0000; -=
-tw-inset-shadow-color: initial; --tw-inset-shadow-alpha: 100%; --tw-ring-c=
olor: initial; --tw-ring-shadow: 0 0 #0000; --tw-inset-ring-color: initial;=
 --tw-inset-ring-shadow: 0 0 #0000; --tw-ring-inset: initial; --tw-ring-off=
set-width: 0px; --tw-ring-offset-color: #fff; --tw-ring-offset-shadow: 0 0 =
#0000; --tw-outline-style: solid; --tw-blur: initial; --tw-brightness: init=
ial; --tw-contrast: initial; --tw-grayscale: initial; --tw-hue-rotate: init=
ial; --tw-invert: initial; --tw-opacity: initial; --tw-saturate: initial; -=
-tw-sepia: initial; --tw-drop-shadow: initial; --tw-drop-shadow-color: init=
ial; --tw-drop-shadow-alpha: 100%; --tw-drop-shadow-size: initial; --tw-bac=
kdrop-blur: initial; --tw-backdrop-brightness: initial; --tw-backdrop-contr=
ast: initial; --tw-backdrop-grayscale: initial; --tw-backdrop-hue-rotate: i=
nitial; --tw-backdrop-invert: initial; --tw-backdrop-opacity: initial; --tw=
-backdrop-saturate: initial; --tw-backdrop-sepia: initial; --tw-duration: i=
nitial; --tw-ease: initial; }
}
}

@layer theme {
  :root, :host { --color-red-100: oklch(93.6% .032 17.717); --color-red-500=
: oklch(63.7% .237 25.331); --color-red-600: oklch(57.7% .245 27.325); --co=
lor-orange-50: oklch(98% .016 73.684); --color-orange-100: oklch(95.4% .038=
 75.164); --color-orange-500: oklch(70.5% .213 47.604); --color-orange-600:=
 oklch(64.6% .222 41.116); --color-orange-800: oklch(47% .157 37.304); --co=
lor-yellow-50: oklch(98.7% .026 102.212); --color-yellow-500: oklch(79.5% .=
184 86.047); --color-yellow-700: oklch(55.4% .135 66.442); --color-yellow-8=
00: oklch(47.6% .114 61.907); --color-green-50: oklch(98.2% .018 155.826); =
--color-green-100: oklch(96.2% .044 156.743); --color-green-300: oklch(87.1=
% .15 154.449); --color-green-400: oklch(79.2% .209 151.711); --color-green=
-500: oklch(72.3% .219 149.579); --color-green-600: oklch(62.7% .194 149.21=
4); --color-green-700: oklch(52.7% .154 150.069); --color-green-800: oklch(=
44.8% .119 151.328); --color-teal-500: oklch(70.4% .14 182.503); --color-cy=
an-500: oklch(71.5% .143 215.221); --color-blue-50: oklch(97% .014 254.604)=
; --color-blue-100: oklch(93.2% .032 255.585); --color-blue-200: oklch(88.2=
% .059 254.128); --color-blue-300: oklch(80.9% .105 251.813); --color-blue-=
400: oklch(70.7% .165 254.624); --color-blue-500: oklch(62.3% .214 259.815)=
; --color-blue-600: oklch(54.6% .245 262.881); --color-blue-700: oklch(48.8=
% .243 264.376); --color-blue-800: oklch(42.4% .199 265.638); --color-indig=
o-100: oklch(93% .034 272.788); --color-indigo-500: oklch(58.5% .233 277.11=
7); --color-purple-50: oklch(97.7% .014 308.299); --color-purple-100: oklch=
(94.6% .033 307.174); --color-purple-200: oklch(90.2% .063 306.703); --colo=
r-purple-500: oklch(62.7% .265 303.9); --color-purple-600: oklch(55.8% .288=
 302.321); --color-purple-700: oklch(49.6% .265 301.924); --color-purple-80=
0: oklch(43.8% .218 303.724); --color-pink-500: oklch(65.6% .241 354.308); =
--color-slate-50: oklch(98.4% .003 247.858); --color-gray-50: oklch(98.5% .=
002 247.839); --color-gray-100: oklch(96.7% .003 264.542); --color-gray-200=
: oklch(92.8% .006 264.531); --color-gray-300: oklch(87.2% .01 258.338); --=
color-gray-400: oklch(70.7% .022 261.325); --color-gray-500: oklch(55.1% .0=
27 264.364); --color-gray-600: oklch(44.6% .03 256.802); --color-gray-700: =
oklch(37.3% .034 259.733); --color-gray-800: oklch(27.8% .033 256.848); --c=
olor-gray-900: oklch(21% .034 264.665); --color-black: #000; --color-white:=
 #fff; --spacing: .25rem; --container-md: 28rem; --container-2xl: 42rem; --=
container-4xl: 56rem; --container-6xl: 72rem; --text-xs: .75rem; --text-xs-=
-line-height: calc(1 / .75); --text-sm: .875rem; --text-sm--line-height: ca=
lc(1.25 / .875); --text-lg: 1.125rem; --text-lg--line-height: calc(1.75 / 1=
.125); --text-xl: 1.25rem; --text-xl--line-height: calc(1.75 / 1.25); --tex=
t-2xl: 1.5rem; --text-2xl--line-height: calc(2 / 1.5); --text-3xl: 1.875rem=
; --text-3xl--line-height: calc(2.25 / 1.875); --text-4xl: 2.25rem; --text-=
4xl--line-height: calc(2.5 / 2.25); --text-5xl: 3rem; --text-5xl--line-heig=
ht: 1; --font-weight-medium: 500; --font-weight-semibold: 600; --font-weigh=
t-bold: 700; --leading-relaxed: 1.625; --radius-lg: .5rem; --ease-in: cubic=
-bezier(.4, 0, 1, 1); --ease-out: cubic-bezier(0, 0, .2, 1); --ease-in-out:=
 cubic-bezier(.4, 0, .2, 1); --animate-spin: spin 1s linear infinite; --ani=
mate-pulse: pulse 2s cubic-bezier(.4, 0, .6, 1) infinite; --blur-sm: 8px; -=
-default-transition-duration: .15s; --default-transition-timing-function: c=
ubic-bezier(.4, 0, .2, 1); --default-font-family: var(--font-geist-sans); -=
-default-mono-font-family: var(--font-geist-mono); }
}

@layer base {
  *, ::after, ::before, ::backdrop, ::file-selector-button { box-sizing: bo=
rder-box; border: 0px solid; margin: 0px; padding: 0px; }
  html, :host { text-size-adjust: 100%; tab-size: 4; line-height: 1.5; font=
-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "=
Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"=
); font-feature-settings: var(--default-font-feature-settings, normal); fon=
t-variation-settings: var(--default-font-variation-settings, normal); -webk=
it-tap-highlight-color: transparent; }
  hr { height: 0px; color: inherit; border-top-width: 1px; }
  abbr:where([title]) { text-decoration: underline dotted; }
  h1, h2, h3, h4, h5, h6 { font-size: inherit; font-weight: inherit; }
  a { color: inherit; text-decoration: inherit; }
  b, strong { font-weight: bolder; }
  code, kbd, samp, pre { font-family: var(--default-mono-font-family, ui-mo=
nospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Couri=
er New", monospace); font-feature-settings: var(--default-mono-font-feature=
-settings, normal); font-variation-settings: var(--default-mono-font-variat=
ion-settings, normal); font-size: 1em; }
  small { font-size: 80%; }
  sub, sup { vertical-align: baseline; font-size: 75%; line-height: 0; posi=
tion: relative; }
  sub { bottom: -0.25em; }
  sup { top: -0.5em; }
  table { text-indent: 0px; border-color: inherit; border-collapse: collaps=
e; }
  progress { vertical-align: baseline; }
  summary { display: list-item; }
  ol, ul, menu { list-style: none; }
  img, svg, video, canvas, audio, iframe, embed, object { vertical-align: m=
iddle; display: block; }
  img, video { max-width: 100%; height: auto; }
  button, input, select, optgroup, textarea, ::file-selector-button { font:=
 inherit; letter-spacing: inherit; color: inherit; opacity: 1; background-c=
olor: rgba(0, 0, 0, 0); border-radius: 0px; }
  :where(select:is([multiple], [size])) optgroup { font-weight: bolder; }
  :where(select:is([multiple], [size])) optgroup option { padding-inline-st=
art: 20px; }
  ::file-selector-button { margin-inline-end: 4px; }
  ::placeholder { opacity: 1; }
  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-int=
rinsic-size: 1px) {
  ::placeholder { color: currentcolor; }
  @supports (color: color-mix(in lab, red, red)) {
  ::placeholder { color: color-mix(in oklab, currentcolor, transparent); }
}
}
  textarea { resize: vertical; }
  ::-webkit-search-decoration { appearance: none; }
  ::-webkit-date-and-time-value { min-height: 1lh; text-align: inherit; }
  ::-webkit-datetime-edit { display: inline-flex; }
  ::-webkit-datetime-edit-fields-wrapper { padding: 0px; }
  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-da=
tetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datet=
ime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-dateti=
me-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-=
datetime-edit-meridiem-field { padding-block: 0px; }
  button, input:where([type=3D"button"], [type=3D"reset"], [type=3D"submit"=
]), ::file-selector-button { appearance: button; }
  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button { height: auto; =
}
  [hidden]:where(:not([hidden=3D"until-found"])) { display: none !important=
; }
}

@layer components;

@layer utilities {
  .pointer-events-none { pointer-events: none; }
  .visible { visibility: visible; }
  .absolute { position: absolute; }
  .fixed { position: fixed; }
  .relative { position: relative; }
  .static { position: static; }
  .sticky { position: sticky; }
  .inset-0 { inset: calc(var(--spacing) * 0); }
  .-top-1 { top: calc(var(--spacing) * -1); }
  .-top-2 { top: calc(var(--spacing) * -2); }
  .-top-6 { top: calc(var(--spacing) * -6); }
  .-top-8 { top: calc(var(--spacing) * -8); }
  .-top-12 { top: calc(var(--spacing) * -12); }
  .top-1\/2 { top: 50%; }
  .top-4 { top: calc(var(--spacing) * 4); }
  .top-full { top: 100%; }
  .-right-1 { right: calc(var(--spacing) * -1); }
  .-right-2 { right: calc(var(--spacing) * -2); }
  .right-0 { right: calc(var(--spacing) * 0); }
  .right-4 { right: calc(var(--spacing) * 4); }
  .-bottom-1 { bottom: calc(var(--spacing) * -1); }
  .bottom-4 { bottom: calc(var(--spacing) * 4); }
  .-left-1 { left: calc(var(--spacing) * -1); }
  .left-0 { left: calc(var(--spacing) * 0); }
  .left-1\/2 { left: 50%; }
  .left-2 { left: calc(var(--spacing) * 2); }
  .left-4 { left: calc(var(--spacing) * 4); }
  .isolate { isolation: isolate; }
  .z-10 { z-index: 10; }
  .z-20 { z-index: 20; }
  .z-40 { z-index: 40; }
  .z-50 { z-index: 50; }
  .container { width: 100%; }
  @media (width >=3D 40rem) {
  .container { max-width: 40rem; }
}
  @media (width >=3D 48rem) {
  .container { max-width: 48rem; }
}
  @media (width >=3D 64rem) {
  .container { max-width: 64rem; }
}
  @media (width >=3D 80rem) {
  .container { max-width: 80rem; }
}
  @media (width >=3D 96rem) {
  .container { max-width: 96rem; }
}
  .m-4 { margin: calc(var(--spacing) * 4); }
  .mx-4 { margin-inline: calc(var(--spacing) * 4); }
  .mx-auto { margin-inline: auto; }
  .my-1 { margin-block: calc(var(--spacing) * 1); }
  .mt-1 { margin-top: calc(var(--spacing) * 1); }
  .mt-2 { margin-top: calc(var(--spacing) * 2); }
  .mt-3 { margin-top: calc(var(--spacing) * 3); }
  .mt-4 { margin-top: calc(var(--spacing) * 4); }
  .mt-6 { margin-top: calc(var(--spacing) * 6); }
  .mt-20 { margin-top: calc(var(--spacing) * 20); }
  .mr-1 { margin-right: calc(var(--spacing) * 1); }
  .mr-2 { margin-right: calc(var(--spacing) * 2); }
  .mb-1 { margin-bottom: calc(var(--spacing) * 1); }
  .mb-2 { margin-bottom: calc(var(--spacing) * 2); }
  .mb-3 { margin-bottom: calc(var(--spacing) * 3); }
  .mb-4 { margin-bottom: calc(var(--spacing) * 4); }
  .mb-6 { margin-bottom: calc(var(--spacing) * 6); }
  .mb-8 { margin-bottom: calc(var(--spacing) * 8); }
  .mb-12 { margin-bottom: calc(var(--spacing) * 12); }
  .mb-16 { margin-bottom: calc(var(--spacing) * 16); }
  .ml-1 { margin-left: calc(var(--spacing) * 1); }
  .block { display: block; }
  .flex { display: flex; }
  .grid { display: grid; }
  .hidden { display: none; }
  .inline { display: inline; }
  .inline-block { display: inline-block; }
  .table { display: table; }
  .h-0\.5 { height: calc(var(--spacing) * .5); }
  .h-3 { height: calc(var(--spacing) * 3); }
  .h-4 { height: calc(var(--spacing) * 4); }
  .h-6 { height: calc(var(--spacing) * 6); }
  .h-8 { height: calc(var(--spacing) * 8); }
  .h-12 { height: calc(var(--spacing) * 12); }
  .h-16 { height: calc(var(--spacing) * 16); }
  .h-full { height: 100%; }
  .h-screen { height: 100vh; }
  .max-h-32 { max-height: calc(var(--spacing) * 32); }
  .max-h-48 { max-height: calc(var(--spacing) * 48); }
  .max-h-96 { max-height: calc(var(--spacing) * 96); }
  .max-h-\[70vh\] { max-height: 70vh; }
  .max-h-\[90vh\] { max-height: 90vh; }
  .max-h-\[300px\] { max-height: 300px; }
  .max-h-full { max-height: 100%; }
  .min-h-\[280px\] { min-height: 280px; }
  .min-h-\[300px\] { min-height: 300px; }
  .min-h-screen { min-height: 100vh; }
  .w-1\/3 { width: 33.3333%; }
  .w-3 { width: calc(var(--spacing) * 3); }
  .w-4 { width: calc(var(--spacing) * 4); }
  .w-6 { width: calc(var(--spacing) * 6); }
  .w-10 { width: calc(var(--spacing) * 10); }
  .w-12 { width: calc(var(--spacing) * 12); }
  .w-16 { width: calc(var(--spacing) * 16); }
  .w-48 { width: calc(var(--spacing) * 48); }
  .w-64 { width: calc(var(--spacing) * 64); }
  .w-72 { width: calc(var(--spacing) * 72); }
  .w-80 { width: calc(var(--spacing) * 80); }
  .w-96 { width: calc(var(--spacing) * 96); }
  .w-full { width: 100%; }
  .w-screen { width: 100vw; }
  .max-w-2xl { max-width: var(--container-2xl); }
  .max-w-4xl { max-width: var(--container-4xl); }
  .max-w-6xl { max-width: var(--container-6xl); }
  .max-w-\[90vw\] { max-width: 90vw; }
  .max-w-full { max-width: 100%; }
  .max-w-md { max-width: var(--container-md); }
  .min-w-0 { min-width: calc(var(--spacing) * 0); }
  .min-w-48 { min-width: calc(var(--spacing) * 48); }
  .min-w-\[35px\] { min-width: 35px; }
  .min-w-\[280px\] { min-width: 280px; }
  .flex-1 { flex: 1 1 0%; }
  .flex-shrink { flex-shrink: 1; }
  .flex-shrink-0 { flex-shrink: 0; }
  .flex-grow { flex-grow: 1; }
  .border-collapse { border-collapse: collapse; }
  .-translate-x-1\/2 { --tw-translate-x: calc(calc(1 / 2 * 100%) * -1); tra=
nslate: var(--tw-translate-x) var(--tw-translate-y); }
  .-translate-y-1\/2 { --tw-translate-y: calc(calc(1 / 2 * 100%) * -1); tra=
nslate: var(--tw-translate-x) var(--tw-translate-y); }
  .scale-95 { --tw-scale-x: 95%; --tw-scale-y: 95%; --tw-scale-z: 95%; scal=
e: var(--tw-scale-x) var(--tw-scale-y); }
  .-rotate-90 { rotate: -90deg; }
  .rotate-0 { rotate: none; }
  .rotate-90 { rotate: 90deg; }
  .transform { transform: var(--tw-rotate-x, ) var(--tw-rotate-y, ) var(--t=
w-rotate-z, ) var(--tw-skew-x, ) var(--tw-skew-y, ); }
  .animate-pulse { animation: var(--animate-pulse); }
  .animate-spin { animation: var(--animate-spin); }
  .cursor-default { cursor: default; }
  .cursor-grab { cursor: grab; }
  .cursor-grabbing { cursor: grabbing; }
  .cursor-move { cursor: move; }
  .cursor-ne-resize { cursor: ne-resize; }
  .cursor-not-allowed { cursor: not-allowed; }
  .cursor-ns-resize { cursor: ns-resize; }
  .cursor-nw-resize { cursor: nw-resize; }
  .cursor-pointer { cursor: pointer; }
  .cursor-se-resize { cursor: se-resize; }
  .cursor-sw-resize { cursor: sw-resize; }
  .resize { resize: both; }
  .resize-none { resize: none; }
  .list-inside { list-style-position: inside; }
  .list-disc { list-style-type: disc; }
  .grid-cols-1 { grid-template-columns: repeat(1, minmax(0px, 1fr)); }
  .grid-cols-2 { grid-template-columns: repeat(2, minmax(0px, 1fr)); }
  .grid-cols-3 { grid-template-columns: repeat(3, minmax(0px, 1fr)); }
  .flex-col { flex-direction: column; }
  .flex-row { flex-direction: row; }
  .flex-wrap { flex-wrap: wrap; }
  .items-center { align-items: center; }
  .items-start { align-items: flex-start; }
  .justify-between { justify-content: space-between; }
  .justify-center { justify-content: center; }
  .justify-end { justify-content: flex-end; }
  .justify-start { justify-content: flex-start; }
  .justify-stretch { justify-content: stretch; }
  .gap-0\.5 { gap: calc(var(--spacing) * .5); }
  .gap-1 { gap: calc(var(--spacing) * 1); }
  .gap-2 { gap: calc(var(--spacing) * 2); }
  .gap-3 { gap: calc(var(--spacing) * 3); }
  .gap-4 { gap: calc(var(--spacing) * 4); }
  .gap-6 { gap: calc(var(--spacing) * 6); }
  .gap-8 { gap: calc(var(--spacing) * 8); }
  :where(.space-y-1 > :not(:last-child)) { --tw-space-y-reverse: 0; margin-=
block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse)); ma=
rgin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-r=
everse))); }
  :where(.space-y-2 > :not(:last-child)) { --tw-space-y-reverse: 0; margin-=
block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse)); ma=
rgin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-r=
everse))); }
  :where(.space-y-3 > :not(:last-child)) { --tw-space-y-reverse: 0; margin-=
block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse)); ma=
rgin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-r=
everse))); }
  :where(.space-y-4 > :not(:last-child)) { --tw-space-y-reverse: 0; margin-=
block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse)); ma=
rgin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-r=
everse))); }
  :where(.space-y-6 > :not(:last-child)) { --tw-space-y-reverse: 0; margin-=
block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse)); ma=
rgin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-r=
everse))); }
  .truncate { text-overflow: ellipsis; white-space: nowrap; overflow: hidde=
n; }
  .overflow-auto { overflow: auto; }
  .overflow-hidden { overflow: hidden; }
  .overflow-visible { overflow: visible; }
  .overflow-y-auto { overflow-y: auto; }
  .rounded { border-radius: 0.25rem; }
  .rounded-full { border-radius: 3.40282e+38px; }
  .rounded-lg { border-radius: var(--radius-lg); }
  .rounded-t { border-top-left-radius: 0.25rem; border-top-right-radius: 0.=
25rem; }
  .rounded-t-lg { border-top-left-radius: var(--radius-lg); border-top-righ=
t-radius: var(--radius-lg); }
  .border { border-style: var(--tw-border-style); border-width: 1px; }
  .border-0 { border-style: var(--tw-border-style); border-width: 0px; }
  .border-1 { border-style: var(--tw-border-style); border-width: 1px; }
  .border-2 { border-style: var(--tw-border-style); border-width: 2px; }
  .border-4 { border-style: var(--tw-border-style); border-width: 4px; }
  .border-t { border-top-style: var(--tw-border-style); border-top-width: 1=
px; }
  .border-t-2 { border-top-style: var(--tw-border-style); border-top-width:=
 2px; }
  .border-r { border-right-style: var(--tw-border-style); border-right-widt=
h: 1px; }
  .border-b { border-bottom-style: var(--tw-border-style); border-bottom-wi=
dth: 1px; }
  .border-b-2 { border-bottom-style: var(--tw-border-style); border-bottom-=
width: 2px; }
  .border-l { border-left-style: var(--tw-border-style); border-left-width:=
 1px; }
  .border-l-2 { border-left-style: var(--tw-border-style); border-left-widt=
h: 2px; }
  .border-dashed { --tw-border-style: dashed; border-style: dashed; }
  .border-none { --tw-border-style: none; border-style: none; }
  .border-blue-200 { border-color: var(--color-blue-200); }
  .border-blue-300 { border-color: var(--color-blue-300); }
  .border-blue-400 { border-color: var(--color-blue-400); }
  .border-blue-500 { border-color: var(--color-blue-500); }
  .border-gray-100 { border-color: var(--color-gray-100); }
  .border-gray-200 { border-color: var(--color-gray-200); }
  .border-gray-300 { border-color: var(--color-gray-300); }
  .border-gray-600 { border-color: var(--color-gray-600); }
  .border-green-300 { border-color: var(--color-green-300); }
  .border-green-400 { border-color: var(--color-green-400); }
  .border-purple-200 { border-color: var(--color-purple-200); }
  .border-white { border-color: var(--color-white); }
  .border-white\/20 { border-color: rgba(255, 255, 255, 0.2); }
  @supports (color: color-mix(in lab, red, red)) {
  .border-white\/20 { border-color: color-mix(in oklab, var(--color-white) =
20%, transparent); }
}
  .bg-black\/50 { background-color: rgba(0, 0, 0, 0.5); }
  @supports (color: color-mix(in lab, red, red)) {
  .bg-black\/50 { background-color: color-mix(in oklab, var(--color-black) =
50%, transparent); }
}
  .bg-blue-50 { background-color: var(--color-blue-50); }
  .bg-blue-50\/30 { background-color: rgba(239, 246, 255, 0.3); }
  @supports (color: color-mix(in lab, red, red)) {
  .bg-blue-50\/30 { background-color: color-mix(in oklab, var(--color-blue-=
50) 30%, transparent); }
}
  .bg-blue-100 { background-color: var(--color-blue-100); }
  .bg-blue-200 { background-color: var(--color-blue-200); }
  .bg-blue-400 { background-color: var(--color-blue-400); }
  .bg-blue-500 { background-color: var(--color-blue-500); }
  .bg-blue-500\/70 { background-color: rgba(48, 128, 255, 0.7); }
  @supports (color: color-mix(in lab, red, red)) {
  .bg-blue-500\/70 { background-color: color-mix(in oklab, var(--color-blue=
-500) 70%, transparent); }
}
  .bg-blue-600 { background-color: var(--color-blue-600); }
  .bg-current { background-color: currentcolor; }
  .bg-cyan-500 { background-color: var(--color-cyan-500); }
  .bg-gray-50 { background-color: var(--color-gray-50); }
  .bg-gray-50\/30 { background-color: rgba(249, 250, 251, 0.3); }
  @supports (color: color-mix(in lab, red, red)) {
  .bg-gray-50\/30 { background-color: color-mix(in oklab, var(--color-gray-=
50) 30%, transparent); }
}
  .bg-gray-100 { background-color: var(--color-gray-100); }
  .bg-gray-200 { background-color: var(--color-gray-200); }
  .bg-gray-300 { background-color: var(--color-gray-300); }
  .bg-gray-400 { background-color: var(--color-gray-400); }
  .bg-gray-500 { background-color: var(--color-gray-500); }
  .bg-gray-600 { background-color: var(--color-gray-600); }
  .bg-gray-800 { background-color: var(--color-gray-800); }
  .bg-gray-900 { background-color: var(--color-gray-900); }
  .bg-green-50 { background-color: var(--color-green-50); }
  .bg-green-100 { background-color: var(--color-green-100); }
  .bg-green-500 { background-color: var(--color-green-500); }
  .bg-green-500\/70 { background-color: rgba(0, 199, 88, 0.7); }
  @supports (color: color-mix(in lab, red, red)) {
  .bg-green-500\/70 { background-color: color-mix(in oklab, var(--color-gre=
en-500) 70%, transparent); }
}
  .bg-indigo-500 { background-color: var(--color-indigo-500); }
  .bg-orange-50 { background-color: var(--color-orange-50); }
  .bg-orange-100 { background-color: var(--color-orange-100); }
  .bg-orange-500 { background-color: var(--color-orange-500); }
  .bg-pink-500 { background-color: var(--color-pink-500); }
  .bg-purple-50 { background-color: var(--color-purple-50); }
  .bg-purple-100 { background-color: var(--color-purple-100); }
  .bg-purple-500 { background-color: var(--color-purple-500); }
  .bg-red-500 { background-color: var(--color-red-500); }
  .bg-slate-50 { background-color: var(--color-slate-50); }
  .bg-teal-500 { background-color: var(--color-teal-500); }
  .bg-transparent { background-color: rgba(0, 0, 0, 0); }
  .bg-white { background-color: var(--color-white); }
  .bg-white\/10 { background-color: rgba(255, 255, 255, 0.1); }
  @supports (color: color-mix(in lab, red, red)) {
  .bg-white\/10 { background-color: color-mix(in oklab, var(--color-white) =
10%, transparent); }
}
  .bg-white\/20 { background-color: rgba(255, 255, 255, 0.2); }
  @supports (color: color-mix(in lab, red, red)) {
  .bg-white\/20 { background-color: color-mix(in oklab, var(--color-white) =
20%, transparent); }
}
  .bg-white\/95 { background-color: rgba(255, 255, 255, 0.95); }
  @supports (color: color-mix(in lab, red, red)) {
  .bg-white\/95 { background-color: color-mix(in oklab, var(--color-white) =
95%, transparent); }
}
  .bg-yellow-50 { background-color: var(--color-yellow-50); }
  .bg-yellow-500 { background-color: var(--color-yellow-500); }
  .bg-gradient-to-br { --tw-gradient-position: to bottom right in oklab; ba=
ckground-image: linear-gradient(var(--tw-gradient-stops)); }
  .bg-gradient-to-r { --tw-gradient-position: to right in oklab; background=
-image: linear-gradient(var(--tw-gradient-stops)); }
  .from-blue-50 { --tw-gradient-from: var(--color-blue-50); --tw-gradient-s=
tops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gr=
adient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--=
tw-gradient-to-position)); }
  .from-blue-600 { --tw-gradient-from: var(--color-blue-600); --tw-gradient=
-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-=
gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(=
--tw-gradient-to-position)); }
  .from-gray-50 { --tw-gradient-from: var(--color-gray-50); --tw-gradient-s=
tops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gr=
adient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--=
tw-gradient-to-position)); }
  .to-gray-100 { --tw-gradient-to: var(--color-gray-100); --tw-gradient-sto=
ps: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-grad=
ient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw=
-gradient-to-position)); }
  .to-indigo-100 { --tw-gradient-to: var(--color-indigo-100); --tw-gradient=
-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-=
gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(=
--tw-gradient-to-position)); }
  .to-purple-50 { --tw-gradient-to: var(--color-purple-50); --tw-gradient-s=
tops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gr=
adient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--=
tw-gradient-to-position)); }
  .to-purple-600 { --tw-gradient-to: var(--color-purple-600); --tw-gradient=
-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-=
gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(=
--tw-gradient-to-position)); }
  .p-0\.5 { padding: calc(var(--spacing) * .5); }
  .p-1 { padding: calc(var(--spacing) * 1); }
  .p-2 { padding: calc(var(--spacing) * 2); }
  .p-3 { padding: calc(var(--spacing) * 3); }
  .p-4 { padding: calc(var(--spacing) * 4); }
  .p-6 { padding: calc(var(--spacing) * 6); }
  .p-8 { padding: calc(var(--spacing) * 8); }
  .px-1 { padding-inline: calc(var(--spacing) * 1); }
  .px-2 { padding-inline: calc(var(--spacing) * 2); }
  .px-3 { padding-inline: calc(var(--spacing) * 3); }
  .px-4 { padding-inline: calc(var(--spacing) * 4); }
  .px-6 { padding-inline: calc(var(--spacing) * 6); }
  .px-8 { padding-inline: calc(var(--spacing) * 8); }
  .py-1 { padding-block: calc(var(--spacing) * 1); }
  .py-2 { padding-block: calc(var(--spacing) * 2); }
  .py-4 { padding-block: calc(var(--spacing) * 4); }
  .py-16 { padding-block: calc(var(--spacing) * 16); }
  .pt-2 { padding-top: calc(var(--spacing) * 2); }
  .pt-3 { padding-top: calc(var(--spacing) * 3); }
  .pt-4 { padding-top: calc(var(--spacing) * 4); }
  .pr-3 { padding-right: calc(var(--spacing) * 3); }
  .text-center { text-align: center; }
  .text-left { text-align: left; }
  .text-right { text-align: right; }
  .font-mono { font-family: var(--font-geist-mono); }
  .text-2xl { font-size: var(--text-2xl); line-height: var(--tw-leading, va=
r(--text-2xl--line-height)); }
  .text-3xl { font-size: var(--text-3xl); line-height: var(--tw-leading, va=
r(--text-3xl--line-height)); }
  .text-4xl { font-size: var(--text-4xl); line-height: var(--tw-leading, va=
r(--text-4xl--line-height)); }
  .text-5xl { font-size: var(--text-5xl); line-height: var(--tw-leading, va=
r(--text-5xl--line-height)); }
  .text-lg { font-size: var(--text-lg); line-height: var(--tw-leading, var(=
--text-lg--line-height)); }
  .text-sm { font-size: var(--text-sm); line-height: var(--tw-leading, var(=
--text-sm--line-height)); }
  .text-xl { font-size: var(--text-xl); line-height: var(--tw-leading, var(=
--text-xl--line-height)); }
  .text-xs { font-size: var(--text-xs); line-height: var(--tw-leading, var(=
--text-xs--line-height)); }
  .text-\[10px\] { font-size: 10px; }
  .leading-relaxed { --tw-leading: var(--leading-relaxed); line-height: var=
(--leading-relaxed); }
  .font-bold { --tw-font-weight: var(--font-weight-bold); font-weight: var(=
--font-weight-bold); }
  .font-medium { --tw-font-weight: var(--font-weight-medium); font-weight: =
var(--font-weight-medium); }
  .font-semibold { --tw-font-weight: var(--font-weight-semibold); font-weig=
ht: var(--font-weight-semibold); }
  .whitespace-nowrap { white-space: nowrap; }
  .whitespace-pre-wrap { white-space: pre-wrap; }
  .text-blue-400 { color: var(--color-blue-400); }
  .text-blue-600 { color: var(--color-blue-600); }
  .text-blue-700 { color: var(--color-blue-700); }
  .text-blue-800 { color: var(--color-blue-800); }
  .text-gray-300 { color: var(--color-gray-300); }
  .text-gray-400 { color: var(--color-gray-400); }
  .text-gray-500 { color: var(--color-gray-500); }
  .text-gray-600 { color: var(--color-gray-600); }
  .text-gray-700 { color: var(--color-gray-700); }
  .text-gray-800 { color: var(--color-gray-800); }
  .text-gray-900 { color: var(--color-gray-900); }
  .text-green-400 { color: var(--color-green-400); }
  .text-green-600 { color: var(--color-green-600); }
  .text-green-700 { color: var(--color-green-700); }
  .text-green-800 { color: var(--color-green-800); }
  .text-orange-600 { color: var(--color-orange-600); }
  .text-orange-800 { color: var(--color-orange-800); }
  .text-purple-600 { color: var(--color-purple-600); }
  .text-purple-700 { color: var(--color-purple-700); }
  .text-purple-800 { color: var(--color-purple-800); }
  .text-red-500 { color: var(--color-red-500); }
  .text-red-600 { color: var(--color-red-600); }
  .text-white { color: var(--color-white); }
  .text-yellow-700 { color: var(--color-yellow-700); }
  .text-yellow-800 { color: var(--color-yellow-800); }
  .antialiased { -webkit-font-smoothing: antialiased; }
  .placeholder-gray-400::placeholder { color: var(--color-gray-400); }
  .placeholder-white\/70::placeholder { color: rgba(255, 255, 255, 0.7); }
  @supports (color: color-mix(in lab, red, red)) {
  .placeholder-white\/70::placeholder { color: color-mix(in oklab, var(--co=
lor-white) 70%, transparent); }
}
  .opacity-20 { opacity: 0.2; }
  .opacity-30 { opacity: 0.3; }
  .opacity-50 { opacity: 0.5; }
  .opacity-60 { opacity: 0.6; }
  .opacity-70 { opacity: 0.7; }
  .opacity-75 { opacity: 0.75; }
  .shadow { --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, #0000001a), 0 1=
px 2px -1px var(--tw-shadow-color, #0000001a); box-shadow: var(--tw-inset-s=
hadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw=
-ring-shadow), var(--tw-shadow); }
  .shadow-2xl { --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, #0000=
0040); box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var=
(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow); }
  .shadow-lg { --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #000000=
1a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a); box-shadow: var(--tw=
-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), =
var(--tw-ring-shadow), var(--tw-shadow); }
  .shadow-md { --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, #0000001a=
), 0 2px 4px -2px var(--tw-shadow-color, #0000001a); box-shadow: var(--tw-i=
nset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), va=
r(--tw-ring-shadow), var(--tw-shadow); }
  .shadow-xl { --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, #000000=
1a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a); box-shadow: var(--t=
w-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow),=
 var(--tw-ring-shadow), var(--tw-shadow); }
  .ring { --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(1px + var(--t=
w-ring-offset-width)) var(--tw-ring-color, currentcolor); box-shadow: var(-=
-tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow=
), var(--tw-ring-shadow), var(--tw-shadow); }
  .ring-2 { --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(2px + var(-=
-tw-ring-offset-width)) var(--tw-ring-color, currentcolor); box-shadow: var=
(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shad=
ow), var(--tw-ring-shadow), var(--tw-shadow); }
  .ring-4 { --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 calc(4px + var(-=
-tw-ring-offset-width)) var(--tw-ring-color, currentcolor); box-shadow: var=
(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shad=
ow), var(--tw-ring-shadow), var(--tw-shadow); }
  .shadow-blue-200 { --tw-shadow-color: oklch(88.2% .059 254.128); }
  @supports (color: color-mix(in lab, red, red)) {
  .shadow-blue-200 { --tw-shadow-color: color-mix(in oklab, var(--color-blu=
e-200) var(--tw-shadow-alpha), transparent); }
}
  .ring-blue-500 { --tw-ring-color: var(--color-blue-500); }
  .ring-green-400 { --tw-ring-color: var(--color-green-400); }
  .outline { outline-style: var(--tw-outline-style); outline-width: 1px; }
  .blur { --tw-blur: blur(8px); filter: var(--tw-blur, ) var(--tw-brightnes=
s, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(=
--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow,=
 ); }
  .grayscale { --tw-grayscale: grayscale(100%); filter: var(--tw-blur, ) va=
r(--tw-brightness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hu=
e-rotate, ) var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(-=
-tw-drop-shadow, ); }
  .sepia { --tw-sepia: sepia(100%); filter: var(--tw-blur, ) var(--tw-brigh=
tness, ) var(--tw-contrast, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) =
var(--tw-invert, ) var(--tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-sha=
dow, ); }
  .filter { filter: var(--tw-blur, ) var(--tw-brightness, ) var(--tw-contra=
st, ) var(--tw-grayscale, ) var(--tw-hue-rotate, ) var(--tw-invert, ) var(-=
-tw-saturate, ) var(--tw-sepia, ) var(--tw-drop-shadow, ); }
  .backdrop-blur-sm { --tw-backdrop-blur: blur(var(--blur-sm)); backdrop-fi=
lter: var(--tw-backdrop-blur, ) var(--tw-backdrop-brightness, ) var(--tw-ba=
ckdrop-contrast, ) var(--tw-backdrop-grayscale, ) var(--tw-backdrop-hue-rot=
ate, ) var(--tw-backdrop-invert, ) var(--tw-backdrop-opacity, ) var(--tw-ba=
ckdrop-saturate, ) var(--tw-backdrop-sepia, ); }
  .transition { transition-property: color, background-color, border-color,=
 outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --=
tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translat=
e, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display=
, visibility, content-visibility, overlay, pointer-events; transition-timin=
g-function: var(--tw-ease, var(--default-transition-timing-function)); tran=
sition-duration: var(--tw-duration, var(--default-transition-duration)); }
  .transition-all { transition-property: all; transition-timing-function: v=
ar(--tw-ease, var(--default-transition-timing-function)); transition-durati=
on: var(--tw-duration, var(--default-transition-duration)); }
  .transition-colors { transition-property: color, background-color, border=
-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-f=
rom, --tw-gradient-via, --tw-gradient-to; transition-timing-function: var(-=
-tw-ease, var(--default-transition-timing-function)); transition-duration: =
var(--tw-duration, var(--default-transition-duration)); }
  .transition-opacity { transition-property: opacity; transition-timing-fun=
ction: var(--tw-ease, var(--default-transition-timing-function)); transitio=
n-duration: var(--tw-duration, var(--default-transition-duration)); }
  .transition-transform { transition-property: transform, translate, scale,=
 rotate; transition-timing-function: var(--tw-ease, var(--default-transitio=
n-timing-function)); transition-duration: var(--tw-duration, var(--default-=
transition-duration)); }
  .duration-200 { --tw-duration: .2s; transition-duration: 0.2s; }
  .duration-300 { --tw-duration: .3s; transition-duration: 0.3s; }
  .ease-in { --tw-ease: var(--ease-in); transition-timing-function: var(--e=
ase-in); }
  .ease-in-out { --tw-ease: var(--ease-in-out); transition-timing-function:=
 var(--ease-in-out); }
  .ease-out { --tw-ease: var(--ease-out); transition-timing-function: var(-=
-ease-out); }
  .outline-none { --tw-outline-style: none; outline-style: none; }
  .select-none { user-select: none; }
  @media (hover: hover) {
  .group-hover\:opacity-100:is(:where(.group):hover *) { opacity: 1; }
}
  .last\:border-b-0:last-child { border-bottom-style: var(--tw-border-style=
); border-bottom-width: 0px; }
  @media (hover: hover) {
  .hover\:scale-105:hover { --tw-scale-x: 105%; --tw-scale-y: 105%; --tw-sc=
ale-z: 105%; scale: var(--tw-scale-x) var(--tw-scale-y); }
}
  @media (hover: hover) {
  .hover\:border-blue-500:hover { border-color: var(--color-blue-500); }
}
  @media (hover: hover) {
  .hover\:border-gray-400:hover { border-color: var(--color-gray-400); }
}
  @media (hover: hover) {
  .hover\:bg-black\/70:hover { background-color: rgba(0, 0, 0, 0.7); }
  @supports (color: color-mix(in lab, red, red)) {
  .hover\:bg-black\/70:hover { background-color: color-mix(in oklab, var(--=
color-black) 70%, transparent); }
}
}
  @media (hover: hover) {
  .hover\:bg-blue-100:hover { background-color: var(--color-blue-100); }
}
  @media (hover: hover) {
  .hover\:bg-blue-600:hover { background-color: var(--color-blue-600); }
}
  @media (hover: hover) {
  .hover\:bg-blue-700:hover { background-color: var(--color-blue-700); }
}
  @media (hover: hover) {
  .hover\:bg-gray-50:hover { background-color: var(--color-gray-50); }
}
  @media (hover: hover) {
  .hover\:bg-gray-100:hover { background-color: var(--color-gray-100); }
}
  @media (hover: hover) {
  .hover\:bg-gray-200:hover { background-color: var(--color-gray-200); }
}
  @media (hover: hover) {
  .hover\:bg-gray-400:hover { background-color: var(--color-gray-400); }
}
  @media (hover: hover) {
  .hover\:bg-gray-500:hover { background-color: var(--color-gray-500); }
}
  @media (hover: hover) {
  .hover\:bg-gray-600:hover { background-color: var(--color-gray-600); }
}
  @media (hover: hover) {
  .hover\:bg-green-100:hover { background-color: var(--color-green-100); }
}
  @media (hover: hover) {
  .hover\:bg-green-600:hover { background-color: var(--color-green-600); }
}
  @media (hover: hover) {
  .hover\:bg-orange-600:hover { background-color: var(--color-orange-600); =
}
}
  @media (hover: hover) {
  .hover\:bg-purple-50:hover { background-color: var(--color-purple-50); }
}
  @media (hover: hover) {
  .hover\:bg-red-100:hover { background-color: var(--color-red-100); }
}
  @media (hover: hover) {
  .hover\:bg-red-600:hover { background-color: var(--color-red-600); }
}
  @media (hover: hover) {
  .hover\:bg-white\/30:hover { background-color: rgba(255, 255, 255, 0.3); =
}
  @supports (color: color-mix(in lab, red, red)) {
  .hover\:bg-white\/30:hover { background-color: color-mix(in oklab, var(--=
color-white) 30%, transparent); }
}
}
  @media (hover: hover) {
  .hover\:bg-white\/50:hover { background-color: rgba(255, 255, 255, 0.5); =
}
  @supports (color: color-mix(in lab, red, red)) {
  .hover\:bg-white\/50:hover { background-color: color-mix(in oklab, var(--=
color-white) 50%, transparent); }
}
}
  @media (hover: hover) {
  .hover\:text-gray-600:hover { color: var(--color-gray-600); }
}
  @media (hover: hover) {
  .hover\:text-gray-700:hover { color: var(--color-gray-700); }
}
  @media (hover: hover) {
  .hover\:text-gray-800:hover { color: var(--color-gray-800); }
}
  @media (hover: hover) {
  .hover\:opacity-80:hover { opacity: 0.8; }
}
  @media (hover: hover) {
  .hover\:opacity-90:hover { opacity: 0.9; }
}
  @media (hover: hover) {
  .hover\:opacity-100:hover { opacity: 1; }
}
  @media (hover: hover) {
  .hover\:shadow-xl:hover { --tw-shadow: 0 20px 25px -5px var(--tw-shadow-c=
olor, #0000001a), 0 8px 10px -6px var(--tw-shadow-color, #0000001a); box-sh=
adow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-of=
fset-shadow), var(--tw-ring-shadow), var(--tw-shadow); }
}
  .focus\:rounded:focus { border-radius: 0.25rem; }
  .focus\:border:focus { border-style: var(--tw-border-style); border-width=
: 1px; }
  .focus\:border-blue-400:focus { border-color: var(--color-blue-400); }
  .focus\:border-blue-500:focus { border-color: var(--color-blue-500); }
  .focus\:bg-white:focus { background-color: var(--color-white); }
  .focus\:ring-1:focus { --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 cal=
c(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor); bo=
x-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-rin=
g-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow); }
  .focus\:ring-2:focus { --tw-ring-shadow: var(--tw-ring-inset, ) 0 0 0 cal=
c(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor); bo=
x-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-rin=
g-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow); }
  .focus\:ring-blue-500:focus { --tw-ring-color: var(--color-blue-500); }
  .focus\:outline-none:focus { --tw-outline-style: none; outline-style: non=
e; }
  .active\:cursor-grabbing:active { cursor: grabbing; }
  @media (width >=3D 48rem) {
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0px, 1fr)); }
}
  @media (width >=3D 48rem) {
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0px, 1fr)); }
}
  @media (width >=3D 64rem) {
  .lg\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0px, 1fr)); }
}
}

:root { --background: #fff; --foreground: #171717; }

@media (prefers-color-scheme: dark) {
  :root { --background: #0a0a0a; --foreground: #ededed; }
}

body { background: var(--background); color: var(--foreground); font-family=
: Arial, Helvetica, sans-serif; }

@keyframes slide-in-from-left {=20
  0% { opacity: 0; transform: translateX(-20px); }
  100% { opacity: 1; transform: translateX(0px); }
}

.animate-in { animation-fill-mode: both; }

.slide-in-from-left-2 { animation: 0.3s ease-out 0s 1 normal none running s=
lide-in-from-left; }

@property --tw-translate-x { syntax: "*"; inherits: false; initial-value: 0=
; }

@property --tw-translate-y { syntax: "*"; inherits: false; initial-value: 0=
; }

@property --tw-translate-z { syntax: "*"; inherits: false; initial-value: 0=
; }

@property --tw-scale-x { syntax: "*"; inherits: false; initial-value: 1; }

@property --tw-scale-y { syntax: "*"; inherits: false; initial-value: 1; }

@property --tw-scale-z { syntax: "*"; inherits: false; initial-value: 1; }

@property --tw-rotate-x { syntax: "*"; inherits: false; }

@property --tw-rotate-y { syntax: "*"; inherits: false; }

@property --tw-rotate-z { syntax: "*"; inherits: false; }

@property --tw-skew-x { syntax: "*"; inherits: false; }

@property --tw-skew-y { syntax: "*"; inherits: false; }

@property --tw-space-y-reverse { syntax: "*"; inherits: false; initial-valu=
e: 0; }

@property --tw-border-style { syntax: "*"; inherits: false; initial-value: =
solid; }

@property --tw-gradient-position { syntax: "*"; inherits: false; }

@property --tw-gradient-from { syntax: "<color>"; inherits: false; initial-=
value: #0000; }

@property --tw-gradient-via { syntax: "<color>"; inherits: false; initial-v=
alue: #0000; }

@property --tw-gradient-to { syntax: "<color>"; inherits: false; initial-va=
lue: #0000; }

@property --tw-gradient-stops { syntax: "*"; inherits: false; }

@property --tw-gradient-via-stops { syntax: "*"; inherits: false; }

@property --tw-gradient-from-position { syntax: "<length-percentage>"; inhe=
rits: false; initial-value: 0%; }

@property --tw-gradient-via-position { syntax: "<length-percentage>"; inher=
its: false; initial-value: 50%; }

@property --tw-gradient-to-position { syntax: "<length-percentage>"; inheri=
ts: false; initial-value: 100%; }

@property --tw-leading { syntax: "*"; inherits: false; }

@property --tw-font-weight { syntax: "*"; inherits: false; }

@property --tw-shadow { syntax: "*"; inherits: false; initial-value: 0 0 #0=
000; }

@property --tw-shadow-color { syntax: "*"; inherits: false; }

@property --tw-shadow-alpha { syntax: "<percentage>"; inherits: false; init=
ial-value: 100%; }

@property --tw-inset-shadow { syntax: "*"; inherits: false; initial-value: =
0 0 #0000; }

@property --tw-inset-shadow-color { syntax: "*"; inherits: false; }

@property --tw-inset-shadow-alpha { syntax: "<percentage>"; inherits: false=
; initial-value: 100%; }

@property --tw-ring-color { syntax: "*"; inherits: false; }

@property --tw-ring-shadow { syntax: "*"; inherits: false; initial-value: 0=
 0 #0000; }

@property --tw-inset-ring-color { syntax: "*"; inherits: false; }

@property --tw-inset-ring-shadow { syntax: "*"; inherits: false; initial-va=
lue: 0 0 #0000; }

@property --tw-ring-inset { syntax: "*"; inherits: false; }

@property --tw-ring-offset-width { syntax: "<length>"; inherits: false; ini=
tial-value: 0; }

@property --tw-ring-offset-color { syntax: "*"; inherits: false; initial-va=
lue: #fff; }

@property --tw-ring-offset-shadow { syntax: "*"; inherits: false; initial-v=
alue: 0 0 #0000; }

@property --tw-outline-style { syntax: "*"; inherits: false; initial-value:=
 solid; }

@property --tw-blur { syntax: "*"; inherits: false; }

@property --tw-brightness { syntax: "*"; inherits: false; }

@property --tw-contrast { syntax: "*"; inherits: false; }

@property --tw-grayscale { syntax: "*"; inherits: false; }

@property --tw-hue-rotate { syntax: "*"; inherits: false; }

@property --tw-invert { syntax: "*"; inherits: false; }

@property --tw-opacity { syntax: "*"; inherits: false; }

@property --tw-saturate { syntax: "*"; inherits: false; }

@property --tw-sepia { syntax: "*"; inherits: false; }

@property --tw-drop-shadow { syntax: "*"; inherits: false; }

@property --tw-drop-shadow-color { syntax: "*"; inherits: false; }

@property --tw-drop-shadow-alpha { syntax: "<percentage>"; inherits: false;=
 initial-value: 100%; }

@property --tw-drop-shadow-size { syntax: "*"; inherits: false; }

@property --tw-backdrop-blur { syntax: "*"; inherits: false; }

@property --tw-backdrop-brightness { syntax: "*"; inherits: false; }

@property --tw-backdrop-contrast { syntax: "*"; inherits: false; }

@property --tw-backdrop-grayscale { syntax: "*"; inherits: false; }

@property --tw-backdrop-hue-rotate { syntax: "*"; inherits: false; }

@property --tw-backdrop-invert { syntax: "*"; inherits: false; }

@property --tw-backdrop-opacity { syntax: "*"; inherits: false; }

@property --tw-backdrop-saturate { syntax: "*"; inherits: false; }

@property --tw-backdrop-sepia { syntax: "*"; inherits: false; }

@property --tw-duration { syntax: "*"; inherits: false; }

@property --tw-ease { syntax: "*"; inherits: false; }

@keyframes spin {=20
  100% { transform: rotate(360deg); }
}

@keyframes pulse {=20
  50% { opacity: 0.5; }
}
------MultipartBoundary--fMhVSzyTwHauVOsjgCwnDvnHSyaD7mUIrpPPqdaxBT----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

@font-face { font-family: __nextjs-Geist; font-style: normal; font-weight: =
400 600; font-display: swap; src: url("/__nextjs_font/geist-latin-ext.woff2=
") format("woff2"); unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2=
D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, =
U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF; }

@font-face { font-family: "__nextjs-Geist Mono"; font-style: normal; font-w=
eight: 400 600; font-display: swap; src: url("/__nextjs_font/geist-mono-lat=
in-ext.woff2") format("woff2"); unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-=
2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U=
+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A=
7FF; }

@font-face { font-family: __nextjs-Geist; font-style: normal; font-weight: =
400 600; font-display: swap; src: url("/__nextjs_font/geist-latin.woff2") f=
ormat("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-2BC, U+2C6, =
U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2=
193, U+2212, U+2215, U+FEFF, U+FFFD; }

@font-face { font-family: "__nextjs-Geist Mono"; font-style: normal; font-w=
eight: 400 600; font-display: swap; src: url("/__nextjs_font/geist-mono-lat=
in.woff2") format("woff2"); unicode-range: U+0-FF, U+131, U+152-153, U+2BB-=
2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122,=
 U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD; }
------MultipartBoundary--fMhVSzyTwHauVOsjgCwnDvnHSyaD7mUIrpPPqdaxBT----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.scroll-area.jsx-**********::-webkit-scrollbar { width: 6px; height: 6px; }

.scroll-area.jsx-**********::-webkit-scrollbar-track { background: rgb(241,=
 241, 241); border-radius: 3px; }

.scroll-area.jsx-**********::-webkit-scrollbar-thumb { background: rgb(193,=
 193, 193); border-radius: 3px; }

.scroll-area.jsx-**********.jsx-**********::-webkit-scrollbar-thumb:hover {=
 background: rgb(168, 168, 168); }

.editor-scroll.jsx-**********::-webkit-scrollbar { width: 8px; height: 8px;=
 }

.editor-scroll.jsx-**********::-webkit-scrollbar-track { background: rgba(0=
, 0, 0, 0.05); border-radius: 4px; }

.editor-scroll.jsx-**********::-webkit-scrollbar-thumb { background: rgba(0=
, 0, 0, 0.2); border-radius: 4px; }

.editor-scroll.jsx-**********.jsx-**********::-webkit-scrollbar-thumb:hover=
 { background: rgba(0, 0, 0, 0.3); }

.editor-scroll.jsx-**********::-webkit-scrollbar-corner { background: 0px 0=
px; }

.editor-scroll.jsx-********** { box-sizing: border-box; margin: 0px; paddin=
g: 0px; }

.editor-scroll.jsx-**********::-webkit-scrollbar { background: 0px 0px; bor=
der: none; margin: 0px; padding: 0px; }

.editor-scroll.jsx-********** { scrollbar-gutter: stable; box-sizing: borde=
r-box; width: calc(100% + 30px); margin-left: -30px; padding-left: 30px; }

.scroll-area.jsx-********** { scrollbar-width: auto !important; scrollbar-c=
olor: rgb(193, 193, 193) rgb(241, 241, 241) !important; }
------MultipartBoundary--fMhVSzyTwHauVOsjgCwnDvnHSyaD7mUIrpPPqdaxBT------
