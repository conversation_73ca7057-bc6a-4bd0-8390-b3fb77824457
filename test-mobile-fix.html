<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التجاوب المحسن</title>
    <style>
        /* تم إنشاء هذا الملف بواسطة AI Website Builder */
        /* أبعاد ورقة العمل: 1280×720px */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            width: 1280px;
            min-height: 720px;
            max-width: 1280px;
            position: relative;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.15);
            overflow: hidden;
            margin: auto;
            border: 3px solid rgba(255,255,255,0.2);
        }

        .main-content {
            position: relative;
            width: 100%;
            min-height: 720px;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        /* العناصر الأساسية */
        #element_123 {
            position: absolute;
            left: 100px;
            top: 50px;
            width: 200px;
            height: 60px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        #element_456 {
            position: absolute;
            left: 400px;
            top: 150px;
            width: 300px;
            height: 40px;
            background: #10b981;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        #element_789 {
            position: absolute;
            left: 800px;
            top: 100px;
            width: 250px;
            height: 50px;
            background: #f59e0b;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        /* تصميم متجاوب للموبايل - يطبق عندما تكون الشاشة أصغر من 768px */
        @media (max-width: 768px) {
            body {
                padding: 0 !important;
                margin: 0 !important;
            }

            .container {
                margin: 0 !important;
                padding: 0 !important;
                width: 100vw !important;
                min-height: 100vh !important;
                max-width: 100vw !important;
                border-radius: 0 !important;
                box-shadow: none !important;
                border: none !important;
            }

            .main-content {
                min-height: 100vh !important;
                padding: 20px !important;
                margin: 0 !important;
            }

            /* ترتيب العناصر عمودياً في الموبايل */
            #element_123 {
                left: 20px !important;
                top: 50px !important;
                position: absolute !important;
                width: calc(100vw - 40px) !important;
                max-width: 300px !important;
            }

            #element_456 {
                left: 20px !important;
                top: 130px !important;
                position: absolute !important;
                width: calc(100vw - 40px) !important;
                max-width: 300px !important;
            }

            #element_789 {
                left: 20px !important;
                top: 210px !important;
                position: absolute !important;
                width: calc(100vw - 40px) !important;
                max-width: 300px !important;
            }
        }

        /* تصميم متجاوب للتابلت - يطبق عندما تكون الشاشة بين 769px و 1024px */
        @media (min-width: 769px) and (max-width: 1024px) {
            .container {
                width: 100vw !important;
                max-width: 100vw !important;
                border-radius: 0 !important;
                box-shadow: none !important;
                border: none !important;
            }
            
            .main-content {
                padding: 20px !important;
            }

            /* ترتيب العناصر للتابلت */
            #element_123 {
                left: 50px !important;
                top: 50px !important;
            }

            #element_456 {
                left: 350px !important;
                top: 50px !important;
            }

            #element_789 {
                left: 200px !important;
                top: 150px !important;
            }
        }

        /* مؤشر حجم الشاشة للاختبار */
        .screen-indicator {
            position: fixed;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
            font-family: monospace;
        }

    </style>
</head>
<body>
    <div class="screen-indicator" id="screenIndicator">
        🖥️ Desktop: 1920×1080
    </div>

    <div class="container">
        <main class="main-content">
            <button id="element_123">زر اختبار 1</button>
            <button id="element_456">زر اختبار 2</button>
            <button id="element_789">زر اختبار 3</button>
        </main>
    </div>

    <script>
        // تحديث مؤشر حجم الشاشة
        function updateScreenIndicator() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const indicator = document.getElementById('screenIndicator');
            
            let deviceType = '';
            let emoji = '';
            
            if (width <= 480) {
                deviceType = 'Mobile Small';
                emoji = '📱';
            } else if (width <= 768) {
                deviceType = 'Mobile';
                emoji = '📱';
            } else if (width <= 1024) {
                deviceType = 'Tablet';
                emoji = '📟';
            } else {
                deviceType = 'Desktop';
                emoji = '🖥️';
            }
            
            indicator.textContent = `${emoji} ${deviceType}: ${width}×${height}`;
            
            // تغيير لون المؤشر حسب النوع
            if (width <= 768) {
                indicator.style.background = 'rgba(59, 130, 246, 0.9)'; // أزرق للموبايل
            } else if (width <= 1024) {
                indicator.style.background = 'rgba(16, 185, 129, 0.9)'; // أخضر للتابلت
            } else {
                indicator.style.background = 'rgba(0, 0, 0, 0.8)'; // أسود للديسكتوب
            }
        }

        // تحديث عند التحميل
        updateScreenIndicator();

        // تحديث عند تغيير حجم الشاشة
        window.addEventListener('resize', updateScreenIndicator);

        // إضافة أحداث للأزرار للاختبار
        document.getElementById('element_123').addEventListener('click', () => {
            alert('تم النقر على الزر الأول!');
        });

        document.getElementById('element_456').addEventListener('click', () => {
            alert('تم النقر على الزر الثاني!');
        });

        document.getElementById('element_789').addEventListener('click', () => {
            alert('تم النقر على الزر الثالث!');
        });
    </script>
</body>
</html>
