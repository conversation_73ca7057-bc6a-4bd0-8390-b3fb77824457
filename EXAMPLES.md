# 🤖 أمثلة على البرومبتات وتوليد الكود

## 📝 **أمثلة البرومبتات:**

### 1. **زر الطباعة:**
```
البرومبت: "طباعة الصفحة"

الكود المولد:
```javascript
// ربط وظيفة الطباعة بالزر
const button = document.getElementById('element_1752172017809');
if (button) {
  button.onclick = function() {
    try {
      window.print();
    } catch (error) {
      console.error('خطأ في الطباعة:', error);
    }
  };
}
```

### 2. **زر تسجيل الدخول:**
```
البرومبت: "جمع بيانات تسجيل الدخول وإرسالها للخادم"

الكود المولد:
```javascript
// دالة تسجيل الدخول
const handleLogin = async () => {
  // جمع البيانات من النموذج
  const email = document.getElementById('email').value;
  const password = document.getElementById('password').value;

  // التحقق من صحة البيانات
  if (!email || !password) {
    alert('يرجى ملء جميع الحقول');
    return;
  }

  try {
    // إرسال البيانات للخادم
    const response = await fetch('/api/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password })
    });

    const result = await response.json();

    if (result.success) {
      alert('تم تسجيل الدخول بنجاح!');
      window.location.href = '/dashboard';
    } else {
      alert('خطأ في البيانات: ' + result.message);
    }
  } catch (error) {
    alert('حدث خطأ في الاتصال');
    console.error('خطأ تسجيل الدخول:', error);
  }
};
```

### 3. **زر عرض رسالة:**
```
البرومبت: "عرض رسالة ترحيب"

الكود المولد:
```javascript
// ربط وظيفة عرض الرسالة بالزر
const button = document.getElementById('element_1752172017809');
if (button) {
  button.onclick = function() {
    alert('أهلاً وسهلاً بك في موقعنا! 🎉');
  };
}
```

### 4. **زر حفظ البيانات:**
```
البرومبت: "حفظ البيانات في التخزين المحلي"

الكود المولد:
```javascript
// دالة حفظ البيانات محلياً
const saveData = () => {
  try {
    const data = {
      timestamp: new Date().toISOString(),
      message: 'تم الحفظ بنجاح'
    };
    localStorage.setItem('userData', JSON.stringify(data));
    alert('تم حفظ البيانات بنجاح! ✅');
  } catch (error) {
    console.error('خطأ في الحفظ:', error);
    alert('حدث خطأ أثناء الحفظ');
  }
};
```

### 5. **زر جلب البيانات:**
```
البرومبت: "جلب قائمة المنتجات من API وعرضها"

الكود المولد:
```javascript
// جلب وعرض المنتجات
const loadProducts = async () => {
  try {
    // عرض مؤشر التحميل
    document.getElementById('loading').style.display = 'block';
    
    // جلب البيانات
    const response = await fetch('/api/products');
    const products = await response.json();
    
    // إنشاء الجدول
    const tableHTML = products.map(product => `
      <tr>
        <td>${product.name}</td>
        <td>${product.price}</td>
        <td>${product.category}</td>
        <td>
          <button onclick="editProduct(${product.id})">تعديل</button>
          <button onclick="deleteProduct(${product.id})">حذف</button>
        </td>
      </tr>
    `).join('');
    
    // عرض البيانات
    document.getElementById('products-table').innerHTML = tableHTML;
    
    // إخفاء مؤشر التحميل
    document.getElementById('loading').style.display = 'none';
    
  } catch (error) {
    alert('خطأ في جلب البيانات');
    console.error('خطأ جلب المنتجات:', error);
  }
};

// دالة البحث
const searchProducts = (searchTerm) => {
  const rows = document.querySelectorAll('#products-table tr');
  rows.forEach(row => {
    const text = row.textContent.toLowerCase();
    row.style.display = text.includes(searchTerm.toLowerCase()) ? '' : 'none';
  });
};
```

### 3. **زر حفظ البيانات:**
```
البرومبت: "احفظ بيانات النموذج في التخزين المحلي مع إظهار رسالة نجاح"

الكود المولد:
```javascript
// حفظ البيانات محلياً
const saveFormData = () => {
  try {
    // جمع بيانات النموذج
    const formData = {
      name: document.getElementById('name').value,
      email: document.getElementById('email').value,
      phone: document.getElementById('phone').value,
      message: document.getElementById('message').value,
      timestamp: new Date().toISOString()
    };
    
    // التحقق من البيانات
    if (!formData.name || !formData.email) {
      alert('الاسم والبريد الإلكتروني مطلوبان');
      return;
    }
    
    // حفظ في التخزين المحلي
    const existingData = JSON.parse(localStorage.getItem('formSubmissions') || '[]');
    existingData.push(formData);
    localStorage.setItem('formSubmissions', JSON.stringify(existingData));
    
    // رسالة النجاح
    alert('تم حفظ البيانات بنجاح! ✅');
    
    // مسح النموذج
    document.getElementById('contact-form').reset();
    
  } catch (error) {
    alert('حدث خطأ في حفظ البيانات');
    console.error('خطأ الحفظ:', error);
  }
};
```

## 🎯 **كيف يعمل النظام:**

1. **المستخدم يكتب البرومبت** بالعربية في حقل "البرومبت (الوظيفة)"
2. **يضغط زر "توليد الكود بالذكاء الاصطناعي"**
3. **النظام يرسل البرومبت إلى OpenAI** مع معلومات العنصر
4. **الذكاء الاصطناعي يحلل الطلب** ويولد كود JavaScript مناسب
5. **يتم عرض الكود** في نافذة الخصائص مع إمكانية النسخ
6. **المستخدم يمكنه نسخ الكود** واستخدامه في مشروعه

## 💡 **نصائح لكتابة البرومبتات الفعالة:**

### ✅ **أمثلة جيدة:**
- "طباعة الصفحة"
- "حفظ البيانات في التخزين المحلي"
- "عرض رسالة ترحيب"
- "إرسال نموذج الاتصال"
- "جلب بيانات المستخدمين"

### ❌ **تجنب هذه الأمثلة:**
- "اعمل زر" (غير واضح)
- "أضف وظيفة للعنصر" (عام جداً)
- "اكتب كود HTML" (النظام يولد JavaScript فقط)

### 🎯 **قواعد البرومبت الجيد:**
1. **كن محدداً**: "طباعة الصفحة" أفضل من "طباعة"
2. **استخدم أفعال واضحة**: حفظ، إرسال، جلب، عرض
3. **اذكر المصدر إذا لزم**: "جلب من API" أو "حفظ في localStorage"
4. **اكتب بالعربية**: النظام يفهم العربية بشكل ممتاز

## 🚀 **المميزات:**

- **فهم اللغة العربية** الطبيعية
- **توليد دوال JavaScript** نظيفة وقابلة للتنفيذ
- **معالجة الأخطاء** تلقائياً
- **تعليقات بالعربية** لسهولة الفهم
- **أمثلة متنوعة** للاستخدامات المختلفة
- **تركيز على الوظيفة** بدلاً من إنشاء عناصر جديدة
