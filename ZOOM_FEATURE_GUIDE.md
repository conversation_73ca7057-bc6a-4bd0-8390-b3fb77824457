# 🔍 دليل استخدام ميزة الزوم في المحرر

## ✨ الميزة الجديدة

تم إضافة نظام زوم متقدم للمحرر يتيح لك:
- **تكبير وتصغير** منطقة العمل بسهولة
- **التحكم الدقيق** في مستوى التفاصيل
- **التنقل السلس** أثناء التصميم

---

## 🎮 كيفية الاستخدام

### **الطريقة الأساسية:**
```
Alt + دولاب الماوس للأعلى = تكبير (Zoom In)
Alt + دولاب الماوس للأسفل = تصغير (Zoom Out)
```

### **أزرار التحكم:**
- **زر −** : تصغير تدريجي
- **زر 100%** : إعادة تعيين الزوم للحجم الطبيعي
- **زر +** : تكبير تدريجي

---

## 📊 مستويات الزوم

- **الحد الأدنى**: 10% (0.1x)
- **الحجم الطبيعي**: 100% (1.0x)
- **الحد الأقصى**: 500% (5.0x)
- **خطوة التغيير**: 15% لكل حركة

---

## 🎯 المميزات المتقدمة

### **1. نقطة الزوم الذكية**
- الزوم يحدث من **مركز الشاشة**
- يحافظ على **موضع العناصر** نسبياً
- **تجربة سلسة** بدون قفزات مفاجئة

### **2. مؤشر بصري**
- **شريط الأدوات**: يعرض النسبة الحالية
- **تلميح عائم**: يظهر عند تغيير الزوم
- **أيقونة 🔍**: تشير لحالة الزوم النشطة

### **3. تكامل مع الوظائف الأخرى**
- **يعمل مع التحريك**: Ctrl + سحب للتنقل
- **يحافظ على التحديد**: العناصر المحددة تبقى محددة
- **متوافق مع الصفحات المتعددة**: كل صفحة لها زوم منفصل

---

## 🛠️ التفاصيل التقنية

### **الكود المضاف:**

```typescript
// حالة الزوم
const [zoomLevel, setZoomLevel] = useState(1);
const [zoomOrigin, setZoomOrigin] = useState({ x: 0, y: 0 });

// دالة معالجة الزوم
const handleZoom = useCallback((event: WheelEvent) => {
  if (!event.altKey) return;
  
  event.preventDefault();
  event.stopPropagation();
  
  const zoomDirection = event.deltaY < 0 ? 1 : -1;
  const zoomFactor = 0.15;
  const newZoomLevel = Math.max(0.1, Math.min(5, zoomLevel + (zoomDirection * zoomFactor)));
  
  // تحديث الزوم والموضع
  setZoomLevel(newZoomLevel);
  setZoomOrigin({ x: centerX, y: centerY });
  setCanvasPosition({ x: newCanvasX, y: newCanvasY });
}, [zoomLevel, canvasPosition]);
```

### **تطبيق الزوم على الكانفاس:**

```typescript
<div
  ref={currentPageId === page.id ? canvasRef : null}
  style={{
    transform: currentPageId === page.id ? 
      `scale(${zoomLevel}) translate(${canvasPosition.x}px, ${canvasPosition.y}px)` : 
      'none',
    transformOrigin: currentPageId === page.id ? 
      `${zoomOrigin.x}px ${zoomOrigin.y}px` : 
      'center',
    transition: 'transform 0.1s ease-out',
  }}
>
```

---

## 🎨 واجهة المستخدم

### **شريط الأدوات العلوي:**
```
🔍 150% [−] [100%] [+]
```

### **التلميح العائم:**
```
🔍 الزوم: 150% | Alt + دولاب الماوس للتحكم
```

---

## 📱 حالات الاستخدام

### **1. التصميم الدقيق**
- **تكبير للتفاصيل**: ضبط موضع العناصر بدقة
- **محاذاة العناصر**: رؤية أفضل للمسافات
- **تحرير النصوص**: قراءة أوضح للمحتوى

### **2. النظرة الشاملة**
- **تصغير للعرض العام**: رؤية التصميم كاملاً
- **مقارنة الصفحات**: عرض عدة صفحات معاً
- **التخطيط العام**: فهم بنية المشروع

### **3. العرض والمراجعة**
- **عرض للعملاء**: تكبير لإظهار التفاصيل
- **مراجعة التصميم**: فحص دقيق للعناصر
- **التوثيق**: التقاط صور بدقة عالية

---

## ⚡ نصائح للاستخدام الأمثل

### **1. اختصارات سريعة**
- **Alt + دولاب**: الزوم السريع
- **انقر 100%**: العودة للحجم الطبيعي
- **Ctrl + سحب**: التنقل أثناء الزوم

### **2. أفضل الممارسات**
- **ابدأ بـ 100%**: للحصول على منظور طبيعي
- **استخدم 150-200%**: للتفاصيل الدقيقة
- **استخدم 50-75%**: للنظرة الشاملة

### **3. تجنب المشاكل**
- **لا تفرط في التكبير**: قد يصبح التنقل صعباً
- **استخدم إعادة التعيين**: عند فقدان الموضع
- **تذكر Alt**: الزوم لا يعمل بدونه

---

## 🔧 المشاكل المحتملة والحلول

### **المشكلة**: الزوم لا يعمل
**الحل**: تأكد من الضغط على Alt مع دولاب الماوس

### **المشكلة**: فقدان موضع الكانفاس
**الحل**: انقر على زر "100%" لإعادة التعيين

### **المشكلة**: الزوم بطيء جداً
**الحل**: استخدم أزرار + و − للتحكم السريع

### **المشكلة**: العناصر تبدو مشوشة
**الحل**: هذا طبيعي في مستويات الزوم العالية

---

## 🚀 التحسينات المستقبلية

1. **زوم تلقائي**: ملائمة الزوم لحجم الشاشة
2. **زوم للعنصر**: تكبير عنصر محدد تلقائياً
3. **حفظ مستوى الزوم**: تذكر الإعدادات لكل مشروع
4. **زوم متدرج**: انتقالات أكثر سلاسة
5. **زوم بالإيماءات**: دعم شاشات اللمس

---

## 🎉 الخلاصة

ميزة الزوم الجديدة تجعل التصميم أكثر دقة ومرونة:
- ✅ **سهولة الاستخدام**: Alt + دولاب الماوس
- ✅ **تحكم دقيق**: من 10% إلى 500%
- ✅ **واجهة بديهية**: مؤشرات بصرية واضحة
- ✅ **أداء سلس**: انتقالات محسنة
- ✅ **تكامل كامل**: يعمل مع جميع الوظائف

استمتع بتجربة تصميم محسنة! 🎨✨
