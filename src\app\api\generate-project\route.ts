import { NextRequest, NextResponse } from 'next/server';

interface ProjectFile {
  path: string;
  content: string;
}

// دالة تحليل الترابط بين العناصر
function analyzeElementRelationships(elements: any[], pages?: any[]): any {
  const relationships: any[] = [];
  const elementMap = new Map();
  const pageMap = new Map();

  // إنشاء خريطة العناصر
  elements.forEach((element: any) => {
    elementMap.set(element.id, element);
  });

  // إنشاء خريطة الصفحات
  if (pages) {
    pages.forEach((page: any) => {
      pageMap.set(page.id, page);
      pageMap.set(page.name.toLowerCase(), page);
    });
  }

  // تحليل البرومبتات للعثور على الترابط
  elements.forEach((element: any) => {
    if (element.properties?.prompt) {
      const prompt = element.properties.prompt.toLowerCase();
      const foundRelationships: any[] = [];

      // البحث عن معرفات العناصر في البرومبت باستخدام التحليل المتقدم
      const referencedElementIds = findElementReferencesInPrompt(prompt, elements);
      referencedElementIds.forEach(targetId => {
        if (targetId !== element.id) {
          const targetElement = elements.find(el => el.id === targetId);
          if (targetElement) {
            foundRelationships.push({
              type: 'element_reference',
              target: targetElement.id,
              targetType: targetElement.type,
              targetText: targetElement.properties?.text || '',
              action: extractActionFromPrompt(prompt)
            });
          }
        }
      });

      // البحث عن أسماء الصفحات في البرومبت
      if (pages) {
        pages.forEach((page: any) => {
          if (prompt.includes(page.name.toLowerCase()) ||
              prompt.includes('صفحة') || prompt.includes('انتقل') || prompt.includes('اذهب')) {
            foundRelationships.push({
              type: 'page_navigation',
              target: page.id,
              targetName: page.name,
              action: 'navigate'
            });
          }
        });
      }

      if (foundRelationships.length > 0) {
        relationships.push({
          elementId: element.id,
          elementType: element.type,
          prompt: element.properties.prompt,
          relationships: foundRelationships
        });
      }
    }
  });

  return {
    relationships,
    elementMap,
    pageMap,
    hasRelationships: relationships.length > 0
  };
}

// استخراج نوع الإجراء من البرومبت مع تحليل متقدم
function extractActionFromPrompt(prompt: string): string {
  const lowerPrompt = prompt.toLowerCase();

  // إجراءات العرض والإخفاء
  if (lowerPrompt.includes('اخفي') || lowerPrompt.includes('اخف') || lowerPrompt.includes('hide')) return 'hide';
  if (lowerPrompt.includes('اظهر') || lowerPrompt.includes('show') || lowerPrompt.includes('عرض')) return 'show';
  if (lowerPrompt.includes('تبديل') || lowerPrompt.includes('toggle')) return 'toggle';

  // إجراءات التعديل
  if (lowerPrompt.includes('غير') || lowerPrompt.includes('بدل') || lowerPrompt.includes('change')) return 'change';
  if (lowerPrompt.includes('لون') && (lowerPrompt.includes('غير') || lowerPrompt.includes('بدل'))) return 'change_color';
  if (lowerPrompt.includes('حجم') && (lowerPrompt.includes('غير') || lowerPrompt.includes('بدل'))) return 'change_size';

  // إجراءات البيانات
  if (lowerPrompt.includes('امسح') || lowerPrompt.includes('احذف') || lowerPrompt.includes('clear')) return 'clear';
  if (lowerPrompt.includes('اضف') || lowerPrompt.includes('add') || lowerPrompt.includes('insert')) return 'add';
  if (lowerPrompt.includes('احفظ') || lowerPrompt.includes('save')) return 'save';
  if (lowerPrompt.includes('حمل') || lowerPrompt.includes('load') || lowerPrompt.includes('جلب')) return 'load';

  // إجراءات التنقل
  if (lowerPrompt.includes('انتقل') || lowerPrompt.includes('اذهب') || lowerPrompt.includes('navigate')) return 'navigate';
  if (lowerPrompt.includes('ارجع') || lowerPrompt.includes('back') || lowerPrompt.includes('عودة')) return 'back';
  if (lowerPrompt.includes('صفحة') && (lowerPrompt.includes('انتقل') || lowerPrompt.includes('اذهب'))) return 'navigate_page';

  // إجراءات التفاعل
  if (lowerPrompt.includes('انقر') || lowerPrompt.includes('اضغط') || lowerPrompt.includes('click')) return 'click';
  if (lowerPrompt.includes('تحرك') || lowerPrompt.includes('move') || lowerPrompt.includes('drag')) return 'move';
  if (lowerPrompt.includes('تكبير') || lowerPrompt.includes('zoom') || lowerPrompt.includes('scale')) return 'scale';

  // إجراءات التأثيرات
  if (lowerPrompt.includes('تأثير') || lowerPrompt.includes('animation') || lowerPrompt.includes('effect')) return 'animate';
  if (lowerPrompt.includes('يهتز') || lowerPrompt.includes('shake')) return 'shake';
  if (lowerPrompt.includes('يطفو') || lowerPrompt.includes('float')) return 'float';

  return 'custom';
}

// دالة تحليل متقدمة للعثور على المعرفات في النص
function findElementReferencesInPrompt(prompt: string, elements: any[]): string[] {
  const foundIds: string[] = [];
  const lowerPrompt = prompt.toLowerCase();

  elements.forEach((element: any) => {
    // البحث عن المعرف المباشر
    if (lowerPrompt.includes(element.id.toLowerCase())) {
      foundIds.push(element.id);
      return;
    }

    // البحث عن النص
    if (element.properties?.text &&
        lowerPrompt.includes(element.properties.text.toLowerCase())) {
      foundIds.push(element.id);
      return;
    }

    // البحث عن التسمية
    if (element.properties?.label &&
        lowerPrompt.includes(element.properties.label.toLowerCase())) {
      foundIds.push(element.id);
      return;
    }

    // البحث عن العنوان
    if (element.properties?.title &&
        lowerPrompt.includes(element.properties.title.toLowerCase())) {
      foundIds.push(element.id);
      return;
    }

    // البحث عن نوع العنصر مع كلمات وصفية
    const typeKeywords: Record<string, string[]> = {
      'button': ['زر', 'زرار', 'button'],
      'text': ['نص', 'كتابة', 'text'],
      'input': ['حقل', 'إدخال', 'input', 'مدخل'],
      'image': ['صورة', 'image', 'صوره'],
      'table': ['جدول', 'table'],
      'form': ['نموذج', 'form', 'استمارة']
    };

    const keywords = typeKeywords[element.type] || [];
    keywords.forEach((keyword: string) => {
      if (lowerPrompt.includes(keyword)) {
        // التحقق من وجود كلمات وصفية إضافية
        const elementText = element.properties?.text || '';
        if (elementText && lowerPrompt.includes(elementText.toLowerCase())) {
          foundIds.push(element.id);
        }
      }
    });
  });

  return [...new Set(foundIds)]; // إزالة التكرار
}

// استدعاء الذكاء الاصطناعي لتوليد الكود مع تحليل الترابط
async function generateAICode(elements: any[], canvasSize?: any, projectInfo?: any, pages?: any[]): Promise<string> {
  try {
    // تحليل الترابط بين العناصر
    const relationshipAnalysis = analyzeElementRelationships(elements, pages);

    // فلترة العناصر - إرسال العناصر التفاعلية فقط التي لم يتم توليد كود لها مسبقاً
    const elementsWithPrompt = elements.filter(element =>
      element.properties?.prompt &&
      element.properties.prompt.trim() !== '' &&
      !element.properties?.generatedCode // تجاهل العناصر التي لها كود مولد فردياً
    );

    // إضافة العناصر المرتبطة للسياق
    const relatedElementIds = new Set<string>();
    relationshipAnalysis.relationships.forEach((rel: any) => {
      rel.relationships.forEach((r: any) => {
        if (r.type === 'element_reference') {
          relatedElementIds.add(r.target);
        }
      });
    });

    // إضافة العناصر المرتبطة للسياق
    const contextElements = elements.filter(element =>
      relatedElementIds.has(element.id) ||
      (element.properties?.prompt && element.properties.prompt.trim() !== '')
    );

    // إذا لم توجد عناصر تحتوي على prompt، إرجاع كود بسيط
    if (elementsWithPrompt.length === 0) {
      console.log('📝 لا توجد عناصر تحتوي على وظائف - إرجاع كود بسيط');
      return `// لا توجد عناصر تحتوي على وظائف مطلوبة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل الموقع بنجاح');
    console.log('📄 جميع العناصر ثابتة - لا توجد تفاعلات مطلوبة');
});`;
    }

    // إعداد البيانات للإرسال للـ AI مع تفاصيل التصميم والترابط
    const elementsData = elementsWithPrompt.map(element => {
      // البحث عن العلاقات الخاصة بهذا العنصر
      const elementRelationships = relationshipAnalysis.relationships.find((rel: any) => rel.elementId === element.id);

      return {
        id: element.id,
        type: element.type,
        text: element.properties?.text || '',
        prompt: element.properties?.prompt || '',
        position: { x: element.x, y: element.y },
        size: { width: element.width, height: element.height },
        rotation: element.rotation || 0,
        // خصائص العناصر التفاعلية
        title: element.properties?.title || '',
        description: element.properties?.description || '',
        icon: element.properties?.icon || '',
        label: element.properties?.label || '',
        placeholder: element.properties?.placeholder || '',
        imageUrl: element.properties?.imageUrl || '',
        imageMode: element.properties?.imageMode || 'cover',
        design: {
          backgroundColor: element.properties?.backgroundColor || '#ffffff',
          color: element.properties?.color || '#000000',
          fontSize: element.properties?.fontSize || '14px',
          borderRadius: element.properties?.borderRadius || '4px',
          padding: element.properties?.padding || '8px',
          margin: element.properties?.margin || '0px',
          border: element.properties?.border || 'none',
          fontWeight: element.properties?.fontWeight || 'normal'
        },
        // معلومات الترابط
        relationships: elementRelationships?.relationships || [],
        hasRelationships: elementRelationships ? true : false
      };
    });

    // إعداد بيانات العناصر المرتبطة (للسياق)
    const contextElementsData = contextElements
      .filter(element => !elementsWithPrompt.find(e => e.id === element.id)) // تجنب التكرار
      .map(element => ({
        id: element.id,
        type: element.type,
        text: element.properties?.text || '',
        position: { x: element.x, y: element.y },
        size: { width: element.width, height: element.height },
        design: {
          backgroundColor: element.properties?.backgroundColor || '#ffffff',
          color: element.properties?.color || '#000000'
        },
        isContextElement: true // تمييز العناصر المرجعية
      }));

    // إعداد بيانات الصفحات (للتنقل)
    const pagesData = pages ? pages.map(page => ({
      id: page.id,
      name: page.name,
      elementsCount: page.elements?.length || 0,
      hasInteractiveElements: page.elements?.some((el: any) => el.properties?.prompt) || false
    })) : [];

    const prompt = `Write JavaScript for interactive website elements:

Project: ${projectInfo?.type || 'website'} - ${projectInfo?.name || 'project'}
Canvas: ${canvasSize?.width || 375}x${canvasSize?.height || 667}px

Interactive elements:
${elementsData.map((el, index) => `${index + 1}. ${el.type} (id: ${el.id}) - Function: ${el.prompt}`).join('\n')}

${contextElementsData.length > 0 ? `Related elements: ${contextElementsData.map(el => `${el.type} (${el.id})`).join(', ')}` : ''}

Requirements:
- Use addEventListener for events
- Use correct element IDs
- Add smooth interactions
- Handle errors properly
- Return only JavaScript code, no explanations
`;

    // استدعاء API الذكاء الاصطناعي (يمكن استخدام OpenAI أو أي API آخر)
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`
      },
      body: JSON.stringify({
        model: 'gpt-4.1-nano',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.3
      })
    });

    if (response.ok) {
      const data = await response.json();
      let generatedCode = data.choices[0]?.message?.content || '';

      // تنظيف الرد من النصوص الإضافية
      generatedCode = cleanAIJavaScriptResponse(generatedCode);

      console.log('🤖 تم توليد الكود بالذكاء الاصطناعي:', generatedCode);
      return generatedCode;
    } else {
      console.error('❌ فشل في استدعاء الذكاء الاصطناعي');
      return '// فشل في توليد الكود بالذكاء الاصطناعي\nconsole.log("تم تحميل الموقع");';
    }
  } catch (error) {
    console.error('❌ خطأ في استدعاء الذكاء الاصطناعي:', error);
    return '// خطأ في توليد الكود بالذكاء الاصطناعي\nconsole.log("تم تحميل الموقع");';
  }
}

// دالة لتنظيف رد JavaScript من الذكاء الاصطناعي
function cleanAIJavaScriptResponse(response: string): string {
  try {
    console.log('🧹 تنظيف رد JavaScript من AI...');

    let cleanedResponse = response.trim();

    // إزالة النصوص التفسيرية في البداية
    const introPatterns = [
      /^.*?بالطبع[^:]*:/i,
      /^.*?هنا هو الكود[^:]*:/i,
      /^.*?إليك الكود[^:]*:/i,
      /^.*?الكود المطلوب[^:]*:/i,
      /^.*?يمكنك استخدام[^:]*:/i,
      /^.*?هذا هو الكود[^:]*:/i
    ];

    for (const pattern of introPatterns) {
      cleanedResponse = cleanedResponse.replace(pattern, '');
    }

    // البحث عن كود JavaScript داخل ```javascript blocks
    const jsCodeBlockMatch = cleanedResponse.match(/```javascript\s*([\s\S]*?)\s*```/i);
    if (jsCodeBlockMatch && jsCodeBlockMatch[1]) {
      console.log('✅ تم العثور على كود JavaScript داخل code block');
      return jsCodeBlockMatch[1].trim();
    }

    // البحث عن كود JavaScript داخل ``` blocks عامة
    const codeBlockMatch = cleanedResponse.match(/```\s*([\s\S]*?)\s*```/);
    if (codeBlockMatch && codeBlockMatch[1]) {
      console.log('✅ تم العثور على كود داخل code block عام');
      return codeBlockMatch[1].trim();
    }

    // إزالة علامات markdown
    cleanedResponse = cleanedResponse
      .replace(/```javascript\n?/g, '')
      .replace(/```\n?/g, '')
      .trim();

    // إزالة النصوص التفسيرية في النهاية
    const outroPatterns = [
      /هذا الكود[\s\S]*$/i,
      /يعمل هذا[\s\S]*$/i,
      /ستحصل على[\s\S]*$/i,
      /بهذا الشكل[\s\S]*$/i,
      /ويعمل بشكل مثالي[\s\S]*$/i
    ];

    for (const pattern of outroPatterns) {
      cleanedResponse = cleanedResponse.replace(pattern, '');
    }

    console.log('✅ تم تنظيف الكود بنجاح');
    return cleanedResponse.trim();

  } catch (error) {
    console.error('❌ خطأ في تنظيف JavaScript:', error);
    return response.trim();
  }
}

// تم إزالة دوال التأثيرات المحددة مسبقاً - الآن نعتمد على الذكاء الاصطناعي فقط
function generateSmartTextEffect_REMOVED(prompt: string, elementId: string): string {
  const lowerPrompt = prompt.toLowerCase();
  const elementVar = `text_${elementId.replace(/[^a-zA-Z0-9]/g, '_')}`;

  // تحليل ذكي للكلمات المفتاحية
  const effects = [];

  // تأثيرات الحركة
  if (lowerPrompt.includes('يهتز') || lowerPrompt.includes('اهتزاز') || lowerPrompt.includes('shake')) {
    effects.push(`${elementVar}.style.animation = 'shake 0.5s infinite';`);
  }
  if (lowerPrompt.includes('يطفو') || lowerPrompt.includes('طفو') || lowerPrompt.includes('float')) {
    effects.push(`${elementVar}.style.animation = 'float 3s ease-in-out infinite';`);
  }
  if (lowerPrompt.includes('يدور') || lowerPrompt.includes('دوران') || lowerPrompt.includes('rotate')) {
    effects.push(`${elementVar}.style.animation = 'rotateIn 2s infinite';`);
  }
  if (lowerPrompt.includes('ينبض') || lowerPrompt.includes('نبض') || lowerPrompt.includes('pulse')) {
    effects.push(`${elementVar}.style.animation = 'pulse 2s infinite';`);
  }
  if (lowerPrompt.includes('يقفز') || lowerPrompt.includes('قفز') || lowerPrompt.includes('bounce')) {
    effects.push(`${elementVar}.style.animation = 'bounceIn 1s infinite';`);
  }
  if (lowerPrompt.includes('يتحرك') || lowerPrompt.includes('متحرك') || lowerPrompt.includes('slide')) {
    effects.push(`${elementVar}.style.animation = 'slide 5s linear infinite';`);
  }
  if (lowerPrompt.includes('يومض') || lowerPrompt.includes('وميض') || lowerPrompt.includes('blink')) {
    effects.push(`${elementVar}.style.animation = 'blink 1s infinite';`);
  }
  if (lowerPrompt.includes('يتموج') || lowerPrompt.includes('تموج') || lowerPrompt.includes('wave')) {
    effects.push(`${elementVar}.style.animation = 'colorWave 2s infinite';`);
  }

  // تأثيرات الألوان والفلاتر
  if (lowerPrompt.includes('قوس قزح') || lowerPrompt.includes('rainbow')) {
    effects.push(`${elementVar}.style.animation = 'rainbow 3s linear infinite';`);
  }
  if (lowerPrompt.includes('يتوهج') || lowerPrompt.includes('توهج') || lowerPrompt.includes('glow')) {
    effects.push(`${elementVar}.style.animation = 'glow 2s infinite';`);
  }
  if (lowerPrompt.includes('ضبابي') || lowerPrompt.includes('blur')) {
    effects.push(`${elementVar}.classList.add('filter-blur');`);
  }
  if (lowerPrompt.includes('رمادي') || lowerPrompt.includes('grayscale')) {
    effects.push(`${elementVar}.classList.add('filter-grayscale');`);
  }
  if (lowerPrompt.includes('سيبيا') || lowerPrompt.includes('sepia') || lowerPrompt.includes('قديم')) {
    effects.push(`${elementVar}.classList.add('filter-sepia');`);
  }

  // تأثيرات الحجم والشكل
  if (lowerPrompt.includes('يكبر') || lowerPrompt.includes('تكبير') || lowerPrompt.includes('zoom')) {
    effects.push(`${elementVar}.style.animation = 'zoom 2s infinite';`);
  }
  if (lowerPrompt.includes('يقلب') || lowerPrompt.includes('قلب') || lowerPrompt.includes('flip')) {
    effects.push(`${elementVar}.style.animation = 'flip 3s infinite';`);
  }

  // تأثيرات مخصصة
  if (lowerPrompt.includes('قلب') && lowerPrompt.includes('نبضة')) {
    effects.push(`${elementVar}.style.animation = 'heartbeat 1.5s infinite';`);
  }
  if (lowerPrompt.includes('يتمايل') || lowerPrompt.includes('تمايل')) {
    effects.push(`${elementVar}.style.animation = 'wiggle 2s infinite';`);
  }

  // إذا لم يتم العثور على تأثير محدد، استخدم تأثير عام
  if (effects.length === 0) {
    effects.push(`${elementVar}.style.animation = 'fadeIn 1s ease-in';`);
    effects.push(`${elementVar}.style.transition = 'all 0.3s ease';`);
    effects.push(`${elementVar}.style.transform = 'scale(1.05)';`);
    effects.push(`setTimeout(() => { ${elementVar}.style.transform = 'scale(1)'; }, 1000);`);
  }

  return effects.join('\\n            ');
}

// تم إزالة دوال التأثيرات المحددة مسبقاً - الآن نعتمد على الذكاء الاصطناعي فقط
function generateSmartImageEffect_REMOVED(prompt: string, elementId: string): string {
  const lowerPrompt = prompt.toLowerCase();
  const elementVar = `elem_${elementId.replace(/[^a-zA-Z0-9]/g, '_')}`;

  const effects = [];

  // تأثيرات الحركة للصور
  if (lowerPrompt.includes('يهتز') || lowerPrompt.includes('اهتزاز')) {
    effects.push(`${elementVar}.style.animation = 'shake 2s infinite';`);
  }
  if (lowerPrompt.includes('يطفو') || lowerPrompt.includes('طفو')) {
    effects.push(`${elementVar}.style.animation = 'float 4s ease-in-out infinite';`);
  }
  if (lowerPrompt.includes('يدور') || lowerPrompt.includes('دوران')) {
    effects.push(`${elementVar}.style.animation = 'rotateIn 3s infinite';`);
  }

  // تأثيرات الفلاتر للصور
  if (lowerPrompt.includes('ضبابي') || lowerPrompt.includes('blur')) {
    effects.push(`${elementVar}.style.filter = 'blur(3px)';`);
  }
  if (lowerPrompt.includes('مشرق') || lowerPrompt.includes('brightness')) {
    effects.push(`${elementVar}.style.filter = 'brightness(1.5)';`);
  }
  if (lowerPrompt.includes('قناع') || lowerPrompt.includes('mask')) {
    // بدلاً من استخدام ملف خارجي، نستخدم CSS mask
    effects.push(`${elementVar}.style.mask = 'radial-gradient(circle, black 60%, transparent 70%)';`);
    effects.push(`${elementVar}.style.webkitMask = 'radial-gradient(circle, black 60%, transparent 70%)';`);
  }
  if (lowerPrompt.includes('غيوم') || lowerPrompt.includes('clouds')) {
    // تأثير غيوم باستخدام CSS
    effects.push(`${elementVar}.style.background = 'linear-gradient(45deg, rgba(255,255,255,0.3) 25%, transparent 25%), linear-gradient(-45deg, rgba(255,255,255,0.3) 25%, transparent 25%)';`);
    effects.push(`${elementVar}.style.backgroundSize = '20px 20px';`);
    effects.push(`${elementVar}.style.animation = 'matrix 10s linear infinite';`);
  }

  if (effects.length === 0) {
    effects.push(`${elementVar}.style.animation = 'pulse 2s infinite';`);
  }

  return effects.join('\\n            ');
}

// تم إزالة الكود الاحتياطي - الآن نعتمد على الذكاء الاصطناعي فقط

// دالة تحليل المشروع
function analyzeProject(elements: any[], canvasSize: any) {
  const hasButtons = elements.some(el => el.type === 'button');
  const hasInputs = elements.some(el => el.type === 'input' || el.type === 'textarea');
  const hasImages = elements.some(el => el.type === 'image');
  const hasInteractiveElements = elements.some(el => el.properties?.prompt);

  return {
    projectType: hasInteractiveElements ? 'موقع تفاعلي' : 'موقع ثابت',
    hasButtons,
    hasInputs,
    hasImages,
    hasInteractiveElements,
    elementCount: elements.length,
    canvasSize
  };
}

// دالة توليد مشروع متعدد الصفحات
async function generateMultiPageProject(
  pages: any[],
  analysis: any,
  projectName: string,
  projectDescription: string,
  canvasSize: any = { width: 375, height: 667 },
  screenAnalysis?: any
): Promise<ProjectFile[]> {
  const files: ProjectFile[] = [];

  // توليد ملف لكل صفحة
  for (const page of pages) {
    const pageElements = page.elements || [];
    console.log(`📄 توليد صفحة: ${page.name} (${pageElements.length} عنصر)`);

    // 1. توليد HTML للصفحة مع دعم ارتفاع الصفحة المخصص
    const htmlContent = generateHTML(pageElements, analysis, {
      name: projectName,
      pageName: page.name,
      pageId: page.id,
      totalPages: pages.length,
      allPages: pages.map(p => ({ id: p.id, name: p.name })),
      pageProperties: page.properties || {},
      pageHeight: page.pageHeight || canvasSize.height // إضافة ارتفاع الصفحة
    }, canvasSize);

    const fileName = page.id === 'page_home' ? 'index.html' : `${page.name.toLowerCase().replace(/\s+/g, '-')}.html`;
    files.push({
      path: fileName,
      content: htmlContent
    });

    // 2. توليد JavaScript للصفحة (إذا كانت تحتوي على عناصر تفاعلية)
    const jsContent = await generateJavaScript(pageElements, analysis, canvasSize, {
      name: projectName,
      description: projectDescription,
      pageName: page.name,
      pageId: page.id
    }, pages); // تمرير معلومات جميع الصفحات

    if (jsContent && jsContent.trim().length > 100) { // فقط إذا كان هناك كود مفيد
      const jsFileName = page.id === 'page_home' ? 'script.js' : `${page.name.toLowerCase().replace(/\s+/g, '-')}.js`;
      files.push({
        path: jsFileName,
        content: jsContent
      });
    }
  }

  // 3. توليد CSS مشترك لجميع الصفحات مع دعم خصائص متعددة الصفحات وارتفاع مخصص
  const allElements = pages.flatMap(page => page.elements || []);
  const cssContent = generateMultiPageCSS(allElements, analysis, canvasSize, pages);
  files.push({
    path: 'styles.css',
    content: cssContent
  });

  // 4. توليد README
  const readmeContent = generateMultiPageReadme(projectName, projectDescription, analysis, pages);
  files.push({
    path: 'README.md',
    content: readmeContent
  });

  return files;
}

// دالة توليد README للمشاريع متعددة الصفحات
function generateMultiPageReadme(projectName: string, projectDescription: string, analysis: any, pages: any[]): string {
  return `# ${projectName || 'مشروع متعدد الصفحات'}

${projectDescription || 'هذا مشروع متعدد الصفحات تم إنشاؤه بواسطة AI Website Builder'}

## 📄 الصفحات

${pages.map(page => `- **${page.name}**: ${page.elements?.length || 0} عنصر`).join('\n')}

## 🎯 نوع المشروع

${analysis.projectType || 'موقع ويب تفاعلي'}

## 🚀 كيفية تشغيل المشروع

1. **فتح الملفات:**
   افتح ملف \`index.html\` في المتصفح

2. **التنقل بين الصفحات:**
   استخدم الروابط للتنقل بين الصفحات المختلفة

## 📁 بنية المشروع

${pages.map(page => {
  const fileName = page.id === 'page_home' ? 'index.html' : `${page.name.toLowerCase().replace(/\s+/g, '-')}.html`;
  return `- \`${fileName}\` - ${page.name}`;
}).join('\n')}
- \`styles.css\` - ملف التنسيقات المشترك
${pages.some(page => (page.elements || []).some((el: any) => el.properties?.prompt)) ? '- \`script.js\` - ملف JavaScript التفاعلي' : ''}

## 🔧 الميزات

- ${analysis.hasButtons ? '✅' : '❌'} أزرار تفاعلية
- ${analysis.hasInputs ? '✅' : '❌'} نماذج إدخال
- ${analysis.hasImages ? '✅' : '❌'} صور
- ${analysis.hasInteractiveElements ? '✅' : '❌'} عناصر تفاعلية

## 📱 التوافق

- متوافق مع جميع المتصفحات الحديثة
- تصميم متجاوب يعمل على الهاتف والكمبيوتر
- حجم Canvas: ${analysis.canvasSize?.width || 375}x${analysis.canvasSize?.height || 667}

---

**تم الإنشاء بواسطة:** AI Website Builder
**التاريخ:** ${new Date().toLocaleDateString('ar-SA')}
**عدد الصفحات:** ${pages.length}
`;
}

// دالة لتحليل الشاشات المستخدمة في المشروع
function analyzeUsedScreenSizes(elements: any[]): {
  usedScreens: string[];
  isResponsive: boolean;
  primaryScreen: string;
  canvasSize: { width: number; height: number };
} {
  const usedScreens = new Set<string>();
  let hasResponsivePositions = false;

  // تحليل العناصر للعثور على الشاشات المستخدمة
  elements.forEach(element => {
    // فحص المواضع المتجاوبة
    if (element.responsivePositions) {
      Object.keys(element.responsivePositions).forEach(screenKey => {
        if (element.responsivePositions[screenKey].customized) {
          usedScreens.add(screenKey);
          hasResponsivePositions = true;
        }
      });
    }

    // فحص الأحجام المتجاوبة
    if (element.responsiveSizes) {
      Object.keys(element.responsiveSizes).forEach(screenKey => {
        if (element.responsiveSizes[screenKey].customized) {
          usedScreens.add(screenKey);
          hasResponsivePositions = true;
        }
      });
    }
  });

  // تحديد الشاشة الأساسية والحجم (مبسط)
  const deviceSizes: { [key: string]: { width: number; height: number } } = {
    'mobile': { width: 375, height: 667 },
    'tablet': { width: 768, height: 1024 },
    'desktop': { width: 1280, height: 720 },
  };

  let primaryScreen = 'current'; // افتراضي
  let canvasSize = { width: 1280, height: 720 };

  if (usedScreens.size === 0) {
    // لا توجد مواضع متجاوبة - استخدم الحجم الحالي
    primaryScreen = 'current';
  } else if (usedScreens.size === 1) {
    // شاشة واحدة فقط
    primaryScreen = Array.from(usedScreens)[0];
    canvasSize = deviceSizes[primaryScreen] || canvasSize;
  } else {
    // عدة شاشات - اختر الأكبر كأساسي
    const sortedScreens = Array.from(usedScreens).sort((a, b) => {
      const sizeA = deviceSizes[a]?.width || 0;
      const sizeB = deviceSizes[b]?.width || 0;
      return sizeB - sizeA;
    });
    primaryScreen = sortedScreens[0];
    canvasSize = deviceSizes[primaryScreen] || canvasSize;
  }

  return {
    usedScreens: Array.from(usedScreens),
    isResponsive: hasResponsivePositions && usedScreens.size > 1,
    primaryScreen,
    canvasSize
  };
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { pages, elements, canvasSize, projectInfo } = body;

    // دعم النظام القديم (elements) والجديد (pages)
    let projectPages: any[] = [];

    if (pages && Array.isArray(pages)) {
      // نظام الصفحات المتعددة الجديد
      projectPages = pages;
      console.log('🚀 بدء توليد مشروع متعدد الصفحات...');
      console.log('📄 عدد الصفحات:', projectPages.length);
    } else if (elements && Array.isArray(elements)) {
      // النظام القديم - تحويل إلى صفحة واحدة
      projectPages = [{
        id: 'page_home',
        name: 'Home',
        elements: elements
      }];
      console.log('🚀 بدء توليد مشروع صفحة واحدة...');
    }

    // تحليل الشاشات المستخدمة في جميع الصفحات
    const allProjectElements = projectPages.flatMap(page => page.elements || []);
    const screenAnalysis = analyzeUsedScreenSizes(allProjectElements);

    console.log('📱 تحليل الشاشات:', {
      usedScreens: screenAnalysis.usedScreens,
      isResponsive: screenAnalysis.isResponsive,
      primaryScreen: screenAnalysis.primaryScreen,
      canvasSize: screenAnalysis.canvasSize
    });

    // تحديد حجم Canvas النهائي
    let finalCanvasSize = canvasSize;
    if (screenAnalysis.primaryScreen !== 'current') {
      finalCanvasSize = screenAnalysis.canvasSize;
      console.log('🎯 تم تحديد حجم Canvas تلقائياً:', finalCanvasSize);
    } else {
      return NextResponse.json({ error: 'لا توجد صفحات أو عناصر صالحة' }, { status: 400 });
    }

    console.log('📐 حجم Canvas النهائي:', finalCanvasSize);

    // تحليل المشروع (استخدام عناصر جميع الصفحات للتحليل)
    const allElements = projectPages.flatMap(page => page.elements || []);
    const analysis = analyzeProject(allElements, finalCanvasSize);
    console.log('🔍 تحليل المشروع:', analysis);

    // توليد ملفات المشروع
    const projectFiles = await generateMultiPageProject(
      projectPages,
      analysis,
      projectInfo?.name || 'مشروع متعدد الصفحات',
      projectInfo?.description || '',
      finalCanvasSize,
      screenAnalysis
    );

    // إرجاع الملفات
    return NextResponse.json({
      success: true,
      projectName: projectInfo?.name || 'multi-page-project',
      files: projectFiles,
      pagesGenerated: projectPages.length,
      message: `تم توليد المشروع بنجاح! (${projectPages.length} صفحة)`
    });

  } catch (error) {
    console.error('خطأ في توليد المشروع:', error);
    return NextResponse.json(
      { error: 'حدث خطأ في توليد المشروع' },
      { status: 500 }
    );
  }
}

async function generateCompleteProject(
  elements: any[],
  analysis: any,
  projectName: string,
  projectDescription: string,
  canvasSize: any = { width: 375, height: 667 }
): Promise<ProjectFile[]> {
  
  const files: ProjectFile[] = [];

  // 1. توليد HTML مع حجم ورقة العمل الدقيق
  const htmlContent = generateHTML(elements, analysis, projectName, canvasSize);
  files.push({ path: 'index.html', content: htmlContent });

  // 2. توليد CSS مع أبعاد دقيقة
  const cssContent = generateCSS(elements, analysis, canvasSize, [{ elements, properties: {} }], 'page_home');
  files.push({ path: 'styles.css', content: cssContent });

  // 3. توليد JavaScript مع معلومات التصميم والمشروع
  const jsContent = await generateJavaScript(elements, analysis, canvasSize, {
    name: projectName,
    description: projectDescription
  });
  files.push({ path: 'script.js', content: jsContent });

  // 4. توليد Backend (Node.js/Express)
  if (analysis.apiEndpoints && analysis.apiEndpoints.length > 0) {
    const serverContent = generateServer(analysis);
    files.push({ path: 'server.js', content: serverContent });

    const packageJsonContent = generatePackageJson(projectName, projectDescription);
    files.push({ path: 'package.json', content: packageJsonContent });
  }

  // 5. توليد قاعدة البيانات
  if (analysis.databaseSchema && analysis.databaseSchema.length > 0) {
    const dbContent = generateDatabase(analysis.databaseSchema);
    files.push({ path: 'database.sql', content: dbContent });
  }

  // 6. توليد README
  const readmeContent = generateReadme(projectName, projectDescription, analysis);
  files.push({ path: 'README.md', content: readmeContent });

  return files;
}

function generateHTML(elements: any[], analysis: any, projectInfo: any, canvasSize: any = { width: 375, height: 667 }): string {
  const projectName = typeof projectInfo === 'string' ? projectInfo : projectInfo?.name || 'موقع ويب';
  const pageName = projectInfo?.pageName || 'الصفحة الرئيسية';
  const pageId = projectInfo?.pageId || 'page_home';
  const allPages = projectInfo?.allPages || [];
  const pageProperties = projectInfo?.pageProperties || {};

  // تحديد اسم ملف JavaScript المناسب للصفحة
  const jsFileName = pageId === 'page_home' ? 'script.js' : `${pageName.toLowerCase().replace(/\s+/g, '-')}.js`;

  // جمع جميع الخطوط المستخدمة في المشروع
  const usedFonts = new Set<string>();

  // إضافة خط الصفحة الحالية
  if (pageProperties.fontFamily) {
    const fontName = pageProperties.fontFamily.replace(/['"]/g, '').split(',')[0].trim();
    if (fontName !== 'Arial' && fontName !== 'sans-serif' && fontName !== 'serif' && fontName !== 'monospace') {
      usedFonts.add(fontName);
    }
  }

  // إضافة خطوط الصفحات الأخرى (للمشاريع متعددة الصفحات)
  allPages.forEach((page: any) => {
    if (page.properties?.fontFamily) {
      const fontName = page.properties.fontFamily.replace(/['"]/g, '').split(',')[0].trim();
      if (fontName !== 'Arial' && fontName !== 'sans-serif' && fontName !== 'serif' && fontName !== 'monospace') {
        usedFonts.add(fontName);
      }
    }
  });

  // إنشاء روابط Google Fonts للخطوط المستخدمة
  let fontLinks = '';
  const googleFonts: { [key: string]: string } = {
    'Cairo': 'Cairo:wght@300;400;600;700',
    'Amiri': 'Amiri:wght@400;700',
    'Tajawal': 'Tajawal:wght@300;400;500;700',
    'Almarai': 'Almarai:wght@300;400;700;800',
    'Noto Sans Arabic': 'Noto+Sans+Arabic:wght@300;400;500;600;700',
    'Roboto': 'Roboto:wght@300;400;500;700',
    'Open Sans': 'Open+Sans:wght@300;400;600;700',
    'Lato': 'Lato:wght@300;400;700',
    'Montserrat': 'Montserrat:wght@300;400;500;600;700'
  };

  usedFonts.forEach(fontName => {
    if (googleFonts[fontName]) {
      fontLinks += `    <link href="https://fonts.googleapis.com/css2?family=${googleFonts[fontName]}&display=swap" rel="stylesheet">\n`;
    }
  });

  let html = `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageProperties.title || projectName || 'موقع ويب'}</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
${fontLinks}    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body data-page="${pageId}">
    <main class="main-content">
`;

  // توليد HTML للعناصر (فقط العناصر الجذر)
  const rootElements = elements.filter((element: any) => !element.parentId);
  rootElements.forEach((element: any) => {
    html += generateElementHTML(element, elements);
  });

  html += `
    </main>

    <footer class="footer">
        <p>تم إنشاء هذا المشروع بواسطة AI Website Builder</p>
    </footer>

    <script src="${jsFileName}"></script>
</body>
</html>`;

  return html;
}

// دالة مساعدة لتطبيق الروابط على العناصر
function wrapWithLink(html: string, link: string): string {
  if (!link || !link.trim()) return html;

  // التأكد من أن الرابط يحتوي على بروتوكول
  const finalLink = link.startsWith('http://') || link.startsWith('https://') || link.startsWith('mailto:') || link.startsWith('tel:')
    ? link
    : `https://${link}`;

  return `        <a href="${finalLink}" target="_blank" rel="noopener noreferrer" style="text-decoration: none; color: inherit; display: block;">\n${html}        </a>\n`;
}

function generateElementHTML(element: any, allElements: any[] = [], isChild: boolean = false): string {
  // التحقق من وجود كود مولد فردياً - إذا كان موجود، استخدم HTML منه
  if (element.properties?.generatedCode) {
    console.log(`🎯 استخدام HTML من الكود المولد فردياً للعنصر: ${element.id}`);
    const generatedHTML = extractHTMLFromGeneratedCode(element.properties.generatedCode, element);
    if (generatedHTML) {
      return generatedHTML;
    }
  }

  // التحقق من إذا كان هذا العنصر طفل لحاوي Flex ويحتاج flex-grow
  const parentElement = allElements.find(el => el.id === element.parentId);
  const isChildOfFlexContainer = parentElement && parentElement.type === 'container' && parentElement.properties.layoutType?.startsWith('flex');
  const needsFlexGrow = isChildOfFlexContainer;

  // إنشاء الأنماط الدقيقة من المحرر
  const exactStyles = isChild ? {
    // للعناصر الأطفال: بدون position absolute
    width: needsFlexGrow && element.type === 'container' ? 'auto' :
           needsFlexGrow ? '100%' : `${element.width}px`, // للحاويات: auto، للعناصر: 100%
    height: needsFlexGrow && element.type === 'container' ? 'auto' : `${element.height}px`, // للحاويات المتداخلة: auto
    fontSize: `${element.properties.fontSize || 14}px`,
    color: element.properties.color || '#000000',
    backgroundColor: element.properties.backgroundColor || 'transparent',
    borderRadius: `${element.properties.borderRadius || 4}px`,
    padding: `${element.properties.padding || 8}px`,
    margin: `${element.properties.margin || 0}px`,
    maxWidth: '100%',
    flexShrink: needsFlexGrow ? 1 : 0, // السماح بالانكماش في Flex
    zIndex: element.zIndex || 0,
    // إضافة flex-grow لجميع العناصر في Flex container
    flexGrow: needsFlexGrow ? 1 : undefined,
    flexBasis: needsFlexGrow ? 'auto' : undefined
  } : {
    // للعناصر الجذر: مع position absolute
    position: 'absolute',
    left: `${element.x}px`,
    top: `${element.y}px`,
    width: `${element.width}px`,
    height: `${element.height}px`,
    fontSize: `${element.properties.fontSize || 14}px`,
    color: element.properties.color || '#000000',
    backgroundColor: element.properties.backgroundColor || 'transparent',
    borderRadius: `${element.properties.borderRadius || 4}px`,
    padding: `${element.properties.padding || 8}px`,
    margin: `${element.properties.margin || 0}px`,
    maxWidth: '100%',
    zIndex: element.zIndex || 0
  };

  const styleString = Object.entries(exactStyles)
    .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
    .join('; ');

  const responsiveStyle = `style="${styleString}"`;

  switch (element.type) {
    case 'button':
      const buttonStyles = {
        ...exactStyles,
        backgroundColor: element.properties.backgroundColor || '#3b82f6',
        color: element.properties.color || '#ffffff',
        border: 'none',
        cursor: 'pointer',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontWeight: '600',
        textAlign: 'center',
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap'
      };

      // إضافة !important للألوان المخصصة لضمان تطبيقها
      const buttonStyleString = Object.entries(buttonStyles)
        .map(([key, value]) => {
          const cssKey = key.replace(/([A-Z])/g, '-$1').toLowerCase();
          // إضافة !important للألوان المخصصة
          if ((cssKey === 'background-color' || cssKey === 'color') && element.properties[key]) {
            return `${cssKey}: ${value} !important`;
          }
          return `${cssKey}: ${value}`;
        })
        .join('; ');

      // تحديد كلاس الزر حسب الوظيفة - مع مراعاة الألوان المخصصة
      const buttonPrompt = element.properties.prompt || '';
      const lowerPrompt = buttonPrompt.toLowerCase();
      const hasCustomColors = element.properties.backgroundColor || element.properties.color;

      let buttonClass = 'btn element-button';

      // إذا كان هناك ألوان مخصصة، لا نستخدم classes ملونة
      if (!hasCustomColors) {
        if (lowerPrompt.includes('واتساب') || lowerPrompt.includes('whatsapp') || lowerPrompt.includes('واتس')) {
          buttonClass += ' btn-whatsapp';
        } else if (lowerPrompt.includes('ايميل') || lowerPrompt.includes('email') || lowerPrompt.includes('بريد')) {
          buttonClass += ' btn-email';
        } else if (lowerPrompt.includes('اتصال') || lowerPrompt.includes('هاتف') || lowerPrompt.includes('call') || lowerPrompt.includes('phone')) {
          buttonClass += ' btn-call';
        } else {
          buttonClass += ' btn-primary';
        }
      } else {
        // للألوان المخصصة، نستخدم class أساسي فقط
        console.log('🎨 استخدام ألوان مخصصة للزر ' + element.id + ': خلفية=' + element.properties.backgroundColor + ', نص=' + element.properties.color);
      }

      const buttonHTML = `        <button id="${element.id}" class="${buttonClass}" style="${buttonStyleString}" data-text="${element.properties.text || 'زر'}" data-prompt="${buttonPrompt}" title="${buttonPrompt || element.properties.text || 'زر'}">${element.properties.text || 'زر'}</button>\n`;
      return wrapWithLink(buttonHTML, element.properties.link);

    case 'input':
      const inputContainerStyles = {
        ...exactStyles,
        backgroundColor: element.properties.backgroundColor || 'rgba(255,255,255,0.9)',
        borderRadius: `${element.properties.borderRadius || 8}px`,
        padding: `${element.properties.padding || 12}px`,
        display: 'flex',
        flexDirection: 'column'
      };

      const inputStyles = {
        width: '100%',
        height: element.properties.label ? `${element.height - 25}px` : `${element.height - 16}px`,
        fontSize: `${Math.min(element.properties.fontSize || 14, element.height / 3)}px`,
        color: element.properties.color || '#000000',
        backgroundColor: '#ffffff',
        border: '2px solid #e1e8ed',
        borderRadius: `${element.properties.borderRadius || 6}px`,
        padding: `${Math.min(8, element.height / 6)}px`,
        outline: 'none'
      };

      const containerStyleString = Object.entries(inputContainerStyles)
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
        .join('; ');

      const inputStyleString = Object.entries(inputStyles)
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
        .join('; ');

      return `        <div class="form-group element-input" style="${containerStyleString}" data-prompt="${element.properties.prompt || ''}">
            ${element.properties.label ? `<label for="${element.id}" class="form-label" style="font-size: ${Math.min(12, element.height / 6)}px; margin-bottom: 4px; color: ${element.properties.color || '#374151'};">${element.properties.label}</label>` : ''}
            <input
                type="${element.properties.inputType || 'text'}"
                id="${element.id}"
                class="form-control"
                style="${inputStyleString}"
                placeholder="${element.properties.placeholder || 'أدخل النص...'}"
                ${element.properties.required ? 'required' : ''}
            />
        </div>\n`;

    case 'textarea':
      const textareaLabel = element.properties.title || element.properties.label || 'منطقة نص';
      const textareaValue = element.properties.text || '';

      return `        <div class="form-group element-textarea" ${responsiveStyle}>
            <label for="${element.id}" class="form-label">${textareaLabel}</label>
            <textarea
                id="${element.id}"
                class="form-control"
                placeholder="${element.properties.placeholder || 'أدخل النص الطويل...'}"
                rows="4"
                ${element.properties.required ? 'required' : ''}
            >${textareaValue}</textarea>
        </div>\n`;

    case 'select':
      const selectStyles = {
        ...exactStyles,
        border: '2px solid #e5e7eb',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        cursor: (element.properties.prompt && element.properties.prompt.trim() !== '') ? 'pointer' : 'default'
      };

      const selectStyleString = Object.entries(selectStyles)
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
        .join('; ');

      const selectIcon = element.properties.icon || '🔽';
      const selectTitle = element.properties.title || element.properties.label || 'قائمة اختيار';
      const selectDescription = element.properties.description || 'اختر من الخيارات المتاحة';
      const options = element.properties.options || ['الخيار الأول', 'الخيار الثاني', 'الخيار الثالث'];

      return `        <div id="${element.id}" class="select-element" style="${selectStyleString}" data-prompt="${element.properties.prompt || ''}">
            <div class="select-header" style="margin-bottom: 8px;">
                <h4 style="margin: 0 0 4px 0; font-size: ${Math.min(16, element.height / 6)}px; color: ${element.properties.color || '#2563eb'};">${selectIcon} ${selectTitle}</h4>
                <p style="margin: 0; font-size: ${Math.min(12, element.height / 8)}px; color: #6b7280;">${selectDescription}</p>
            </div>
            <select class="form-control" style="width: 100%; padding: 8px; border: 1px solid #d1d5db; border-radius: 6px; font-size: ${Math.min(14, element.height / 8)}px;">
                <option value="" disabled selected>اختر من القائمة...</option>
                ${options.map((opt: string) => `<option value="${opt}">${opt}</option>`).join('')}
            </select>
        </div>\n`;

    case 'table':
      const columns = element.properties.columns || ['العمود الأول', 'العمود الثاني', 'العمود الثالث'];
      const rows = element.properties.rows || [
        ['بيانات 1', 'بيانات 2', 'بيانات 3'],
        ['بيانات 4', 'بيانات 5', 'بيانات 6']
      ];
      return `        <div class="table-container element-table" ${responsiveStyle}>
            <table id="${element.id}" class="data-table">
                <thead>
                    <tr>${columns.map((col: string) => `<th>${col}</th>`).join('')}</tr>
                </thead>
                <tbody>
                    ${rows.map((row: string[]) =>
                      `<tr>${row.map(cell => `<td>${cell}</td>`).join('')}</tr>`
                    ).join('')}
                </tbody>
            </table>
        </div>\n`;

    case 'text':
      const textContent = element.properties.text || 'نص تجريبي';
      const textStyles = {
        ...exactStyles,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        wordWrap: 'break-word',
        overflow: 'hidden',
        cursor: (element.properties.prompt && element.properties.prompt.trim() !== '') ? 'pointer' : 'default'
      };

      const textStyleString = Object.entries(textStyles)
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
        .join('; ');

      const textHTML = `        <div id="${element.id}" class="text-element" style="${textStyleString}" data-prompt="${element.properties.prompt || ''}">
            ${textContent}
        </div>\n`;
      return wrapWithLink(textHTML, element.properties.link);

    case 'div':
      const divStyles = {
        ...exactStyles,
        border: '2px dashed #dee2e6',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        cursor: (element.properties.prompt && element.properties.prompt.trim() !== '') ? 'pointer' : 'default'
      };

      const divStyleString = Object.entries(divStyles)
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
        .join('; ');

      const divIcon = element.properties.icon || '📦';
      const divTitle = element.properties.title || 'حاوي';
      const divDescription = element.properties.description || element.properties.text || 'منطقة محتوى تفاعلية';

      const divHTML = `        <div id="${element.id}" class="container-element" style="${divStyleString}" data-prompt="${element.properties.prompt || ''}">
            <div class="container-content">
                <h4 style="margin: 0 0 8px 0; font-size: ${Math.min(16, element.height / 6)}px;">${divIcon} ${divTitle}</h4>
                <p style="margin: 0; font-size: ${Math.min(12, element.height / 8)}px;">${divDescription}</p>
            </div>
        </div>\n`;
      return wrapWithLink(divHTML, element.properties.link);

    case 'container':
      // الحصول على العناصر الأطفال
      const childElements = allElements.filter((child: any) => child.parentId === element.id);

      // تحديد نوع التخطيط
      const layoutType = element.properties.layoutType || 'flex-row';
      const justifyContent = element.properties.justifyContent || 'flex-start';
      const alignItems = element.properties.alignItems || 'stretch'; // توحيد مع المحرر
      const gap = element.properties.gap || 8;
      const gridColumns = element.properties.gridColumns || 2;
      const gridRows = element.properties.gridRows || 2;

      // إنشاء CSS للحاوي - استخدام التخطيط الافتراضي (desktop) للـ inline styles
      // التخطيط المتجاوب سيتم إضافته في CSS منفصل
      const defaultLayout = element.properties.responsiveLayout?.desktop || {
        layoutType: layoutType,
        justifyContent: justifyContent,
        alignItems: alignItems,
        gap: gap
      };

      // ملاحظة: parentElement تم تعريفه بالفعل أعلاه

      const containerStyles = {
        ...exactStyles,
        display: defaultLayout.layoutType === 'grid' ? 'grid' :
                defaultLayout.layoutType === 'absolute' ? 'relative' : 'flex', // إصلاح: استخدم 'flex' للحالات الأخرى
        flexDirection: defaultLayout.layoutType === 'flex-col' ? 'column' : 'row',
        justifyContent: defaultLayout.layoutType.startsWith('flex') ? defaultLayout.justifyContent : undefined,
        alignItems: defaultLayout.layoutType.startsWith('flex') ? defaultLayout.alignItems : undefined,
        gap: defaultLayout.layoutType !== 'absolute' ? `${defaultLayout.gap}px` : undefined,
        gridTemplateColumns: defaultLayout.layoutType === 'grid' ? `repeat(${defaultLayout.gridColumns || gridColumns}, 1fr)` : undefined,
        gridTemplateRows: defaultLayout.layoutType === 'grid' ? `repeat(${defaultLayout.gridRows || gridRows}, 1fr)` : undefined,
        justifyItems: defaultLayout.layoutType === 'grid' ? (defaultLayout.justifyItems || 'stretch') : undefined,
        alignContent: defaultLayout.layoutType === 'grid' ? (defaultLayout.alignContent || 'start') : undefined,
        backgroundColor: element.properties.backgroundColor || 'transparent',
        borderRadius: `${element.properties.borderRadius || 0}px`,
        padding: `${element.properties.padding || 8}px`,
        border: element.properties.border || 'none',
        overflow: 'hidden'
      };

      const layoutContainerStyleString = Object.entries(containerStyles)
        .filter(([_, value]) => value !== undefined)
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
        .join('; ');

      let containerHTML = `        <div id="${element.id}" class="layout-container" style="${layoutContainerStyleString}" data-prompt="${element.properties.prompt || ''}">\n`;

      // إضافة العناصر الأطفال
      childElements.forEach((child: any) => {
        if (layoutType === 'absolute') {
          // في التخطيط المطلق، استخدم المواضع الأصلية للأطفال
          const childStyles = {
            position: 'absolute',
            left: `${child.x}px`,
            top: `${child.y}px`,
            width: `${child.width}px`,
            height: `${child.height}px`
          };
          const childStyleString = Object.entries(childStyles)
            .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
            .join('; ');

          containerHTML += `            <div style="${childStyleString}">\n`;
          containerHTML += generateElementHTML(child, allElements, true).replace(/^        /, '                ');
          containerHTML += `            </div>\n`;
        } else {
          // في التخطيطات الأخرى، أضف دعم flex-grow للتمدد
          let childWrapperStyles = '';

          if (layoutType === 'grid' && child.type !== 'container') {
            // في Grid، العناصر العادية تحتاج wrapper للتوسيط
            childWrapperStyles = 'style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center;"';
            containerHTML += `            <div ${childWrapperStyles}>\n`;
            containerHTML += generateElementHTML(child, allElements, true).replace(/^        /, '                ');
            containerHTML += `            </div>\n`;
          } else {
            // في Flex أو للحاويات، استخدم العنصر مباشرة (flex-grow في exactStyles)
            containerHTML += generateElementHTML(child, allElements, true).replace(/^        /, '            ');
          }
        }
      });

      containerHTML += `        </div>\n`;
      return containerHTML;

    case 'image':
      const imageUrl = element.properties.imageUrl || '';
      const imageMode = element.properties.imageMode || 'cover';

      // تحديد CSS background properties حسب نمط الصورة
      const getImageBackgroundStyle = () => {
        if (!imageUrl) return '';

        switch (imageMode) {
          case 'cover':
            return `background-image: url(${imageUrl}); background-size: cover; background-position: center; background-repeat: no-repeat;`;
          case 'contain':
            return `background-image: url(${imageUrl}); background-size: contain; background-position: center; background-repeat: no-repeat;`;
          case 'repeat':
            return `background-image: url(${imageUrl}); background-size: auto; background-position: top left; background-repeat: repeat;`;
          case 'stretch':
            return `background-image: url(${imageUrl}); background-size: 100% 100%; background-position: center; background-repeat: no-repeat;`;
          default:
            return '';
        }
      };

      const imageStyles = {
        ...exactStyles,
        border: '2px solid #dee2e6',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: element.properties.backgroundColor || '#f8f9fa',
        cursor: (element.properties.prompt && element.properties.prompt.trim() !== '') ? 'pointer' : 'default'
      };

      const imageStyleString = Object.entries(imageStyles)
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
        .join('; ');

      const backgroundStyle = getImageBackgroundStyle();
      const fullImageStyle = backgroundStyle ? `${imageStyleString}; ${backgroundStyle}` : imageStyleString;

      const imageHTML = `        <div id="${element.id}" class="image-element" style="${fullImageStyle}" data-prompt="${element.properties.prompt || ''}" data-image-url="${imageUrl}" data-image-mode="${imageMode}">
            ${!imageUrl ? `<div class="image-placeholder" style="text-align: center; color: #6c757d;">
                <span class="image-icon" style="font-size: ${Math.min(48, element.height / 3)}px; display: block; margin-bottom: 8px;">🖼️</span>
                <p style="margin: 0; font-size: ${Math.min(14, element.height / 8)}px;">صورة تفاعلية</p>
            </div>` : ''}
        </div>\n`;
      return wrapWithLink(imageHTML, element.properties.link);

    case 'form':
      const formStyles = {
        ...exactStyles,
        backgroundColor: element.properties.backgroundColor || '#f8f9fa',
        border: '1px solid #dee2e6',
        display: 'flex',
        flexDirection: 'column'
      };

      const formStyleString = Object.entries(formStyles)
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
        .join('; ');

      const formIcon = element.properties.icon || '📋';
      const formTitle = element.properties.title || 'نموذج تفاعلي';
      const formDescription = element.properties.description || 'منطقة النموذج';

      return `        <div id="${element.id}" class="form-container element-form" style="${formStyleString}" data-prompt="${element.properties.prompt || ''}">
            <div class="form-header" style="padding: 12px; border-bottom: 1px solid #dee2e6;">
                <h3 style="margin: 0; font-size: ${Math.min(18, element.height / 8)}px; color: ${element.properties.color || '#2c3e50'};">${formIcon} ${formTitle}</h3>
            </div>
            <div class="form-body" style="padding: 12px; flex: 1; display: flex; align-items: center; justify-content: center;">
                <p style="margin: 0; font-size: ${Math.min(14, element.height / 10)}px; color: #6c757d;">${formDescription}</p>
            </div>
        </div>\n`;

    case 'content':
      const contentStyles = {
        ...exactStyles,
        border: '2px dashed #dee2e6',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center',
        cursor: (element.properties.prompt && element.properties.prompt.trim() !== '') ? 'pointer' : 'default'
      };

      const contentStyleString = Object.entries(contentStyles)
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
        .join('; ');

      const contentIcon = element.properties.icon || '📄';
      const contentTitle = element.properties.title || 'منطقة محتوى';
      const contentDescription = element.properties.description || element.properties.text || 'منطقة محتوى تفاعلية';

      return `        <div id="${element.id}" class="content-element" style="${contentStyleString}" data-prompt="${element.properties.prompt || ''}">
            <div class="content-content">
                <h4 style="margin: 0 0 8px 0; font-size: ${Math.min(16, element.height / 6)}px;">${contentIcon} ${contentTitle}</h4>
                <p style="margin: 0; font-size: ${Math.min(12, element.height / 8)}px;">${contentDescription}</p>
            </div>
        </div>\n`;

    case 'circle':
      const circleStyles = {
        ...exactStyles,
        backgroundColor: element.properties.backgroundColor || '#3b82f6',
        color: element.properties.color || '#ffffff',
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: Math.min(element.properties.fontSize || 16, element.height * 0.4) + 'px',
        fontWeight: 'bold'
      };

      const circleStyleString = Object.entries(circleStyles)
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
        .join('; ');

      const circleHTML = `        <div id="${element.id}" class="circle-element" style="${circleStyleString}" data-prompt="${element.properties.prompt || ''}">
            ${element.properties.text || ''}
        </div>\n`;
      return wrapWithLink(circleHTML, element.properties.link);

    case 'square':
      const squareStyles = {
        ...exactStyles,
        backgroundColor: element.properties.backgroundColor || '#ef4444',
        color: element.properties.color || '#ffffff',
        borderRadius: '0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: Math.min(element.properties.fontSize || 16, element.height * 0.4) + 'px',
        fontWeight: 'bold'
      };

      const squareStyleString = Object.entries(squareStyles)
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
        .join('; ');

      const squareHTML = `        <div id="${element.id}" class="square-element" style="${squareStyleString}" data-prompt="${element.properties.prompt || ''}">
            ${element.properties.text || ''}
        </div>\n`;
      return wrapWithLink(squareHTML, element.properties.link);

    case 'arrow':
      const arrowStyles = {
        ...exactStyles,
        backgroundColor: element.properties.backgroundColor || '#10b981',
        color: element.properties.color || '#ffffff',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: Math.min(element.properties.fontSize || 24, element.height * 0.8) + 'px',
        fontWeight: 'bold'
      };

      const arrowStyleString = Object.entries(arrowStyles)
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
        .join('; ');

      const arrowHTML = `        <div id="${element.id}" class="arrow-element" style="${arrowStyleString}" data-prompt="${element.properties.prompt || ''}">
            ${element.properties.text || '→'}
        </div>\n`;
      return wrapWithLink(arrowHTML, element.properties.link);



    case 'star':
      const starStyles = {
        ...exactStyles,
        backgroundColor: element.properties.backgroundColor || '#8b5cf6',
        color: element.properties.color || '#ffffff',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        fontSize: Math.min(element.properties.fontSize || 32, element.height * 0.8) + 'px',
        fontWeight: 'bold'
      };

      const starStyleString = Object.entries(starStyles)
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
        .join('; ');

      const starHTML = `        <div id="${element.id}" class="star-element" style="${starStyleString}" data-prompt="${element.properties.prompt || ''}">
            ${element.properties.text || '★'}
        </div>\n`;
      return wrapWithLink(starHTML, element.properties.link);

    default:
      // بدلاً من "عنصر غير معروف"، ننشئ عنصر div عام
      const unknownStyles = {
        ...exactStyles,
        backgroundColor: element.properties.backgroundColor || '#e3f2fd',
        border: '2px solid #2196f3',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        textAlign: 'center'
      };

      const unknownStyleString = Object.entries(unknownStyles)
        .map(([key, value]) => `${key.replace(/([A-Z])/g, '-$1').toLowerCase()}: ${value}`)
        .join('; ');

      const unknownHTML = `        <div id="${element.id}" class="custom-element" style="${unknownStyleString}" data-prompt="${element.properties.prompt || ''}">
            <div class="custom-content">
                <span style="font-size: ${Math.min(24, element.height / 4)}px; display: block; margin-bottom: 8px;">⭐</span>
                <p style="margin: 0; font-size: ${Math.min(14, element.height / 6)}px; font-weight: 600;">عنصر مخصص</p>
                <small style="font-size: ${Math.min(12, element.height / 8)}px; color: #666;">نوع: ${element.type}</small>
            </div>
        </div>\n`;

      return wrapWithLink(unknownHTML, element.properties.link);
  }
}

// دالة لاستخراج HTML من الكود المولد فردياً
function extractHTMLFromGeneratedCode(generatedCode: string, element: any): string {
  try {
    console.log(`🔍 استخراج HTML للعنصر: ${element.id}`);

    // البحث عن العنصر الرئيسي في body أو مباشرة في الكود
    let content = '';

    // أولاً: البحث في body
    const bodyMatch = generatedCode.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
    if (bodyMatch && bodyMatch[1]) {
      content = bodyMatch[1].trim();
    } else {
      // إذا لم يوجد body، استخدم الكود كاملاً
      content = generatedCode;
    }

    console.log(`📝 محتوى للبحث: ${content.substring(0, 200)}...`);

    // البحث عن العنصر الرئيسي بطريقة ديناميكية
    const elementWithIdMatch = content.match(/<(\w+)[^>]*id="[^"]*"[^>]*>/i);

    if (elementWithIdMatch) {
      const tagName = elementWithIdMatch[1];
      const startTag = elementWithIdMatch[0];

      console.log(`🏷️ نوع العنصر: ${tagName}`);

      // استخراج العنصر الكامل مع جميع العناصر الفرعية
      let elementHTML = extractCompleteElement(content, tagName, startTag);

      if (elementHTML) {
        // تحديث المعرف ليتطابق مع معرف العنصر الفعلي
        elementHTML = elementHTML.replace(/id="[^"]*"/, `id="${element.id}"`);

        // إضافة الموضع والحجم للعنصر الرئيسي فقط
        const positionStyle = `position: absolute; left: ${element.x}px; top: ${element.y}px; width: ${element.width}px; height: ${element.height}px;`;
        elementHTML = addPositionToMainElement(elementHTML, positionStyle, element.id);

        console.log(`✅ HTML مستخرج: ${elementHTML.substring(0, 100)}...`);
        return `        ${elementHTML}\n`;
      }
    }

    console.log('❌ لم يتم العثور على HTML صالح');
  } catch (error) {
    console.error('❌ خطأ في استخراج HTML:', error);
  }

  return '';
}

// دالة محسنة لاستخراج العنصر الكامل مع جميع العناصر الفرعية
function extractCompleteElement(content: string, tagName: string, startTag: string): string {
  const startIndex = content.indexOf(startTag);
  if (startIndex === -1) return '';

  // العناصر المغلقة ذاتياً
  const selfClosingTags = ['input', 'img', 'br', 'hr', 'meta', 'link'];
  if (selfClosingTags.includes(tagName.toLowerCase()) || startTag.endsWith('/>')) {
    return startTag;
  }

  let currentIndex = startIndex;
  let depth = 0;
  let inTag = false;
  let inString = false;
  let stringChar = '';
  let tagBuffer = '';

  while (currentIndex < content.length) {
    const char = content[currentIndex];

    if (!inString) {
      if (char === '"' || char === "'") {
        inString = true;
        stringChar = char;
      } else if (char === '<') {
        inTag = true;
        tagBuffer = '<';
      } else if (char === '>' && inTag) {
        tagBuffer += '>';
        inTag = false;

        // تحليل التاغ
        const isClosingTag = tagBuffer.startsWith('</');
        const isSelfClosing = tagBuffer.endsWith('/>');
        const tagMatch = tagBuffer.match(/<\/?(\w+)/);

        if (tagMatch) {
          const currentTagName = tagMatch[1];

          if (currentTagName.toLowerCase() === tagName.toLowerCase()) {
            if (isClosingTag) {
              depth--;
              if (depth === 0) {
                return content.substring(startIndex, currentIndex + 1);
              }
            } else if (!isSelfClosing) {
              depth++;
            }
          }
        }

        tagBuffer = '';
      } else if (inTag) {
        tagBuffer += char;
      }
    } else {
      if (char === stringChar && content[currentIndex - 1] !== '\\') {
        inString = false;
        stringChar = '';
      }
    }

    currentIndex++;
  }

  // إذا لم نجد closing tag، نأخذ كل شيء حتى نهاية المحتوى
  return content.substring(startIndex);
}

// دالة لإضافة الموضع للعنصر الرئيسي فقط
function addPositionToMainElement(elementHTML: string, positionStyle: string, elementId: string): string {
  // البحث عن العنصر الرئيسي (الذي له id المحدد)
  const mainElementRegex = new RegExp(`(<[^>]*id="${elementId}"[^>]*)(>)`, 'i');
  const match = elementHTML.match(mainElementRegex);

  if (match) {
    const beforeClosing = match[1];
    const closing = match[2];

    // إضافة أو تحديث style attribute
    if (beforeClosing.includes('style="')) {
      elementHTML = elementHTML.replace(
        new RegExp(`(<[^>]*id="${elementId}"[^>]*style=")([^"]*)(")`, 'i'),
        `$1${positionStyle} $2$3`
      );
    } else {
      elementHTML = elementHTML.replace(
        mainElementRegex,
        `$1 style="${positionStyle}"$2`
      );
    }
  }

  return elementHTML;
}

// دالة لاستخراج CSS من الكود المولد فردياً
function extractCSSFromGeneratedCode(generatedCode: string, elementId: string): string {
  try {
    console.log(`🎨 استخراج CSS للعنصر: ${elementId}`);

    // البحث عن تاغ style في الكود
    const styleMatch = generatedCode.match(/<style[^>]*>([\s\S]*?)<\/style>/i);
    if (styleMatch && styleMatch[1]) {
      let cssCode = styleMatch[1].trim();

      // تحديث معرفات العناصر في CSS لتتطابق مع المعرف الفعلي
      const idMatches = generatedCode.match(/id="([^"]+)"/g);
      if (idMatches) {
        idMatches.forEach(match => {
          const originalId = match.match(/id="([^"]+)"/)?.[1];
          if (originalId && originalId !== elementId) {
            console.log(`🔄 استبدال المعرف في CSS: ${originalId} → ${elementId}`);
            // استبدال المعرف في CSS
            cssCode = cssCode.replace(new RegExp(`#${originalId.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'g'), `#${elementId}`);
            cssCode = cssCode.replace(new RegExp(`"${originalId.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}"`, 'g'), `"${elementId}"`);
          }
        });
      }

      // تنظيف CSS لتجنب تعارض مع التصميم الأساسي
      cssCode = cleanGeneratedCSS(cssCode, elementId);

      console.log(`✅ CSS مستخرج ومنظف: ${cssCode.substring(0, 100)}...`);
      return `/* CSS للعنصر المولد فردياً: ${elementId} */\n${cssCode}`;
    } else {
      console.log('❌ لم يتم العثور على تاغ style');
    }
  } catch (error) {
    console.error('❌ خطأ في استخراج CSS:', error);
  }

  return '';
}

// دالة لتنظيف CSS المولد فردياً لتجنب التعارض مع التصميم الأساسي
function cleanGeneratedCSS(cssCode: string, elementId: string): string {
  try {
    console.log(`🧹 تنظيف CSS للعنصر: ${elementId}`);

    let cleanedCSS = cssCode;

    // إزالة تعريفات body التي تتعارض مع التصميم الأساسي
    cleanedCSS = cleanedCSS.replace(/body\s*{[^}]*}/gi, '');

    // إزالة تعريفات html التي قد تتعارض
    cleanedCSS = cleanedCSS.replace(/html\s*{[^}]*}/gi, '');

    // إزالة تعريفات * (universal selector) التي قد تتعارض
    cleanedCSS = cleanedCSS.replace(/\*\s*{[^}]*}/gi, '');

    // إزالة تعريفات container عامة قد تتعارض
    cleanedCSS = cleanedCSS.replace(/\.container\s*{[^}]*}/gi, '');

    // إزالة أي تعريفات position: fixed أو absolute على body
    cleanedCSS = cleanedCSS.replace(/body[^{]*{[^}]*position\s*:\s*(fixed|absolute)[^}]*}/gi, '');

    // إزالة تعريفات height: 100vh على body
    cleanedCSS = cleanedCSS.replace(/body[^{]*{[^}]*height\s*:\s*100vh[^}]*}/gi, '');

    // تنظيف الأسطر الفارغة المتعددة
    cleanedCSS = cleanedCSS.replace(/\n\s*\n\s*\n/g, '\n\n');

    console.log(`✅ تم تنظيف CSS بنجاح`);
    return cleanedCSS.trim();

  } catch (error) {
    console.error('❌ خطأ في تنظيف CSS:', error);
    return cssCode;
  }
}

// دالة توليد CSS متعدد الصفحات مع دعم خصائص مختلفة لكل صفحة
function generateMultiPageCSS(elements: any[], analysis: any, canvasSize: any = { width: 375, height: 667 }, pages: any[] = []): string {
  const isDesktop = canvasSize.width >= 1024;
  const isTablet = canvasSize.width >= 768 && canvasSize.width < 1024;
  const isMobile = canvasSize.width < 768;

  // استخراج CSS من العناصر المولدة فردياً
  let individualCSS = '';
  elements.forEach(element => {
    if (element.properties?.generatedCode) {
      console.log(`🎨 استخراج CSS للعنصر: ${element.id}`);
      const extractedCSS = extractCSSFromGeneratedCode(element.properties.generatedCode, element.id);
      if (extractedCSS) {
        individualCSS += extractedCSS + '\n\n';
      }
    }
  });

  // استخراج خصائص الصفحة الرئيسية للأنماط العامة
  const mainPage = pages.find(p => p.id === 'page_home') || pages[0];
  const mainPageProperties = mainPage?.properties || {};

  // إنشاء CSS أساسي
  let css = `/* تم إنشاء هذا الملف بواسطة AI Website Builder */
/* أبعاد ورقة العمل: ${canvasSize.width}×${canvasSize.height}px */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: ${mainPageProperties.fontFamily ? mainPageProperties.fontFamily : "'Cairo', Arial, sans-serif"};
    background: ${mainPageProperties.canvasBackgroundColor || 'white'};
    ${mainPageProperties.backgroundImage ? `background-image: url('${mainPageProperties.backgroundImage}');` : ''}
    ${mainPageProperties.backgroundImage ? `background-size: ${mainPageProperties.backgroundSize || 'cover'};` : ''}
    ${mainPageProperties.backgroundImage ? `background-position: ${mainPageProperties.backgroundPosition || 'center'};` : ''}
    ${mainPageProperties.backgroundImage ? `background-repeat: ${mainPageProperties.backgroundRepeat || 'no-repeat'};` : ''}
    min-height: 100vh;
    color: ${mainPageProperties.textColor || '#333'};
    margin: 0;
    padding: 0;
    overflow-x: auto;
    overflow-y: auto;
}

.main-content {
    width: 100%;
    min-height: 100vh;
    min-width: 100%;
    padding: 40px 15%; /* مساحة جانبية 15% من كل جهة */
    margin: 0;
    box-sizing: border-box;
    overflow: visible;
    position: relative;
}

/* أنماط خاصة بكل صفحة */
`;

  // إضافة أنماط خاصة لكل صفحة
  pages.forEach(page => {
    const pageFileName = page.id === 'page_home' ? 'index' : page.name.toLowerCase().replace(/\s+/g, '-');
    const pageHeight = page.pageHeight || canvasSize.height;

    // إضافة ارتفاع مخصص لكل صفحة مع دعم التجاوب
    css += `
/* ارتفاع مخصص لصفحة: ${page.name} (${pageFileName}.html) */
body[data-page="${page.id}"] .container {
    min-height: ${pageHeight}px !important;
}

body[data-page="${page.id}"] .main-content {
    min-height: ${pageHeight}px !important;
}`;

    // إضافة ارتفاعات متجاوبة إذا كانت موجودة
    if (page.responsivePageHeights) {
      Object.entries(page.responsivePageHeights).forEach(([device, heightConfig]: [string, any]) => {
        if (heightConfig && heightConfig.customized) {
          let mediaQuery = '';

          if (device === 'mobile') {
            mediaQuery = '@media (max-width: 768px)';
          } else if (device === 'tablet') {
            mediaQuery = '@media (min-width: 769px) and (max-width: 1024px)';
          } else if (device === 'desktop') {
            mediaQuery = '@media (min-width: 1025px)';
          }

          if (mediaQuery) {
            css += `

/* ارتفاع متجاوب لصفحة ${page.name} - ${device} */
${mediaQuery} {
    body[data-page="${page.id}"] .container {
        min-height: ${heightConfig.height}px !important;
    }

    body[data-page="${page.id}"] .main-content {
        min-height: ${heightConfig.height}px !important;
    }
}`;
          }
        }
      });
    }

    css += `
`;

    if (page.properties && Object.keys(page.properties).length > 0) {

      css += `
/* أنماط خاصة بصفحة: ${page.name} (${pageFileName}.html) */
body[data-page="${page.id}"] {`;

      if (page.properties.fontFamily && page.properties.fontFamily !== mainPageProperties.fontFamily) {
        css += `
    font-family: ${page.properties.fontFamily} !important;`;
      }

      if (page.properties.backgroundColor && page.properties.backgroundColor !== mainPageProperties.backgroundColor) {
        css += `
    background: ${page.properties.backgroundColor} !important;`;
      }

      if (page.properties.textColor && page.properties.textColor !== mainPageProperties.textColor) {
        css += `
    color: ${page.properties.textColor} !important;`;
      }

      css += `
}

body[data-page="${page.id}"] .container {`;

      if (page.properties.canvasBackgroundColor && page.properties.canvasBackgroundColor !== mainPageProperties.canvasBackgroundColor) {
        css += `
    background: ${page.properties.canvasBackgroundColor} !important;`;
      }

      if (page.properties.backgroundImage && page.properties.backgroundImage !== mainPageProperties.backgroundImage) {
        css += `
    background-image: url('${page.properties.backgroundImage}') !important;
    background-size: ${page.properties.backgroundSize || 'cover'} !important;
    background-position: ${page.properties.backgroundPosition || 'center'} !important;
    background-repeat: ${page.properties.backgroundRepeat || 'no-repeat'} !important;`;
      }

      css += `
}
`;

      // إضافة CSS مخصص للصفحة
      if (page.properties.customCSS) {
        css += `
/* CSS مخصص لصفحة: ${page.name} */
${page.properties.customCSS}
`;
      }
    }
  });

  return css + `

/* دعم التمرير للصفحات الطويلة */
html {
    scroll-behavior: smooth;
}

body {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

body::-webkit-scrollbar {
    width: 8px;
}

body::-webkit-scrollbar-track {
    background: transparent;
}

body::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 4px;
}

body::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.5);
}

/* باقي الأنماط العامة */
.header {
    text-align: center;
    padding: ${isMobile ? '15px' : '20px'};
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-bottom: ${isMobile ? '15px' : '20px'};
}

.header h1 {
    font-size: ${isMobile ? '1.8rem' : '2.5rem'};
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header p {
    font-size: ${isMobile ? '0.9rem' : '1.1rem'};
    opacity: 0.9;
    font-weight: 300;
}

.footer {
    text-align: center;
    padding: ${isMobile ? '15px' : '20px'};
    background: #f8f9fa;
    color: #6c757d;
    font-size: ${isMobile ? '0.8rem' : '0.9rem'};
    margin-top: ${isMobile ? '15px' : '20px'};
    border-top: 1px solid #e9ecef;
}

/* تصميم متجاوب للموبايل - يطبق عندما تكون الشاشة أصغر من 768px */
@media (max-width: 768px) {
    body {
        padding: 0 !important;
        margin: 0 !important;
    }

    .container {
        margin: 0 !important;
        padding: 0 !important;
        width: 100vw !important;
        min-height: 100vh !important;
        max-width: 100vw !important;
        border-radius: 0 !important;
        box-shadow: none !important;
    }

    .header h1 {
        font-size: 2rem;
    }

    .main-content {
        min-height: 100vh !important;
        padding: 20px !important;
        margin: 0 !important;
        overflow-x: auto !important;
    }

    ${mainPageProperties.mobileResponsive ? `
    /* إعادة ترتيب العناصر عند الحاجة */
    .main-content > * {
        position: relative !important;
        left: auto !important;
        top: auto !important;
        width: 100% !important;
        max-width: 100% !important;
        margin-bottom: 15px;
        display: block !important;
    }` : ''}
}

/* تصميم متجاوب للكمبيوتر - يطبق عندما تكون الشاشة أكبر من 1024px */
@media (min-width: 1025px) {
    .container {
        width: ${canvasSize.width}px !important;
        min-width: ${canvasSize.width}px !important;
        max-width: ${canvasSize.width}px !important;
    }

    .main-content {
        min-width: ${canvasSize.width}px !important;
        width: 100% !important;
        overflow: visible !important;
    }
}

/* تصميم متجاوب للتابلت - يطبق عندما تكون الشاشة بين 769px و 1024px */
@media (min-width: 769px) and (max-width: 1024px) {
    .container {
        width: 100vw !important;
        max-width: 100vw !important;
        min-width: auto !important;
        border-radius: 0 !important;
        box-shadow: none !important;
    }

    .main-content {
        padding: 20px !important;
        min-width: auto !important;
        width: 100% !important;
        overflow-x: auto !important;
    }
}

/* تصميم متجاوب للهاتف - يطبق عندما تكون الشاشة أصغر من 769px */
@media (max-width: 768px) {
    .container {
        width: 100vw !important;
        max-width: 100vw !important;
        min-width: auto !important;
        border-radius: 0 !important;
        margin: 0 !important;
        box-shadow: none !important;
    }

    .main-content {
        min-width: auto !important;
        width: 100% !important;
        padding: 10px !important;
        overflow-x: auto !important;
    }
}

/* CSS للعناصر المدورة - تم إنشاؤها تلقائياً */
${elements.filter(el => el.rotation && el.rotation !== 0).map(el => `
#${el.id} {
    transform: rotate(${el.rotation}deg) !important;
    transform-origin: center center !important;
}`).join('\n')}

/* CSS للعناصر المولدة فردياً */
${individualCSS}

/* CSS للتخطيط المتجاوب للحاويات */
${(() => {
  // جمع جميع الحاويات التي لها تخطيط متجاوب
  const containersWithResponsiveLayout = elements.filter(element =>
    element.type === 'container' && element.properties.responsiveLayout
  );

  if (containersWithResponsiveLayout.length === 0) return '';

  let responsiveCSS = '';

  // CSS للموبايل
  const mobileContainers = containersWithResponsiveLayout.filter(element =>
    element.properties.responsiveLayout.mobile
  );
  if (mobileContainers.length > 0) {
    responsiveCSS += `
/* تخطيط الحاويات للموبايل */
@media (max-width: 768px) {`;
    mobileContainers.forEach(element => {
      const layout = element.properties.responsiveLayout.mobile;
      const childCount = elements.filter(el => el.parentId === element.id).length;

      // حساب الارتفاع المطلوب للتخطيط العمودي أو Grid
      let autoHeight = '';
      if (childCount > 0) {
        if (layout.layoutType === 'flex-col') {
          // للتخطيط العمودي: عدد العناصر × 50px + المسافات
          const estimatedHeight = (childCount * 50) + ((childCount - 1) * (layout.gap || 8)) + 20;
          autoHeight = `height: auto !important; min-height: ${estimatedHeight}px !important;`;
        } else if (layout.layoutType === 'grid') {
          // للشبكة: حساب عدد الصفوف المطلوبة
          const gridColumns = layout.gridColumns || 1;
          const gridRows = Math.ceil(childCount / gridColumns);
          const estimatedHeight = (gridRows * 80) + ((gridRows - 1) * (layout.gap || 8)) + 20; // 80px لكل صف
          autoHeight = `height: auto !important; min-height: ${estimatedHeight}px !important;`;
        }
      }

      responsiveCSS += `
    #${element.id} {
        display: ${layout.layoutType === 'grid' ? 'grid' : layout.layoutType === 'absolute' ? 'relative' : 'flex'} !important;
        flex-direction: ${layout.layoutType === 'flex-col' ? 'column' : 'row'} !important;
        justify-content: ${layout.justifyContent || 'flex-start'} !important;
        align-items: ${layout.alignItems || 'stretch'} !important; // توحيد مع المحرر
        gap: ${layout.gap || 8}px !important;
        ${layout.layoutType === 'grid' ? `grid-template-columns: repeat(${layout.gridColumns || 2}, 1fr) !important;` : ''}
        ${layout.layoutType === 'grid' ? `grid-template-rows: repeat(${layout.gridRows || 2}, 1fr) !important;` : ''}
        ${layout.layoutType === 'grid' ? `justify-items: ${layout.justifyItems || 'stretch'} !important;` : ''}
        ${layout.layoutType === 'grid' ? `align-content: ${layout.alignContent || 'start'} !important;` : ''}
        ${autoHeight}
    }`;
    });
    responsiveCSS += `
}`;
  }

  // CSS للتابلت
  const tabletContainers = containersWithResponsiveLayout.filter(element =>
    element.properties.responsiveLayout.tablet
  );
  if (tabletContainers.length > 0) {
    responsiveCSS += `
/* تخطيط الحاويات للتابلت */
@media (min-width: 769px) and (max-width: 1024px) {`;
    tabletContainers.forEach(element => {
      const layout = element.properties.responsiveLayout.tablet;
      const childCount = elements.filter(el => el.parentId === element.id).length;

      // حساب الارتفاع المطلوب للتخطيط العمودي أو Grid
      let autoHeight = '';
      if (childCount > 0) {
        if (layout.layoutType === 'flex-col') {
          const estimatedHeight = (childCount * 50) + ((childCount - 1) * (layout.gap || 12)) + 20;
          autoHeight = `height: auto !important; min-height: ${estimatedHeight}px !important;`;
        } else if (layout.layoutType === 'grid') {
          const gridColumns = layout.gridColumns || 2;
          const gridRows = Math.ceil(childCount / gridColumns);
          const estimatedHeight = (gridRows * 80) + ((gridRows - 1) * (layout.gap || 12)) + 20;
          autoHeight = `height: auto !important; min-height: ${estimatedHeight}px !important;`;
        }
      }

      responsiveCSS += `
    #${element.id} {
        display: ${layout.layoutType === 'grid' ? 'grid' : layout.layoutType === 'absolute' ? 'relative' : 'flex'} !important;
        flex-direction: ${layout.layoutType === 'flex-col' ? 'column' : 'row'} !important;
        justify-content: ${layout.justifyContent || 'flex-start'} !important;
        align-items: ${layout.alignItems || 'stretch'} !important; // توحيد مع المحرر
        gap: ${layout.gap || 12}px !important;
        ${layout.layoutType === 'grid' ? `grid-template-columns: repeat(${layout.gridColumns || 2}, 1fr) !important;` : ''}
        ${layout.layoutType === 'grid' ? `grid-template-rows: repeat(${layout.gridRows || 2}, 1fr) !important;` : ''}
        ${layout.layoutType === 'grid' ? `justify-items: ${layout.justifyItems || 'stretch'} !important;` : ''}
        ${layout.layoutType === 'grid' ? `align-content: ${layout.alignContent || 'start'} !important;` : ''}
        ${autoHeight}
    }`;
    });
    responsiveCSS += `
}`;
  }

  // CSS للديسكتوب
  const desktopContainers = containersWithResponsiveLayout.filter(element =>
    element.properties.responsiveLayout.desktop
  );
  if (desktopContainers.length > 0) {
    responsiveCSS += `
/* تخطيط الحاويات للديسكتوب */
@media (min-width: 1025px) {`;
    desktopContainers.forEach(element => {
      const layout = element.properties.responsiveLayout.desktop;
      const childCount = elements.filter(el => el.parentId === element.id).length;

      // حساب الارتفاع المطلوب للتخطيط العمودي أو Grid
      let autoHeight = '';
      if (childCount > 0) {
        if (layout.layoutType === 'flex-col') {
          const estimatedHeight = (childCount * 50) + ((childCount - 1) * (layout.gap || 16)) + 20;
          autoHeight = `height: auto !important; min-height: ${estimatedHeight}px !important;`;
        } else if (layout.layoutType === 'grid') {
          const gridColumns = layout.gridColumns || 3;
          const gridRows = Math.ceil(childCount / gridColumns);
          const estimatedHeight = (gridRows * 80) + ((gridRows - 1) * (layout.gap || 16)) + 20;
          autoHeight = `height: auto !important; min-height: ${estimatedHeight}px !important;`;
        }
      }

      responsiveCSS += `
    #${element.id} {
        display: ${layout.layoutType === 'grid' ? 'grid' : layout.layoutType === 'absolute' ? 'relative' : 'flex'} !important;
        flex-direction: ${layout.layoutType === 'flex-col' ? 'column' : 'row'} !important;
        justify-content: ${layout.justifyContent || 'flex-start'} !important;
        align-items: ${layout.alignItems || 'stretch'} !important; // توحيد مع المحرر
        gap: ${layout.gap || 16}px !important;
        ${layout.layoutType === 'grid' ? `grid-template-columns: repeat(${layout.gridColumns || 2}, 1fr) !important;` : ''}
        ${layout.layoutType === 'grid' ? `grid-template-rows: repeat(${layout.gridRows || 2}, 1fr) !important;` : ''}
        ${layout.layoutType === 'grid' ? `justify-items: ${layout.justifyItems || 'stretch'} !important;` : ''}
        ${layout.layoutType === 'grid' ? `align-content: ${layout.alignContent || 'start'} !important;` : ''}
        ${autoHeight}
    }`;
    });
    responsiveCSS += `
}`;
  }

  return responsiveCSS;
})()}

/* CSS للمواضع المتجاوبة - يطبق فقط إذا تم استخدام عدة شاشات */
${(() => {
  // جمع جميع الشاشات المستخدمة
  const usedScreens = new Set();
  elements.forEach(element => {
    if (element.responsivePositions) {
      Object.keys(element.responsivePositions).forEach(screenKey => {
        if (element.responsivePositions[screenKey].customized) {
          usedScreens.add(screenKey);
        }
      });
    }
  });

  // إذا لم تكن هناك شاشات متعددة، لا تطبق CSS متجاوب للعناصر الفردية
  // لكن نبقي CSS إعادة الترتيب العام للموبايل
  if (usedScreens.size <= 1) return '';

  // تجميع المواضع حسب media query لتجنب التكرار
  const mediaQueries: { [key: string]: string[] } = {};

  elements.forEach(element => {
    // معالجة المواضع المتجاوبة
    if (element.responsivePositions) {
      Object.entries(element.responsivePositions).forEach(([deviceKey, position]: [string, any]) => {
        if (position.customized) {
          let mediaQuery = '';

          // تحديد media query حسب نوع الجهاز (مبسط)
          if (deviceKey === 'mobile') {
            mediaQuery = '@media (max-width: 768px)';
          } else if (deviceKey === 'tablet') {
            mediaQuery = '@media (min-width: 769px) and (max-width: 1024px)';
          } else if (deviceKey === 'desktop') {
            mediaQuery = '@media (min-width: 1025px)';
          }

          if (mediaQuery) {
            if (!mediaQueries[mediaQuery]) {
              mediaQueries[mediaQuery] = [];
            }

            mediaQueries[mediaQuery].push(`
    #${element.id} {
        left: ${position.x}px !important;
        top: ${position.y}px !important;
    }`);
          }
        }
      });
    }

    // معالجة الأحجام المتجاوبة
    if (element.responsiveSizes) {
      Object.entries(element.responsiveSizes).forEach(([deviceKey, size]: [string, any]) => {
        if (size.customized) {
          let mediaQuery = '';

          // تحديد media query حسب نوع الجهاز (مبسط)
          if (deviceKey === 'mobile') {
            mediaQuery = '@media (max-width: 768px)';
          } else if (deviceKey === 'tablet') {
            mediaQuery = '@media (min-width: 769px) and (max-width: 1024px)';
          } else if (deviceKey === 'desktop') {
            mediaQuery = '@media (min-width: 1025px)';
          }

          if (mediaQuery) {
            if (!mediaQueries[mediaQuery]) {
              mediaQueries[mediaQuery] = [];
            }

            mediaQueries[mediaQuery].push(`
    #${element.id} {
        width: ${size.width}px !important;
        height: ${size.height}px !important;
    }`);
          }
        }
      });
    }

    // معالجة الأحجام المتجاوبة (responsiveSize)
    if (element.properties?.responsiveSize) {
      Object.entries(element.properties.responsiveSize).forEach(([deviceKey, sizeConfig]: [string, any]) => {
        let mediaQuery = '';

        // تحديد media query حسب نوع الجهاز
        if (deviceKey === 'mobile') {
          mediaQuery = '@media (max-width: 768px)';
        } else if (deviceKey === 'tablet') {
          mediaQuery = '@media (min-width: 769px) and (max-width: 1024px)';
        } else if (deviceKey === 'desktop') {
          mediaQuery = '@media (min-width: 1025px)';
        }

        if (mediaQuery && sizeConfig) {
          if (!mediaQueries[mediaQuery]) {
            mediaQueries[mediaQuery] = [];
          }

          let sizeCSS = '';

          // حساب العرض
          if (sizeConfig.widthMode === 'percentage') {
            sizeCSS += `width: ${sizeConfig.widthValue}% !important; `;
          } else {
            sizeCSS += `width: ${sizeConfig.widthValue}px !important; `;
          }

          // حساب الارتفاع
          if (sizeConfig.heightMode === 'percentage') {
            sizeCSS += `height: ${sizeConfig.heightValue}% !important;`;
          } else {
            sizeCSS += `height: ${sizeConfig.heightValue}px !important;`;
          }

          mediaQueries[mediaQuery].push(`
    #${element.id} {
        ${sizeCSS}
    }`);
        }
      });
    }
  });

  // إنشاء CSS منظم بدون تكرار
  return Object.entries(mediaQueries).map(([mediaQuery, rules]) => `
${mediaQuery} {${rules.join('')}
}`).join('\n');
})()}
`;
}

function generateCSS(elements: any[], analysis: any, canvasSize: any = { width: 375, height: 667 }, pages: any[] = [], currentPageId?: string): string {
  const isDesktop = canvasSize.width >= 1024;
  const isTablet = canvasSize.width >= 768 && canvasSize.width < 1024;
  const isMobile = canvasSize.width < 768;

  // استخراج CSS من العناصر المولدة فردياً
  let individualCSS = '';
  elements.forEach(element => {
    if (element.properties?.generatedCode) {
      console.log(`🎨 استخراج CSS للعنصر: ${element.id}`);
      const extractedCSS = extractCSSFromGeneratedCode(element.properties.generatedCode, element.id);
      if (extractedCSS) {
        individualCSS += extractedCSS + '\n\n';
      }
    }
  });

  // استخراج خصائص الصفحة الحالية أو الأولى كبديل
  let currentPage = null;
  if (currentPageId && pages.length > 0) {
    currentPage = pages.find(page => page.id === currentPageId);
  }
  if (!currentPage && pages.length > 0) {
    currentPage = pages[0]; // الصفحة الأولى كبديل
  }
  const pageProperties = currentPage?.properties || {};

  return `/* تم إنشاء هذا الملف بواسطة AI Website Builder */
/* أبعاد ورقة العمل: ${canvasSize.width}×${canvasSize.height}px */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: ${pageProperties.fontFamily ? `'${pageProperties.fontFamily}'` : "'Cairo', Arial, sans-serif"};
    background: ${pageProperties.canvasBackgroundColor || 'white'};
    ${pageProperties.backgroundImage ? `background-image: url('${pageProperties.backgroundImage}');` : ''}
    ${pageProperties.backgroundImage ? `background-size: ${pageProperties.backgroundSize || 'cover'};` : ''}
    ${pageProperties.backgroundImage ? `background-position: ${pageProperties.backgroundPosition || 'center'};` : ''}
    ${pageProperties.backgroundImage ? `background-repeat: ${pageProperties.backgroundRepeat || 'no-repeat'};` : ''}
    min-height: 100vh;
    color: ${pageProperties.textColor || '#333'};
    margin: 0;
    padding: 0;
    overflow-x: auto;
    overflow-y: auto;
}

.main-content {
    width: 100%;
    min-height: 100vh;
    min-width: 100%;
    padding: 40px 15%; /* مساحة جانبية 15% من كل جهة */
    margin: 0;
    box-sizing: border-box;
    overflow: visible;
}

.header {
    text-align: center;
    padding: ${isMobile ? '20px 15px' : '40px 30px'};
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: ${isMobile ? '15px 15px 0 0' : '20px 20px 0 0'};
    margin-bottom: ${isMobile ? '20px' : '30px'};
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.header-content {
    position: relative;
    z-index: 1;
}

.main-title {
    font-size: ${isMobile ? '1.8rem' : '2.5rem'};
    font-weight: 700;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    background: linear-gradient(45deg, #fff, #f0f8ff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-decoration {
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, transparent, white, transparent);
    margin: 0 auto;
    border-radius: 2px;
    opacity: 0.8;
}

.subtitle {
    color: #7f8c8d;
    font-size: ${isMobile ? '1rem' : '1.2rem'};
}

.main-content {
    width: 100%;
    min-height: ${isMobile ? '100vh' : canvasSize.height + 'px'};
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

/* أنماط الأزرار */
/* ملاحظة: الألوان المخصصة في inline styles لها أولوية مع !important */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-family: inherit;
    font-size: 14px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

.btn-primary:hover {
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
}

/* أزرار متخصصة للوظائف الحقيقية */
.btn-whatsapp {
    background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
    box-shadow: 0 4px 15px rgba(37, 211, 102, 0.4);
}

.btn-whatsapp:hover {
    box-shadow: 0 8px 25px rgba(37, 211, 102, 0.6);
}

.btn-email {
    background: linear-gradient(135deg, #EA4335 0%, #FBBC05 100%);
    box-shadow: 0 4px 15px rgba(234, 67, 53, 0.4);
}

.btn-email:hover {
    box-shadow: 0 8px 25px rgba(234, 67, 53, 0.6);
}

.btn-call {
    background: linear-gradient(135deg, #4285F4 0%, #34A853 100%);
    box-shadow: 0 4px 15px rgba(66, 133, 244, 0.4);
}

.btn-call:hover {
    box-shadow: 0 8px 25px rgba(66, 133, 244, 0.6);
}

/* أنماط النماذج المحسنة */
.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
}

.form-control {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e1e8ed;
    border-radius: 10px;
    font-family: inherit;
    font-size: 14px;
    transition: all 0.3s ease;
    background: #fff;
}

.form-control:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 4px rgba(52, 152, 219, 0.1);
    transform: translateY(-1px);
}

.form-control:hover {
    border-color: #bdc3c7;
}

/* أنماط العناصر المحسنة */
.element-button {
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.element-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.element-input,
.element-textarea,
.element-select {
    background: rgba(255,255,255,0.9);
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.image-element {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.image-element:hover {
    border-color: #3498db;
    background: #e3f2fd;
}

.image-placeholder {
    text-align: center;
    color: #6c757d;
}

.image-icon {
    font-size: 2rem;
    display: block;
    margin-bottom: 8px;
}

.form-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.form-header h3 {
    margin: 0 0 15px 0;
    color: #2c3e50;
}

.layout-container {
    position: relative;
    box-sizing: border-box;
}

.layout-container > * {
    box-sizing: border-box;
    position: relative !important;
    left: auto !important;
    top: auto !important;
}

.layout-container[style*="display: flex"] > * {
    flex-shrink: 0;
}

.layout-container[style*="display: grid"] > * {
    position: relative !important;
}

.unknown-element {
    background: #fff3cd;
    border: 2px dashed #ffc107;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #856404;
}

.unknown-content {
    text-align: center;
}

.unknown-content span {
    font-size: 2rem;
    display: block;
    margin-bottom: 8px;
}

/* أنماط الجداول */
.data-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.data-table th,
.data-table td {
    padding: 12px;
    text-align: right;
    border-bottom: 1px solid #eee;
}

.data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.data-table tr:hover {
    background: #f8f9fa;
}

/* أنماط النصوص */
.text-element {
    font-size: 16px;
    line-height: 1.6;
    color: #2c3e50;
}

.container-element {
    background: #f8f9fa;
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    color: #7f8c8d;
}

.layout-container {
    position: relative;
    box-sizing: border-box;
}

.layout-container > * {
    box-sizing: border-box;
    position: relative !important;
    left: auto !important;
    top: auto !important;
}

.layout-container[style*="display: flex"] > * {
    flex-shrink: 0;
}

.layout-container[style*="display: grid"] > * {
    position: relative !important;
}

.footer {
    text-align: center;
    padding: 20px 0;
    border-top: 2px solid #eee;
    margin-top: 40px;
    color: #7f8c8d;
    font-size: 14px;
}

/* رسوم متحركة للوظائف المخصصة */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes slideInRight {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

@keyframes slideInUp {
    from { transform: translateY(100%); }
    to { transform: translateY(0); }
}

@keyframes slideInDown {
    from { transform: translateY(-100%); }
    to { transform: translateY(0); }
}

@keyframes bounceIn {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
}

@keyframes rotateIn {
    from { transform: rotate(-200deg); opacity: 0; }
    to { transform: rotate(0); opacity: 1; }
}

@keyframes zoomIn {
    from { transform: scale(0); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-10px); }
    20%, 40%, 60%, 80% { transform: translateX(10px); }
}

/* رسوم متحركة إضافية للنصوص والعناصر */
@keyframes colorWave {
    0% { color: #3498db; }
    25% { color: #e74c3c; }
    50% { color: #f39c12; }
    75% { color: #27ae60; }
    100% { color: #3498db; }
}

@keyframes slide {
    0% { transform: translateX(100%); }
    100% { transform: translateX(-100%); }
}

@keyframes textGlow {
    0%, 100% { text-shadow: 0 0 5px rgba(52, 152, 219, 0.5); }
    50% { text-shadow: 0 0 20px rgba(52, 152, 219, 1), 0 0 30px rgba(52, 152, 219, 0.8); }
}

@keyframes typewriter {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* تأثيرات متقدمة للعناصر */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes flip {
    0% { transform: rotateY(0deg); }
    50% { transform: rotateY(180deg); }
    100% { transform: rotateY(360deg); }
}

@keyframes zoom {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes wiggle {
    0%, 7%, 14%, 21%, 28%, 35%, 42%, 49%, 56%, 63%, 70%, 77%, 84%, 91%, 98%, 100% { transform: rotate(0deg); }
    3.5%, 10.5%, 17.5%, 24.5%, 31.5%, 38.5%, 45.5%, 52.5%, 59.5%, 66.5%, 73.5%, 80.5%, 87.5%, 94.5% { transform: rotate(2deg); }
}

@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    14% { transform: scale(1.1); }
    28% { transform: scale(1); }
    42% { transform: scale(1.1); }
    70% { transform: scale(1); }
}

@keyframes rainbow {
    0% { filter: hue-rotate(0deg); }
    100% { filter: hue-rotate(360deg); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(255, 255, 255, 0.5); }
    50% { box-shadow: 0 0 20px rgba(255, 255, 255, 1), 0 0 30px rgba(255, 255, 255, 0.8); }
}

@keyframes matrix {
    0% { background-position: 0% 0%; }
    100% { background-position: 100% 100%; }
}

/* فلاتر CSS للتأثيرات البصرية */
.filter-blur { filter: blur(2px); }
.filter-brightness { filter: brightness(1.5); }
.filter-contrast { filter: contrast(1.5); }
.filter-grayscale { filter: grayscale(100%); }
.filter-sepia { filter: sepia(100%); }
.filter-vintage { filter: sepia(50%) contrast(1.2) brightness(1.1); }
.filter-cool { filter: hue-rotate(180deg) saturate(1.2); }
.filter-warm { filter: hue-rotate(30deg) saturate(1.1) brightness(1.1); }
.filter-neon { filter: brightness(1.5) contrast(1.3) saturate(1.5); }
.filter-dark { filter: brightness(0.7) contrast(1.2); }

/* تأثيرات خاصة للصور */
@keyframes imageShake {
    0%, 100% { transform: translateX(0) translateY(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px) translateY(-1px); }
    20%, 40%, 60%, 80% { transform: translateX(2px) translateY(1px); }
}

@keyframes imageFloat {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes imageRotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes imagePulse {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.05); opacity: 0.8; }
}

@keyframes imageFlip {
    0% { transform: rotateY(0deg); }
    50% { transform: rotateY(180deg); }
    100% { transform: rotateY(360deg); }
}

@keyframes imageBounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-15px); }
    60% { transform: translateY(-7px); }
}

@keyframes imageZoom {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* فلاتر CSS للصور */
.image-filter-blur { filter: blur(2px); }
.image-filter-brightness { filter: brightness(1.2); }
.image-filter-contrast { filter: contrast(1.3); }
.image-filter-grayscale { filter: grayscale(100%); }
.image-filter-sepia { filter: sepia(100%); }
.image-filter-vintage { filter: sepia(50%) contrast(1.2) brightness(1.1); }
.image-filter-cool { filter: hue-rotate(180deg) saturate(1.2); }
.image-filter-warm { filter: hue-rotate(30deg) saturate(1.1) brightness(1.1); }

/* تصميم متجاوب للموبايل - يطبق عندما تكون الشاشة أصغر من 768px */
@media (max-width: 768px) {
    body {
        padding: 0 !important;
        margin: 0 !important;
    }



    .header h1 {
        font-size: 2rem;
    }

    .main-content {
        min-height: 100vh !important;
        padding: 20px !important;
        margin: 0 !important;
        overflow-x: auto !important;
    }

    ${pageProperties.mobileResponsive ? `
    /* إعادة ترتيب العناصر داخل الحاويات فقط */
    .layout-container > * {
        position: relative !important;
        left: auto !important;
        top: auto !important;
        width: 100% !important;
        max-width: 100% !important;
        margin-bottom: 15px;
        display: block !important;
    }` : ''}
}

/* تصميم متجاوب للتابلت - مثل الهاتف بملء الشاشة */
@media (min-width: 769px) and (max-width: 1024px) {
    body {
        padding: 0 !important;
        margin: 0 !important;
    }



    .main-content {
        min-height: 100vh !important;
        padding: 30px !important;
        margin: 0 !important;
        overflow-x: auto !important;
    }

    ${pageProperties.mobileResponsive ? `
    /* إعادة ترتيب العناصر داخل الحاويات فقط */
    .layout-container > * {
        position: relative !important;
        left: auto !important;
        top: auto !important;
        width: 100% !important;
        max-width: 100% !important;
        margin-bottom: 20px;
        display: block !important;
    }` : ''}
}

/* تصميم متجاوب للكمبيوتر - بملء الشاشة مع مساحة جانبية */
@media (min-width: 1025px) {
    body {
        padding: 0 !important;
        margin: 0 !important;
    }



    .main-content {
        width: 100% !important;
        min-width: 100% !important;
        padding: 40px 15% !important; /* مساحة جانبية 15% من كل جهة */
        margin: 0 !important;
        overflow: visible !important;
        min-height: 100vh !important;
    }
}



/* CSS للعناصر المدورة - تم إنشاؤها تلقائياً */
${elements.filter(el => el.rotation && el.rotation !== 0).map(el => `
#${el.id} {
    transform: rotate(${el.rotation}deg) !important;
    transform-origin: center center !important;
}`).join('\n')}

/* CSS للعناصر المولدة فردياً */
${individualCSS}

/* CSS للمواضع المتجاوبة - يطبق فقط إذا تم استخدام عدة شاشات */
${(() => {
  // جمع جميع الشاشات المستخدمة
  const usedScreens = new Set();
  elements.forEach(element => {
    // فحص المواضع المتجاوبة
    if (element.responsivePositions) {
      Object.keys(element.responsivePositions).forEach(screenKey => {
        if (element.responsivePositions[screenKey].customized) {
          usedScreens.add(screenKey);
        }
      });
    }

    // فحص الأحجام المتجاوبة
    if (element.responsiveSizes) {
      Object.keys(element.responsiveSizes).forEach(screenKey => {
        if (element.responsiveSizes[screenKey].customized) {
          usedScreens.add(screenKey);
        }
      });
    }
  });

  // إذا لم تكن هناك شاشات متعددة، لا تطبق CSS متجاوب
  if (usedScreens.size <= 1) return '';

  // تجميع المواضع حسب media query لتجنب التكرار
  const mediaQueries: { [key: string]: string[] } = {};

  elements.forEach(element => {
    // معالجة المواضع المتجاوبة
    if (element.responsivePositions) {
      Object.entries(element.responsivePositions).forEach(([deviceKey, position]: [string, any]) => {
        if (position.customized) {
          let mediaQuery = '';

          // تحديد media query حسب نوع الجهاز (مبسط)
          if (deviceKey === 'mobile') {
            mediaQuery = '@media (max-width: 768px)';
          } else if (deviceKey === 'tablet') {
            mediaQuery = '@media (min-width: 769px) and (max-width: 1024px)';
          } else if (deviceKey === 'desktop') {
            mediaQuery = '@media (min-width: 1025px)';
          }

          if (mediaQuery) {
            if (!mediaQueries[mediaQuery]) {
              mediaQueries[mediaQuery] = [];
            }

            mediaQueries[mediaQuery].push(`
    #${element.id} {
        left: ${position.x}px !important;
        top: ${position.y}px !important;
    }`);
          }
        }
      });
    }

    // معالجة الأحجام المتجاوبة
    if (element.responsiveSizes) {
      Object.entries(element.responsiveSizes).forEach(([deviceKey, size]: [string, any]) => {
        if (size.customized) {
          let mediaQuery = '';

          // تحديد media query حسب نوع الجهاز (مبسط)
          if (deviceKey === 'mobile') {
            mediaQuery = '@media (max-width: 768px)';
          } else if (deviceKey === 'tablet') {
            mediaQuery = '@media (min-width: 769px) and (max-width: 1024px)';
          } else if (deviceKey === 'desktop') {
            mediaQuery = '@media (min-width: 1025px)';
          }

          if (mediaQuery) {
            if (!mediaQueries[mediaQuery]) {
              mediaQueries[mediaQuery] = [];
            }

            mediaQueries[mediaQuery].push(`
    #${element.id} {
        width: ${size.width}px !important;
        height: ${size.height}px !important;
    }`);
          }
        }
      });
    }
  });

  // إنشاء CSS منظم بدون تكرار
  return Object.entries(mediaQueries).map(([mediaQuery, rules]) => `
${mediaQuery} {${rules.join('')}
}`).join('\n');
})()}

/* CSS مخصص للصفحة */
${pageProperties.customCSS || ''}
`;
}

async function generateJavaScript(elements: any[], analysis: any, canvasSize?: any, projectInfo?: any, pages?: any[]): Promise<string> {
  console.log('🤖 بدء توليد JavaScript...');

  // أولاً: جمع الكود المولد فردياً للعناصر التي لها generatedCode
  const individualCodes: string[] = [];
  const elementsWithGeneratedCode: any[] = [];
  const elementsForAI: any[] = [];

  elements.forEach(element => {
    if (element.properties?.generatedCode) {
      console.log(`📦 استخدام كود مولد فردياً للعنصر: ${element.id}`);
      // استخراج JavaScript من الكود المولد فردياً
      const jsCode = extractJavaScriptFromHTML(element.properties.generatedCode, element.id);
      if (jsCode) {
        individualCodes.push(jsCode);
        elementsWithGeneratedCode.push(element);
      }
    } else if (element.properties?.prompt && element.properties.prompt.trim() !== '') {
      // العناصر التي لها برومبت ولكن لم يتم توليد كود لها فردياً
      elementsForAI.push(element);
    }
  });

  console.log(`📦 عناصر بكود فردي: ${elementsWithGeneratedCode.length}`);
  console.log(`🤖 عناصر للذكاء الاصطناعي: ${elementsForAI.length}`);

  // ثانياً: توليد كود للعناصر المتبقية بالذكاء الاصطناعي
  let aiGeneratedCode = '';
  if (elementsForAI.length > 0) {
    try {
      aiGeneratedCode = await generateAICode(elementsForAI, canvasSize, {
        type: analysis.projectType || 'موقع ويب عام',
        name: projectInfo?.name || 'مشروع جديد',
        description: projectInfo?.description || ''
      }, pages);
      console.log('✅ تم توليد الكود بالذكاء الاصطناعي للعناصر الجديدة');
    } catch (error) {
      console.error('❌ فشل في توليد الكود بالذكاء الاصطناعي:', error);
    }
  }

  // ثالثاً: دمج جميع الأكواد
  const finalCode = combineJavaScriptCodes(individualCodes, aiGeneratedCode);

  console.log('✅ تم دمج الأكواد بنجاح');
  return finalCode;
}

// دالة ذكية لاستخراج JavaScript - تعمل مع أي كود بدون تعديل
function extractJavaScriptFromHTML(htmlCode: string, elementId: string): string {
  try {
    console.log(`🔍 استخراج JavaScript للعنصر: ${elementId}`);
    console.log(`📄 الكود المولد: ${htmlCode.substring(0, 200)}...`);

    // البحث عن تاغ script في الكود
    const scriptMatch = htmlCode.match(/<script[^>]*>([\s\S]*?)<\/script>/i);
    if (scriptMatch && scriptMatch[1]) {
      let jsCode = scriptMatch[1].trim();
      console.log(`📝 JavaScript مستخرج: ${jsCode.substring(0, 100)}...`);

      // فقط استبدال المعرفات - لا نغير منطق الكود أبداً
      const idMatches = htmlCode.match(/id="([^"]+)"/g);
      if (idMatches) {
        idMatches.forEach(match => {
          const originalId = match.match(/id="([^"]+)"/)?.[1];
          if (originalId && originalId !== elementId) {
            console.log(`🔄 استبدال المعرف: ${originalId} → ${elementId}`);
            // استبدال دقيق للمعرفات فقط
            jsCode = jsCode.replace(new RegExp(`(['"\`])${originalId.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\1`, 'g'), `$1${elementId}$1`);
            jsCode = jsCode.replace(new RegExp(`getElementById\\(['"\`]${originalId.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}['"\`]\\)`, 'g'), `getElementById('${elementId}')`);
          }
        });
      }

      console.log(`✅ JavaScript نهائي: ${jsCode.substring(0, 100)}...`);
      return jsCode;
    } else {
      console.log('❌ لم يتم العثور على تاغ script');
    }
  } catch (error) {
    console.error('❌ خطأ في استخراج JavaScript:', error);
  }
  return '';
}

// دالة ذكية 100% لاستخراج المتطلبات العامة من أي كود
function extractGlobalRequirements(code: string, elementIndex: number): { functions: string[], variables: string[] } {
  const functions: string[] = [];
  const variables: string[] = [];

  try {
    // الحل البسيط والذكي: تنفيذ الكود كما هو مع جعل كل شيء عام
    const globalCode = `
// كود عام آمن للعنصر ${elementIndex + 1}
(function() {
    try {
        // تنفيذ الكود الأصلي
        ${code}

        // جعل جميع الدوال متاحة عالمياً
        if (typeof toggleState !== 'undefined') window.toggleState = toggleState;
        if (typeof showMessage !== 'undefined') window.showMessage = showMessage;
        if (typeof handleClick !== 'undefined') window.handleClick = handleClick;
        if (typeof submitForm !== 'undefined') window.submitForm = submitForm;
        if (typeof validateInput !== 'undefined') window.validateInput = validateInput;
        if (typeof animateElement !== 'undefined') window.animateElement = animateElement;
        if (typeof changeColor !== 'undefined') window.changeColor = changeColor;
        if (typeof playSound !== 'undefined') window.playSound = playSound;
        if (typeof openModal !== 'undefined') window.openModal = openModal;
        if (typeof closeModal !== 'undefined') window.closeModal = closeModal;

        // جعل جميع المتغيرات متاحة عالمياً
        if (typeof isSuccess !== 'undefined') window.isSuccess = isSuccess;
        if (typeof counter !== 'undefined') window.counter = counter;
        if (typeof currentState !== 'undefined') window.currentState = currentState;
        if (typeof userData !== 'undefined') window.userData = userData;
        if (typeof config !== 'undefined') window.config = config;

    } catch (error) {
        console.error('خطأ في العنصر ${elementIndex + 1}:', error);
    }
})();`;

    functions.push(globalCode);

  } catch (error) {
    console.error('خطأ في استخراج المتطلبات العامة:', error);
  }

  return { functions, variables };
}

// دالة لإصلاح أسماء المتغيرات المتضاربة
function fixVariableNaming(jsCode: string, elementId: string): string {
  // استبدال أسماء المتغيرات العامة بأسماء فريدة
  const uniqueSuffix = elementId.slice(-6); // آخر 6 أرقام من المعرف

  // قائمة المتغيرات الشائعة التي تحتاج استبدال
  const commonVariables = ['button', 'element', 'input', 'div', 'span', 'form'];

  commonVariables.forEach(varName => {
    const regex = new RegExp(`\\b(const|let|var)\\s+${varName}\\b`, 'g');
    jsCode = jsCode.replace(regex, `$1 ${varName}_${uniqueSuffix}`);

    // استبدال استخدامات المتغير (لكن ليس في createElement)
    const usageRegex = new RegExp(`\\b${varName}\\b(?!_)(?!['"])`, 'g');
    jsCode = jsCode.replace(usageRegex, (match, offset) => {
      // تجنب استبدال أسماء العناصر في createElement
      const beforeMatch = jsCode.substring(Math.max(0, offset - 20), offset);
      if (beforeMatch.includes('createElement(')) {
        return match; // لا تستبدل
      }
      return `${varName}_${uniqueSuffix}`;
    });
  });

  // إصلاح مشاكل createElement الشائعة
  jsCode = jsCode.replace(/createElement\(['"`](\w+)_\d+['"`]\)/g, "createElement('$1')");

  return jsCode;
}

// دالة للتحقق من وجود العناصر المطلوبة في HTML
function validateRequiredElements(jsCode: string, htmlCode: string, elementId: string): string {
  // البحث عن العناصر المطلوبة في JavaScript
  const requiredClasses = jsCode.match(/querySelector\(['"`]\.([^'"`]+)['"`]\)/g);

  if (requiredClasses) {
    requiredClasses.forEach(match => {
      const className = match.match(/\.([^'"`]+)/)?.[1];
      if (className && !htmlCode.includes(`class="${className}"`)) {
        console.log(`⚠️ عنصر مطلوب غير موجود في HTML: .${className}`);

        // إصلاح مشكلة spinner المفقود
        if (className === 'spinner') {
          console.log(`🔧 إصلاح مشكلة spinner للعنصر: ${elementId}`);

          // استبدال querySelector بإنشاء العنصر تلقائياً
          const spinnerVarMatch = jsCode.match(/const\s+(\w+)\s*=\s*[^;]*\.querySelector\(['"`]\.spinner['"`]\);?/);
          if (spinnerVarMatch) {
            const spinnerVar = spinnerVarMatch[1];

            jsCode = jsCode.replace(
              /const\s+\w+\s*=\s*[^;]*\.querySelector\(['"`]\.spinner['"`]\);?/,
              `// تم إنشاء spinner تلقائياً
              let ${spinnerVar} = document.getElementById('${elementId}').querySelector('.spinner');
              if (!${spinnerVar}) {
                  ${spinnerVar} = document.createElement('span');
                  ${spinnerVar}.className = 'spinner';
                  ${spinnerVar}.style.display = 'none';
                  document.getElementById('${elementId}').appendChild(${spinnerVar});
              }`
            );
          }
        }
      }
    });
  }

  return jsCode;
}

// دالة لمعالجة الدوال المستدعاة من onclick
function handleOnClickFunctions(jsCode: string, htmlCode: string, elementId: string): string {
  try {
    // البحث عن onclick في HTML
    const onclickMatch = htmlCode.match(/onclick="([^"]+)"/);
    if (onclickMatch) {
      const onclickFunction = onclickMatch[1];
      console.log(`🔧 معالجة onclick: ${onclickFunction}`);

      // استخراج اسم الدالة
      const functionNameMatch = onclickFunction.match(/(\w+)\s*\(/);
      if (functionNameMatch) {
        const functionName = functionNameMatch[1];

        // البحث عن تعريف الدالة في JavaScript باستخدام parser محسن
        const functionDef = extractFunctionDefinition(jsCode, functionName);
        if (functionDef) {
          // إزالة تعريف الدالة من مكانها الحالي
          jsCode = jsCode.replace(functionDef.fullMatch, '');

          // إضافة الدالة في النطاق العام مع جميع المتغيرات المطلوبة
          const globalFunction = `// دالة عامة للـ onclick
${functionDef.variables}
window.${functionName} = ${functionDef.cleanFunction};`;

          // إضافة الدالة في بداية الكود
          jsCode = globalFunction + '\n\n' + jsCode;

          console.log(`✅ تم نقل الدالة ${functionName} للنطاق العام`);
        }
      }
    }
  } catch (error) {
    console.error('❌ خطأ في معالجة onclick:', error);
  }

  return jsCode;
}

// دالة لاستخراج تعريف الدالة مع المتغيرات المطلوبة
function extractFunctionDefinition(jsCode: string, functionName: string): any {
  try {
    // البحث عن تعريف الدالة
    const functionRegex = new RegExp(`function\\s+${functionName}\\s*\\([^)]*\\)\\s*{`, 'g');
    const match = functionRegex.exec(jsCode);

    if (match) {
      const startIndex = match.index;
      let braceCount = 0;
      let currentIndex = startIndex;
      let inString = false;
      let stringChar = '';

      // العثور على نهاية الدالة
      while (currentIndex < jsCode.length) {
        const char = jsCode[currentIndex];

        if (!inString) {
          if (char === '"' || char === "'") {
            inString = true;
            stringChar = char;
          } else if (char === '{') {
            braceCount++;
          } else if (char === '}') {
            braceCount--;
            if (braceCount === 0) {
              break;
            }
          }
        } else {
          if (char === stringChar && jsCode[currentIndex - 1] !== '\\') {
            inString = false;
            stringChar = '';
          }
        }

        currentIndex++;
      }

      const fullMatch = jsCode.substring(startIndex, currentIndex + 1);

      // البحث عن المتغيرات المطلوبة قبل الدالة
      const beforeFunction = jsCode.substring(0, startIndex);
      const variableMatches = beforeFunction.match(/let\s+\w+\s*=\s*[^;]+;/g) || [];
      const variables = variableMatches.join('\n');

      // تنظيف الدالة
      const cleanFunction = fullMatch.replace(`function ${functionName}`, 'function');

      return {
        fullMatch,
        cleanFunction,
        variables
      };
    }
  } catch (error) {
    console.error('❌ خطأ في استخراج تعريف الدالة:', error);
  }

  return null;
}

// دالة ذكية 100% - تدعم أي كود من الذكاء الاصطناعي
function combineJavaScriptCodes(individualCodes: string[], aiCode: string): string {
  console.log(`🔧 دمج الأكواد: ${individualCodes.length} كود فردي + AI`);

  const allCodes: string[] = [];
  const globalFunctions: string[] = [];
  const globalVariables: string[] = [];

  // استخراج الدوال والمتغيرات المطلوبة عالمياً
  individualCodes.forEach((code, index) => {
    if (code.trim()) {
      const extracted = extractGlobalRequirements(code, index);
      if (extracted.functions.length > 0) {
        globalFunctions.push(...extracted.functions);
      }
      if (extracted.variables.length > 0) {
        globalVariables.push(...extracted.variables);
      }
    }
  });

  // إضافة المتغيرات العامة أولاً
  if (globalVariables.length > 0) {
    allCodes.push('// المتغيرات العامة المطلوبة');
    globalVariables.forEach(variable => {
      allCodes.push(variable);
    });
    allCodes.push('');
  }

  // إضافة الدوال العامة
  if (globalFunctions.length > 0) {
    allCodes.push('// الدوال العامة للاستدعاء من HTML');
    globalFunctions.forEach(func => {
      allCodes.push(func);
    });
    allCodes.push('');
  }

  // إضافة كود التحميل الأساسي
  allCodes.push(`// ربط الأحداث بالعناصر
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تم تحميل الموقع بنجاح');`);

  // إضافة الأكواد الفردية مع الحماية الكاملة
  if (individualCodes.length > 0) {
    allCodes.push('');
    allCodes.push('    // 📦 أكواد العناصر المولدة فردياً (محفوظة)');
    individualCodes.forEach((code, index) => {
      if (code.trim()) {
        allCodes.push(`    // عنصر فردي ${index + 1}`);

        // تنفيذ الكود مع حماية كاملة
        const safeCode = `    try {
        ${code.split('\n').map(line => '        ' + line).join('\n')}
    } catch (error) {
        console.error('خطأ في عنصر فردي ${index + 1}:', error);
    }`;

        allCodes.push(safeCode);
      }
    });
  }

  // إضافة كود الذكاء الاصطناعي للعناصر الجديدة فقط
  if (aiCode && aiCode.trim() && !aiCode.includes('لا توجد عناصر')) {
    allCodes.push('');
    allCodes.push('    // 🤖 كود الذكاء الاصطناعي للعناصر الجديدة');
    const aiCodeContent = extractContentFromDOMContentLoaded(aiCode);
    if (aiCodeContent.trim()) {
      const indentedAiCode = aiCodeContent.split('\n').map(line =>
        line.trim() ? '    ' + line : line
      ).join('\n');
      allCodes.push(indentedAiCode);
    }
  }

  // إغلاق DOMContentLoaded
  allCodes.push('});');

  const finalCode = allCodes.join('\n');
  console.log(`✅ كود نهائي: ${finalCode.length} حرف`);
  return finalCode;
}

// دالة لاستخراج المحتوى من داخل DOMContentLoaded
function extractContentFromDOMContentLoaded(code: string): string {
  try {
    // البحث عن محتوى DOMContentLoaded
    const match = code.match(/DOMContentLoaded[^{]*{([\s\S]*)}[^}]*$/);
    if (match && match[1]) {
      return match[1].trim();
    }
  } catch (error) {
    console.error('خطأ في استخراج محتوى DOMContentLoaded:', error);
  }
  // إذا لم يتم العثور على DOMContentLoaded، إرجاع الكود كما هو
  return code;
}

// دالة مساعدة لتحديد اسم كلاس المدير
function getManagerClassName(projectType: string): string {
  if (projectType.includes('متجر')) return 'EcommerceManager';
  if (projectType.includes('تعليم') || projectType.includes('مدرسة')) return 'EducationManager';
  if (projectType.includes('مطعم')) return 'RestaurantManager';
  if (projectType.includes('شركة')) return 'BusinessManager';
  if (projectType.includes('طبي') || projectType.includes('مستشفى')) return 'MedicalManager';
  if (projectType.includes('مكتبة')) return 'LibraryManager';
  if (projectType.includes('فندق')) return 'HotelManager';
  return 'WebsiteManager';
}

function generateAdvancedJavaScript(elements: any[], analysis: any): string {
  const hasButtons = elements.some(el => el.type === 'button');
  const hasInputs = elements.some(el => el.type === 'input' || el.type === 'textarea');
  const hasTables = elements.some(el => el.type === 'table');
  const hasForms = elements.some(el => el.type === 'form');

  // تحديد نوع المشروع من التحليل
  const projectType = analysis.projectType || 'موقع ويب عام';
  const isEcommerce = projectType.includes('متجر');
  const isEducation = projectType.includes('تعليم') || projectType.includes('مدرسة');
  const isRestaurant = projectType.includes('مطعم');
  const isBusiness = projectType.includes('شركة');
  const isMedical = projectType.includes('طبي') || projectType.includes('مستشفى');

  let js = `// تم إنشاء هذا الملف بواسطة AI Website Builder
// مشروع متخصص: ${projectType}
// كود JavaScript تفاعلي ومتقدم

class ${getManagerClassName(projectType)} {
    constructor() {
        this.elements = {};
        this.data = [];
        this.currentPage = 'home';
        ${isEcommerce ? 'this.cart = []; this.cartTotal = 0;' : ''}
        ${isEducation ? 'this.students = []; this.currentStudent = null;' : ''}
        ${isRestaurant ? 'this.orders = []; this.tables = [];' : ''}
        ${isBusiness ? 'this.employees = []; this.projects = [];' : ''}
        ${isMedical ? 'this.patients = []; this.appointments = [];' : ''}
        this.init();
    }

    // تهيئة التطبيق
    init() {
        console.log('🚀 تم تحميل الموقع بنجاح');
        this.cacheElements();
        this.applyElementStyles();
        this.bindEvents();
        this.setupAnimations();
        ${hasInputs ? 'this.setupFormValidation();' : ''}
        ${hasTables ? 'this.loadTableData();' : ''}
    }

    // حفظ مراجع العناصر
    cacheElements() {
`;

  elements.forEach((element: any) => {
    js += `        this.elements.${element.id} = document.getElementById('${element.id}');\n`;
  });

  js += `    }

    // تطبيق الأنماط والخصائص من المحرر
    applyElementStyles() {
        console.log('🎨 تطبيق أنماط العناصر من المحرر...');
`;

  // إضافة تطبيق الأنماط لكل عنصر
  elements.forEach((element: any) => {
    const elementDesign = {
      backgroundColor: element.properties?.backgroundColor || 'transparent',
      color: element.properties?.color || '#000000',
      fontSize: `${element.properties?.fontSize || 14}px`,
      borderRadius: `${element.properties?.borderRadius || 4}px`,
      padding: `${element.properties?.padding || 8}px`,
      margin: `${element.properties?.margin || 0}px`,
      fontWeight: element.properties?.fontWeight || 'normal'
    };

    js += `
        // عنصر: ${element.id} (${element.type})
        if (this.elements.${element.id}) {
            this.elements.${element.id}.style.backgroundColor = '${elementDesign.backgroundColor}';
            this.elements.${element.id}.style.color = '${elementDesign.color}';
            this.elements.${element.id}.style.fontSize = '${elementDesign.fontSize}';
            this.elements.${element.id}.style.borderRadius = '${elementDesign.borderRadius}';
            this.elements.${element.id}.style.padding = '${elementDesign.padding}';
            this.elements.${element.id}.style.margin = '${elementDesign.margin}';
            this.elements.${element.id}.style.fontWeight = '${elementDesign.fontWeight}';
            ${element.rotation ? `this.elements.${element.id}.style.transform = 'rotate(${element.rotation}deg)';` : ''}
            console.log('✅ تم تطبيق أنماط العنصر: ${element.id}');
        }`;
  });

  js += `
    }

    // ربط الأحداث
    bindEvents() {
        console.log('🔗 بدء ربط الأحداث...');
`;

  elements.forEach((element: any) => {
    const prompt = element.properties.prompt && element.properties.prompt.trim() !== '' ? element.properties.prompt : '';
    const elementText = element.properties.text || element.id;

    switch (element.type) {
      case 'button':
        js += `        // زر: ${elementText}
        if (this.elements.${element.id}) {
            console.log('✅ ربط حدث زر: ${elementText}');
            this.elements.${element.id}.addEventListener('click', () => {
                console.log('🖱️ تم الضغط على زر: ${elementText}');
                ${prompt && prompt.trim() !== '' ? `this.executeCustomFunction('${element.id}', '${prompt}');` : `this.handle${elementText.replace(/\s+/g, '')}Click();`}
            });
        } else {
            console.error('❌ لم يتم العثور على زر: ${elementText}');
        }
`;
        break;
      case 'input':
      case 'textarea':
        js += `        // حقل إدخال: ${element.properties.label || element.id}
        if (this.elements.${element.id}) {
            this.elements.${element.id}.addEventListener('input', (e) => {
                this.validateField(e.target);
                ${prompt && prompt.trim() !== '' ? `this.executeCustomFunction('${element.id}', '${prompt}', e.target.value);` : ''}
            });
        }
`;
        break;
      case 'select':
        js += `        // قائمة اختيار: ${element.properties.title || element.properties.label || element.id}
        if (this.elements.${element.id}) {
            console.log('✅ ربط حدث قائمة اختيار: ${element.properties.title || element.properties.label || element.id}');
            this.elements.${element.id}.addEventListener('change', (e) => {
                console.log('🔽 تم اختيار قيمة: ' + e.target.value);
                this.validateField(e.target);
                ${prompt && prompt.trim() !== '' ? `this.executeCustomFunction('${element.id}', '${prompt}', e.target.value);` : ''}
            });
        } else {
            console.error('❌ لم يتم العثور على قائمة الاختيار: ${element.properties.title || element.properties.label || element.id}');
        }
`;
        break;
      case 'text':
        if (prompt && prompt.trim() !== '') {
          js += `        // نص تفاعلي: ${elementText}
        if (this.elements.${element.id}) {
            this.elements.${element.id}.addEventListener('click', () => {
                this.executeCustomFunction('${element.id}', '${prompt}');
            });
        }
`;
        }
        break;
    }
  });

  js += `
        console.log('✅ تم الانتهاء من ربط الأحداث');
    }

    // إعداد الرسوم المتحركة
    setupAnimations() {
        // تأثيرات الدخول
        const elements = document.querySelectorAll('.main-content > *');
        elements.forEach((el, index) => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            setTimeout(() => {
                el.style.transition = 'all 0.6s ease';
                el.style.opacity = '1';
                el.style.transform = 'translateY(0)';
            }, index * 100);
        });
    }
`;

  // إضافة وظائف الأزرار
  elements.filter((el: any) => el.type === 'button').forEach((element: any) => {
    const buttonText = element.properties.text || 'زر';
    const functionName = buttonText.replace(/\s+/g, '');

    js += `
    // وظيفة زر: ${buttonText}
    handle${functionName}Click() {
        console.log('تم الضغط على زر: ${buttonText}');

        // إضافة تأثير بصري
        this.addClickEffect(this.elements.${element.id});

        // تنفيذ الوظيفة حسب نوع الزر ونوع المشروع
        switch('${buttonText}') {
            case 'الرئيسية':
            case 'الصفحة الرئيسية':
            case 'Home':
                this.navigateToHome();
                break;
            case 'من نحن':
            case 'عن الشركة':
            case 'About':
                this.navigateToAbout();
                break;
            case 'اتصل بنا':
            case 'تواصل معنا':
            case 'Contact':
                this.navigateToContact();
                break;
            case 'إرسال':
            case 'حفظ':
            case 'Submit':
                this.submitForm();
                break;
            case 'بحث':
            case 'Search':
                this.performSearch();
                break;
            ${isEcommerce ? `
            case 'أضف للسلة':
            case 'Add to Cart':
                this.addToCart();
                break;
            case 'عرض السلة':
            case 'View Cart':
                this.viewCart();
                break;
            case 'الدفع':
            case 'Checkout':
                this.checkout();
                break;` : ''}
            ${isRestaurant ? `
            case 'احجز طاولة':
            case 'Book Table':
                this.bookTable();
                break;
            case 'اطلب الآن':
            case 'Order Now':
                this.placeOrder();
                break;
            case 'قائمة الطعام':
            case 'Menu':
                this.showMenu();
                break;` : ''}
            ${isEducation ? `
            case 'تسجيل طالب':
            case 'Register Student':
                this.registerStudent();
                break;
            case 'عرض الدرجات':
            case 'View Grades':
                this.viewGrades();
                break;
            case 'الحضور':
            case 'Attendance':
                this.markAttendance();
                break;` : ''}
            ${isMedical ? `
            case 'حجز موعد':
            case 'Book Appointment':
                this.bookAppointment();
                break;
            case 'السجل الطبي':
            case 'Medical Record':
                this.viewMedicalRecord();
                break;
            case 'الوصفات':
            case 'Prescriptions':
                this.viewPrescriptions();
                break;` : ''}
            default:
                this.showMessage('تم الضغط على: ${buttonText}', 'success');
        }
    }`;
  });

  js += `

    // التنقل بين الصفحات
    navigateToHome() {
        this.currentPage = 'home';
        this.updatePageContent('🏠 الصفحة الرئيسية', 'مرحباً بكم في موقعنا الإلكتروني');
    }

    navigateToAbout() {
        this.currentPage = 'about';
        this.updatePageContent('👥 من نحن', 'نحن شركة رائدة في مجال التكنولوجيا');
    }

    navigateToContact() {
        this.currentPage = 'contact';
        this.updatePageContent('📞 اتصل بنا', 'يمكنكم التواصل معنا عبر الوسائل التالية');
    }

    // تحديث محتوى الصفحة
    updatePageContent(title, content) {
        const mainContent = document.querySelector('.main-content');
        if (mainContent) {
            // إضافة تأثير انتقال
            mainContent.style.opacity = '0.5';
            setTimeout(() => {
                // تحديث العنوان إذا وجد
                const titleElement = document.querySelector('.header h1');
                if (titleElement) {
                    titleElement.textContent = title;
                }

                // إضافة محتوى ديناميكي
                this.addDynamicContent(content);

                mainContent.style.opacity = '1';
            }, 300);
        }
    }

    // إضافة محتوى ديناميكي
    addDynamicContent(content) {
        const existingContent = document.getElementById('dynamic-content');
        if (existingContent) {
            existingContent.remove();
        }

        const contentDiv = document.createElement('div');
        contentDiv.id = 'dynamic-content';
        contentDiv.className = 'dynamic-content';
        contentDiv.innerHTML = \`
            <div class="content-card">
                <h3>\${content}</h3>
                <p>تم تحديث المحتوى في: \${new Date().toLocaleString('ar-SA')}</p>
            </div>
        \`;

        document.querySelector('.main-content').appendChild(contentDiv);
    }
`;

  if (hasInputs) {
    js += `
    // إعداد التحقق من النماذج
    setupFormValidation() {
        const inputs = document.querySelectorAll('input, textarea');
        inputs.forEach(input => {
            input.addEventListener('blur', (e) => {
                this.validateField(e.target);
            });
        });
    }

    // التحقق من صحة الحقل
    validateField(field) {
        const value = field.value.trim();
        let isValid = true;
        let message = '';

        // إزالة رسائل الخطأ السابقة
        this.removeFieldError(field);

        // التحقق حسب نوع الحقل
        switch(field.type) {
            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                isValid = emailRegex.test(value);
                message = isValid ? '' : 'يرجى إدخال بريد إلكتروني صحيح';
                break;
            case 'tel':
                const phoneRegex = /^[0-9+\-\s()]+$/;
                isValid = phoneRegex.test(value) && value.length >= 10;
                message = isValid ? '' : 'يرجى إدخال رقم هاتف صحيح';
                break;
            default:
                isValid = value.length >= 2;
                message = isValid ? '' : 'يرجى إدخال نص صحيح (حد أدنى حرفان)';
        }

        // عرض رسالة الخطأ
        if (!isValid && message) {
            this.showFieldError(field, message);
        }

        return isValid;
    }

    // عرض خطأ الحقل
    showFieldError(field, message) {
        field.style.borderColor = '#e74c3c';
        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.textContent = message;
        errorDiv.style.color = '#e74c3c';
        errorDiv.style.fontSize = '12px';
        errorDiv.style.marginTop = '5px';
        field.parentNode.appendChild(errorDiv);
    }

    // إزالة خطأ الحقل
    removeFieldError(field) {
        field.style.borderColor = '#ddd';
        const errorDiv = field.parentNode.querySelector('.field-error');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    // إرسال النموذج
    submitForm() {
        const inputs = document.querySelectorAll('input, textarea');
        let isFormValid = true;

        inputs.forEach(input => {
            if (!this.validateField(input)) {
                isFormValid = false;
            }
        });

        if (isFormValid) {
            this.showMessage('تم إرسال النموذج بنجاح! ✅', 'success');
            // هنا يمكن إضافة كود إرسال البيانات للخادم
        } else {
            this.showMessage('يرجى تصحيح الأخطاء في النموذج', 'error');
        }
    }`;
  }

  if (hasTables) {
    js += `
    // تحميل بيانات الجدول
    loadTableData() {
        const tables = document.querySelectorAll('table');
        tables.forEach(table => {
            this.makeTableInteractive(table);
        });
    }

    // جعل الجدول تفاعلي
    makeTableInteractive(table) {
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach((row, index) => {
            row.addEventListener('click', () => {
                // إزالة التحديد السابق
                rows.forEach(r => r.classList.remove('selected-row'));
                // تحديد الصف الحالي
                row.classList.add('selected-row');
                console.log('تم تحديد الصف:', index + 1);
            });
        });
    }`;
  }

  js += `
    // البحث
    performSearch() {
        const searchTerm = prompt('أدخل كلمة البحث:');
        if (searchTerm) {
            this.showMessage(\`جاري البحث عن: \${searchTerm}\`, 'info');
            // هنا يمكن إضافة منطق البحث الفعلي
        }
    }

    // تنفيذ الوظائف المخصصة بناءً على البرومبت - وظائف حقيقية
    executeCustomFunction(elementId, prompt, value = null) {
        console.log(\`🚀 تنفيذ وظيفة حقيقية للعنصر \${elementId}: \${prompt}\`);

        const element = this.elements[elementId];
        if (!element) {
            console.error(\`❌ لم يتم العثور على العنصر: \${elementId}\`);
            this.showMessage(\`خطأ: لم يتم العثور على العنصر\`, 'error');
            return;
        }

        console.log(\`✅ تم العثور على العنصر:\`, element);

        // أولاً: محاولة تنفيذ الوظيفة بالذكاء الاصطناعي
        if (this.executeAIFunction(element, prompt, value)) {
            return; // تم التنفيذ بنجاح
        }

        // ثانياً: التحقق من الكلمات المفتاحية كخيار احتياطي
        const lowerPrompt = prompt.toLowerCase();

        // === وظائف التواصل الحقيقية ===
        if (lowerPrompt.includes('واتساب') || lowerPrompt.includes('whatsapp') || lowerPrompt.includes('واتس') ||
            lowerPrompt.includes('الواتساب') || lowerPrompt.includes('واتس اب') || lowerPrompt.includes('wa.me')) {
            console.log('🎯 تم اكتشاف طلب واتساب');
            this.openWhatsApp(prompt);
        }
        // وظائف الإيميل الحقيقية
        else if (lowerPrompt.includes('ايميل') || lowerPrompt.includes('email') || lowerPrompt.includes('بريد') ||
                 lowerPrompt.includes('الايميل') || lowerPrompt.includes('إيميل') || lowerPrompt.includes('الإيميل') ||
                 lowerPrompt.includes('بريد الكتروني') || lowerPrompt.includes('البريد الالكتروني')) {
            console.log('🎯 تم اكتشاف طلب إيميل');
            this.openEmail(prompt);
        }
        // وظائف الخرائط الحقيقية
        else if (lowerPrompt.includes('خريطة') || lowerPrompt.includes('موقع') || lowerPrompt.includes('maps') || lowerPrompt.includes('عنوان')) {
            this.openMaps(prompt);
        }
        // وظائف الاتصال الحقيقية
        else if (lowerPrompt.includes('اتصال') || lowerPrompt.includes('هاتف') || lowerPrompt.includes('call') || lowerPrompt.includes('phone')) {
            this.makeCall(prompt);
        }
        // وظائف المواقع الحقيقية
        else if (lowerPrompt.includes('موقع') || lowerPrompt.includes('رابط') || lowerPrompt.includes('website') || lowerPrompt.includes('url')) {
            this.openWebsite(prompt);
        }
        // وظائف التحميل الحقيقية
        else if (lowerPrompt.includes('تحميل') || lowerPrompt.includes('download') || lowerPrompt.includes('ملف')) {
            this.downloadFile(prompt);
        }
        // وظائف المشاركة الحقيقية
        else if (lowerPrompt.includes('مشاركة') || lowerPrompt.includes('share') || lowerPrompt.includes('شارك')) {
            this.shareContent(prompt);
        }
        // === وظائف التصميم ===
        // وظائف تغيير اللون
        else if (lowerPrompt.includes('لون') || lowerPrompt.includes('color')) {
            this.changeElementColor(element, prompt);
        }
        // وظائف الحركة والتحريك
        else if (lowerPrompt.includes('حرك') || lowerPrompt.includes('move') || lowerPrompt.includes('انتقل')) {
            this.animateElement(element, prompt);
        }
        // وظائف تغيير النص
        else if (lowerPrompt.includes('نص') || lowerPrompt.includes('text') || lowerPrompt.includes('كتابة')) {
            this.changeElementText(element, prompt, value);
        }
        // وظائف الإخفاء والإظهار
        else if (lowerPrompt.includes('اخف') || lowerPrompt.includes('اظهر') || lowerPrompt.includes('hide') || lowerPrompt.includes('show')) {
            this.toggleElementVisibility(element, prompt);
        }
        // وظائف تغيير الحجم
        else if (lowerPrompt.includes('حجم') || lowerPrompt.includes('size') || lowerPrompt.includes('كبر') || lowerPrompt.includes('صغر')) {
            this.resizeElement(element, prompt);
        }
        // وظيفة افتراضية
        else {
            this.executeGenericFunction(element, prompt, value);
        }
    }

    // تغيير لون العنصر
    changeElementColor(element, prompt) {
        const colors = {
            'أحمر': '#e74c3c', 'red': '#e74c3c',
            'أزرق': '#3498db', 'blue': '#3498db',
            'أخضر': '#27ae60', 'green': '#27ae60',
            'أصفر': '#f1c40f', 'yellow': '#f1c40f',
            'بنفسجي': '#9b59b6', 'purple': '#9b59b6',
            'برتقالي': '#e67e22', 'orange': '#e67e22',
            'وردي': '#e91e63', 'pink': '#e91e63',
            'رمادي': '#95a5a6', 'gray': '#95a5a6'
        };

        let targetColor = '#3498db';
        for (const [colorName, colorValue] of Object.entries(colors)) {
            if (prompt.includes(colorName)) {
                targetColor = colorValue;
                break;
            }
        }

        if (prompt.includes('خلفية') || prompt.includes('background')) {
            element.style.backgroundColor = targetColor;
        } else {
            element.style.color = targetColor;
        }

        this.showMessage(\`تم تغيير اللون إلى \${targetColor}\`, 'success');
    }

    // تحريك العنصر
    animateElement(element, prompt) {
        const animations = [
            'fadeIn', 'slideInLeft', 'slideInRight', 'slideInUp', 'slideInDown',
            'bounceIn', 'rotateIn', 'zoomIn', 'pulse', 'shake'
        ];

        const randomAnimation = animations[Math.floor(Math.random() * animations.length)];

        element.style.animation = \`\${randomAnimation} 1s ease\`;

        setTimeout(() => {
            element.style.animation = '';
        }, 1000);

        this.showMessage('تم تحريك العنصر', 'success');
    }

    // تغيير نص العنصر
    changeElementText(element, prompt, value = null) {
        if (value) {
            element.textContent = value;
        } else if (prompt.includes('وقت') || prompt.includes('time')) {
            element.textContent = new Date().toLocaleString('ar-SA');
        } else if (prompt.includes('تاريخ') || prompt.includes('date')) {
            element.textContent = new Date().toLocaleDateString('ar-SA');
        } else {
            const newText = prompt('أدخل النص الجديد:');
            if (newText) {
                element.textContent = newText;
            }
        }

        this.showMessage('تم تغيير النص', 'success');
    }

    // إخفاء/إظهار العنصر
    toggleElementVisibility(element, prompt) {
        if (prompt.includes('اخف') || prompt.includes('hide')) {
            element.style.opacity = '0';
            element.style.pointerEvents = 'none';
            this.showMessage('تم إخفاء العنصر', 'info');
        } else {
            element.style.opacity = '1';
            element.style.pointerEvents = 'auto';
            this.showMessage('تم إظهار العنصر', 'info');
        }
    }

    // تغيير حجم العنصر
    resizeElement(element, prompt) {
        if (prompt.includes('كبر') || prompt.includes('bigger')) {
            element.style.transform = 'scale(1.2)';
        } else if (prompt.includes('صغر') || prompt.includes('smaller')) {
            element.style.transform = 'scale(0.8)';
        } else {
            element.style.transform = 'scale(1)';
        }

        this.showMessage('تم تغيير حجم العنصر', 'success');
    }

    // تفاعل عام
    handleGenericInteraction(element, prompt) {
        element.style.background = 'linear-gradient(45deg, #ff6b6b, #4ecdc4)';
        element.style.transform = 'rotate(5deg)';

        setTimeout(() => {
            element.style.transform = 'rotate(0deg)';
        }, 500);

        this.showMessage(\`تم تنفيذ: \${prompt}\`, 'success');
    }

    // رسالة مخصصة
    showCustomMessage(prompt) {
        const message = prompt.replace(/رسالة|تنبيه|alert/gi, '').trim();
        this.showMessage(message || 'رسالة مخصصة', 'info');
    }

    // تحقق مخصص
    validateCustom(element, prompt, value) {
        if (value) {
            const isValid = value.length > 0;
            element.style.borderColor = isValid ? '#27ae60' : '#e74c3c';
            this.showMessage(isValid ? 'البيانات صحيحة ✅' : 'البيانات غير صحيحة ❌', isValid ? 'success' : 'error');
        }
    }

    // تنفيذ الوظائف بالذكاء الاصطناعي - تحليل ذكي للبرومبت
    executeAIFunction(element, prompt, value = null) {
        console.log(\`🤖 تحليل البرومبت بالذكاء الاصطناعي: \${prompt}\`);

        try {
            // تحليل البرومبت وتوليد الكود المناسب
            const generatedCode = this.generateCodeFromPrompt(prompt, element, value);

            if (generatedCode) {
                console.log(\`📝 كود مولد: \${generatedCode}\`);

                // تنفيذ الكود المولد
                const executeFunction = new Function('element', 'value', 'showMessage', generatedCode);
                executeFunction(element, value, (msg, type) => this.showMessage(msg, type));

                console.log(\`✅ تم تنفيذ الوظيفة بالذكاء الاصطناعي بنجاح\`);
                return true;
            }
        } catch (error) {
            console.error(\`❌ خطأ في تنفيذ الوظيفة بالذكاء الاصطناعي:\`, error);
        }

        return false; // فشل في التنفيذ
    }

    // توليد الكود من البرومبت
    generateCodeFromPrompt(prompt, element, value) {
        const lowerPrompt = prompt.toLowerCase();

        // استخراج الأرقام من البرومبت
        const numbers = prompt.match(/\\\\d+/g) || [];
        const phoneNumbers = numbers.filter(num => num.length >= 10);

        // استخراج الإيميلات من البرومبت
        const emails = prompt.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}/g) || [];

        // استخراج الروابط من البرومبت
        const urls = prompt.match(/https?:\\/\\/[^\\\\s]+/g) || [];

        // تحليل نوع الطلب وتوليد الكود المناسب

        // طلبات الواتساب
        if (lowerPrompt.includes('واتساب') || lowerPrompt.includes('whatsapp') || lowerPrompt.includes('واتس')) {
            const phone = phoneNumbers[0] || '966501234567';
            const message = this.extractMessage(prompt) || 'مرحباً';
            return \`
                console.log('📱 فتح واتساب للرقم: \${phone}');
                const whatsappUrl = 'https://wa.me/\${phone}?text=' + encodeURIComponent('\${message}');
                window.location.href = whatsappUrl;
                showMessage('تم فتح واتساب للرقم: \${phone} 📱', 'success');
            \`;
        }

        // طلبات الإيميل
        if (lowerPrompt.includes('ايميل') || lowerPrompt.includes('email') || lowerPrompt.includes('بريد')) {
            const email = emails[0] || '<EMAIL>';
            const subject = this.extractSubject(prompt) || 'استفسار من الموقع';
            const body = this.extractMessage(prompt) || 'مرحباً، أود الاستفسار عن خدماتكم.';
            return \`
                console.log('📧 فتح الإيميل: \${email}');
                const mailtoUrl = 'mailto:\${email}?subject=' + encodeURIComponent('\${subject}') + '&body=' + encodeURIComponent('\${body}');
                window.location.href = mailtoUrl;
                showMessage('تم فتح الإيميل: \${email} 📧', 'success');
            \`;
        }

        // طلبات الاتصال
        if (lowerPrompt.includes('اتصال') || lowerPrompt.includes('اتصل') || lowerPrompt.includes('call') || lowerPrompt.includes('هاتف')) {
            const phone = phoneNumbers[0] || '966501234567';
            return \`
                console.log('📞 إجراء اتصال للرقم: \${phone}');
                window.location.href = 'tel:\${phone}';
                showMessage('جاري الاتصال بالرقم: \${phone} 📞', 'success');
            \`;
        }

        // طلبات فتح الروابط
        if (urls.length > 0 || lowerPrompt.includes('رابط') || lowerPrompt.includes('موقع') || lowerPrompt.includes('website')) {
            const url = urls[0] || this.extractUrl(prompt);
            if (url) {
                return \`
                    console.log('🌐 فتح الرابط: \${url}');
                    window.open('\${url}', '_blank');
                    showMessage('تم فتح الرابط: \${url} 🌐', 'success');
                \`;
            }
        }

        // طلبات تغيير النص
        if (lowerPrompt.includes('غير النص') || lowerPrompt.includes('اكتب') || lowerPrompt.includes('نص')) {
            const newText = this.extractNewText(prompt);
            if (newText) {
                return \`
                    console.log('✏️ تغيير النص إلى: \${newText}');
                    element.textContent = '\${newText}';
                    showMessage('تم تغيير النص بنجاح ✏️', 'success');
                \`;
            }
        }

        // طلبات تغيير اللون
        if (lowerPrompt.includes('لون') || lowerPrompt.includes('color')) {
            const color = this.extractColor(prompt);
            if (color) {
                return \`
                    console.log('🎨 تغيير اللون إلى: \${color}');
                    element.style.backgroundColor = '\${color}';
                    showMessage('تم تغيير اللون إلى \${color} 🎨', 'success');
                \`;
            }
        }

        // طلبات الإخفاء والإظهار
        if (lowerPrompt.includes('اخف') || lowerPrompt.includes('hide')) {
            return \`
                console.log('👻 إخفاء العنصر');
                element.style.display = 'none';
                showMessage('تم إخفاء العنصر 👻', 'info');
            \`;
        }

        if (lowerPrompt.includes('اظهر') || lowerPrompt.includes('show')) {
            return \`
                console.log('👁️ إظهار العنصر');
                element.style.display = 'block';
                showMessage('تم إظهار العنصر 👁️', 'info');
            \`;
        }

        // طلبات عامة - محاولة تنفيذ أي طلب آخر
        return this.generateGenericCode(prompt, element);
    }

    // وظيفة عامة
    executeGenericFunction(element, prompt, value) {
        console.log(\`تنفيذ وظيفة عامة: \${prompt}\`);

        // إضافة تأثير بصري
        element.style.boxShadow = '0 0 20px rgba(52, 152, 219, 0.8)';
        element.style.transition = 'all 0.3s ease';

        setTimeout(() => {
            element.style.boxShadow = '';
        }, 1000);

        this.showMessage(\`تم تنفيذ: \${prompt}\`, 'success');
    }

    // === وظائف مساعدة لاستخراج البيانات من البرومبت ===

    extractMessage(prompt) {
        // البحث عن رسالة محددة في البرومبت
        if (prompt.includes('رسالة:')) {
            return prompt.split('رسالة:')[1].trim();
        }
        if (prompt.includes('message:')) {
            return prompt.split('message:')[1].trim();
        }
        if (prompt.includes('قل:') || prompt.includes('اكتب:')) {
            return prompt.split(/قل:|اكتب:/)[1].trim();
        }
        return null;
    }

    extractSubject(prompt) {
        // البحث عن موضوع محدد في البرومبت
        if (prompt.includes('موضوع:')) {
            return prompt.split('موضوع:')[1].split(/رسالة:|message:/)[0].trim();
        }
        if (prompt.includes('subject:')) {
            return prompt.split('subject:')[1].split(/رسالة:|message:/)[0].trim();
        }
        return null;
    }

    extractNewText(prompt) {
        // البحث عن نص جديد في البرومبت
        const patterns = [
            /غير النص إلى[:\\s]+(.+)/i,
            /اكتب[:\\s]+(.+)/i,
            /النص الجديد[:\\s]+(.+)/i,
            /change text to[:\\s]+(.+)/i,
            /write[:\\s]+(.+)/i
        ];

        for (const pattern of patterns) {
            const match = prompt.match(pattern);
            if (match) return match[1].trim();
        }
        return null;
    }

    extractColor(prompt) {
        // البحث عن لون محدد في البرومبت
        const colorMap = {
            'أحمر': 'red', 'احمر': 'red', 'red': 'red',
            'أزرق': 'blue', 'ازرق': 'blue', 'blue': 'blue',
            'أخضر': 'green', 'اخضر': 'green', 'green': 'green',
            'أصفر': 'yellow', 'اصفر': 'yellow', 'yellow': 'yellow',
            'برتقالي': 'orange', 'orange': 'orange',
            'بنفسجي': 'purple', 'purple': 'purple',
            'وردي': 'pink', 'pink': 'pink',
            'أسود': 'black', 'اسود': 'black', 'black': 'black',
            'أبيض': 'white', 'ابيض': 'white', 'white': 'white'
        };

        const lowerPrompt = prompt.toLowerCase();
        for (const [arabic, english] of Object.entries(colorMap)) {
            if (lowerPrompt.includes(arabic)) {
                return english;
            }
        }

        // البحث عن كود لون hex
        const hexMatch = prompt.match(/#[0-9a-fA-F]{6}/);
        if (hexMatch) return hexMatch[0];

        return null;
    }

    extractUrl(prompt) {
        // البحث عن رابط في البرومبت
        const urlPatterns = [
            /انتقل إلى[:\\s]+(\\S+)/i,
            /افتح[:\\s]+(\\S+)/i,
            /رابط[:\\s]+(\\S+)/i,
            /موقع[:\\s]+(\\S+)/i,
            /go to[:\\s]+(\\S+)/i,
            /open[:\\s]+(\\S+)/i,
            /visit[:\\s]+(\\S+)/i
        ];

        for (const pattern of urlPatterns) {
            const match = prompt.match(pattern);
            if (match) {
                let url = match[1].trim();
                if (!url.startsWith('http')) {
                    url = 'https://' + url;
                }
                return url;
            }
        }
        return null;
    }

    generateGenericCode(prompt, element) {
        // توليد كود عام لأي طلب آخر
        console.log(\`🔧 توليد كود عام للطلب: \${prompt}\`);

        return \`
            console.log('🚀 تنفيذ طلب عام: \${prompt}');

            // إضافة تأثير بصري
            element.style.boxShadow = '0 0 20px rgba(52, 152, 219, 0.8)';
            element.style.transition = 'all 0.3s ease';
            element.style.transform = 'scale(1.05)';

            setTimeout(() => {
                element.style.boxShadow = '';
                element.style.transform = 'scale(1)';
            }, 1000);

            showMessage('تم تنفيذ: \${prompt} ✨', 'success');

            // محاولة تنفيذ الطلب كما هو
            try {
                // إذا كان الطلب يحتوي على كود JavaScript، جرب تنفيذه
                if ('\${prompt}'.includes('alert') || '\${prompt}'.includes('console.log')) {
                    eval('\${prompt}');
                }
            } catch (e) {
                console.log('تم تنفيذ الطلب بشكل عام');
            }
        \`;
    }

    // === وظائف التواصل الحقيقية ===

    // فتح واتساب حقيقي
    openWhatsApp(prompt) {
        // استخراج رقم الهاتف من البرومبت - تحسين استخراج الأرقام
        const phoneMatch = prompt.match(/\\\\d{10,15}/g);
        const defaultPhone = '966501234567'; // رقم افتراضي
        let phone = defaultPhone;

        if (phoneMatch && phoneMatch.length > 0) {
            // أخذ أطول رقم موجود (عادة رقم الهاتف الكامل)
            phone = phoneMatch.reduce((longest, current) =>
                current.length > longest.length ? current : longest
            );
        }

        console.log(\`📱 رقم الواتساب المستخرج: \${phone} من البرومبت: \${prompt}\`);

        // استخراج الرسالة من البرومبت
        let message = 'مرحباً';
        if (prompt.includes('رسالة:')) {
            message = prompt.split('رسالة:')[1].trim();
        } else if (prompt.includes('message:')) {
            message = prompt.split('message:')[1].trim();
        }

        const whatsappUrl = \`https://wa.me/\${phone}?text=\${encodeURIComponent(message)}\`;
        // استخدام window.location.href للتوافق مع الكود المولد في المحرر
        window.location.href = whatsappUrl;

        this.showMessage(\`تم فتح واتساب للرقم: \${phone} 📱\`, 'success');
    }

    // فتح الإيميل حقيقي
    openEmail(prompt) {
        // استخراج الإيميل من البرومبت - تحسين استخراج الإيميل
        const emailMatch = prompt.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}/g);
        const defaultEmail = '<EMAIL>';
        let email = defaultEmail;

        if (emailMatch && emailMatch.length > 0) {
            email = emailMatch[0]; // أخذ أول إيميل موجود
        }

        console.log(\`📧 الإيميل المستخرج: \${email} من البرومبت: \${prompt}\`);

        // استخراج الموضوع والرسالة
        let subject = 'استفسار من الموقع';
        let body = 'مرحباً،\n\nأود الاستفسار عن خدماتكم.\n\nشكراً';

        if (prompt.includes('موضوع:')) {
            subject = prompt.split('موضوع:')[1].split('رسالة:')[0].trim();
        }
        if (prompt.includes('رسالة:')) {
            body = prompt.split('رسالة:')[1].trim();
        }

        const mailtoUrl = \`mailto:\${email}?subject=\${encodeURIComponent(subject)}&body=\${encodeURIComponent(body)}\`;
        window.location.href = mailtoUrl;

        this.showMessage(\`تم فتح الإيميل: \${email} 📧\`, 'success');
    }

    // فتح الخرائط حقيقي
    openMaps(prompt) {
        // استخراج العنوان من البرومبت
        let address = 'الرياض، السعودية';
        if (prompt.includes('عنوان:')) {
            address = prompt.split('عنوان:')[1].trim();
        } else if (prompt.includes('address:')) {
            address = prompt.split('address:')[1].trim();
        }

        const mapsUrl = \`https://www.google.com/maps/search/\${encodeURIComponent(address)}\`;
        window.open(mapsUrl, '_blank');

        this.showMessage(\`تم فتح الخرائط للعنوان: \${address} 🗺️\`, 'success');
    }

    // إجراء اتصال حقيقي
    makeCall(prompt) {
        // استخراج رقم الهاتف من البرومبت
        const phoneMatch = prompt.match(/\d{10,15}/);
        const defaultPhone = '966501234567';
        const phone = phoneMatch ? phoneMatch[0] : defaultPhone;

        const telUrl = \`tel:\${phone}\`;
        window.location.href = telUrl;

        this.showMessage(\`جاري الاتصال بالرقم: \${phone} 📞\`, 'success');
    }

    // فتح موقع ويب حقيقي
    openWebsite(prompt) {
        // استخراج الرابط من البرومبت
        const urlMatch = prompt.match(/https?:\/\/[^\s]+/);
        let url = 'https://www.google.com';

        if (urlMatch) {
            url = urlMatch[0];
        } else if (prompt.includes('رابط:')) {
            url = prompt.split('رابط:')[1].trim();
        } else if (prompt.includes('url:')) {
            url = prompt.split('url:')[1].trim();
        }

        // إضافة https إذا لم يكن موجود
        if (!url.startsWith('http')) {
            url = 'https://' + url;
        }

        window.open(url, '_blank');
        this.showMessage(\`تم فتح الموقع: \${url} 🌐\`, 'success');
    }

    // تحميل ملف حقيقي
    downloadFile(prompt) {
        // استخراج رابط الملف من البرومبت
        let fileUrl = '#';
        let fileName = 'ملف.pdf';

        if (prompt.includes('ملف:')) {
            fileUrl = prompt.split('ملف:')[1].trim();
        } else if (prompt.includes('file:')) {
            fileUrl = prompt.split('file:')[1].trim();
        }

        if (prompt.includes('اسم:')) {
            fileName = prompt.split('اسم:')[1].trim();
        }

        // إنشاء رابط تحميل
        const link = document.createElement('a');
        link.href = fileUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        this.showMessage(\`تم تحميل الملف: \${fileName} 📥\`, 'success');
    }

    // مشاركة المحتوى حقيقي
    shareContent(prompt) {
        // استخراج المحتوى للمشاركة
        let shareText = 'شاهد هذا الموقع الرائع!';
        let shareUrl = window.location.href;

        if (prompt.includes('نص:')) {
            shareText = prompt.split('نص:')[1].trim();
        }

        // استخدام Web Share API إذا كان متاح
        if (navigator.share) {
            navigator.share({
                title: document.title,
                text: shareText,
                url: shareUrl
            }).then(() => {
                this.showMessage('تم مشاركة المحتوى بنجاح! 📤', 'success');
            }).catch(() => {
                this.fallbackShare(shareText, shareUrl);
            });
        } else {
            this.fallbackShare(shareText, shareUrl);
        }
    }

    // مشاركة احتياطية
    fallbackShare(text, url) {
        // نسخ الرابط للحافظة
        navigator.clipboard.writeText(url).then(() => {
            this.showMessage('تم نسخ الرابط للحافظة! 📋', 'success');
        }).catch(() => {
            // فتح نوافذ المشاركة الاجتماعية
            const shareUrls = {
                twitter: \`https://twitter.com/intent/tweet?text=\${encodeURIComponent(text)}&url=\${encodeURIComponent(url)}\`,
                facebook: \`https://www.facebook.com/sharer/sharer.php?u=\${encodeURIComponent(url)}\`,
                linkedin: \`https://www.linkedin.com/sharing/share-offsite/?url=\${encodeURIComponent(url)}\`
            };

            // فتح تويتر كمثال
            window.open(shareUrls.twitter, '_blank');
            this.showMessage('تم فتح نافذة المشاركة! 🐦', 'success');
        });
    }

    ${isEcommerce ? `
    // === وظائف المتجر الإلكتروني ===
    addToCart() {
        const product = { id: Date.now(), name: 'منتج تجريبي', price: 100 };
        this.cart.push(product);
        this.cartTotal += product.price;
        this.updateCartDisplay();
        this.showMessage('تم إضافة المنتج للسلة 🛒', 'success');
    }

    viewCart() {
        const cartContent = this.cart.length > 0
            ? \`السلة تحتوي على \${this.cart.length} منتج بقيمة \${this.cartTotal} ريال\`
            : 'السلة فارغة';
        this.showMessage(cartContent, 'info');
    }

    checkout() {
        if (this.cart.length === 0) {
            this.showMessage('السلة فارغة!', 'error');
            return;
        }
        this.showMessage(\`تم تأكيد الطلب بقيمة \${this.cartTotal} ريال ✅\`, 'success');
        this.cart = [];
        this.cartTotal = 0;
        this.updateCartDisplay();
    }

    updateCartDisplay() {
        const cartElement = document.querySelector('.cart-counter');
        if (cartElement) {
            cartElement.textContent = this.cart.length;
        }
    }` : ''}

    ${isRestaurant ? `
    // === وظائف المطعم ===
    bookTable() {
        const tableNumber = Math.floor(Math.random() * 10) + 1;
        const reservation = {
            id: Date.now(),
            table: tableNumber,
            time: new Date().toLocaleString('ar-SA')
        };
        this.tables.push(reservation);
        this.showMessage(\`تم حجز الطاولة رقم \${tableNumber} 🍽️\`, 'success');
    }

    placeOrder() {
        const orderNumber = Math.floor(Math.random() * 1000) + 1;
        const order = {
            id: Date.now(),
            number: orderNumber,
            items: ['وجبة تجريبية'],
            total: 50
        };
        this.orders.push(order);
        this.showMessage(\`تم تأكيد الطلب رقم \${orderNumber} 🍕\`, 'success');
    }

    showMenu() {
        this.showMessage('عرض قائمة الطعام... 📋', 'info');
        // يمكن إضافة عرض قائمة ديناميكية هنا
    }` : ''}

    ${isEducation ? `
    // === وظائف النظام التعليمي ===
    registerStudent() {
        const studentId = Math.floor(Math.random() * 10000) + 1;
        const student = {
            id: studentId,
            name: 'طالب جديد',
            grade: 'الصف الأول',
            registrationDate: new Date().toLocaleDateString('ar-SA')
        };
        this.students.push(student);
        this.showMessage(\`تم تسجيل الطالب برقم \${studentId} 🎓\`, 'success');
    }

    viewGrades() {
        this.showMessage('عرض درجات الطلاب... 📊', 'info');
        // يمكن إضافة عرض الدرجات هنا
    }

    markAttendance() {
        const attendanceCount = Math.floor(Math.random() * 30) + 1;
        this.showMessage(\`تم تسجيل حضور \${attendanceCount} طالب ✅\`, 'success');
    }` : ''}

    ${isMedical ? `
    // === وظائف النظام الطبي ===
    bookAppointment() {
        const appointmentId = Math.floor(Math.random() * 1000) + 1;
        const appointment = {
            id: appointmentId,
            patientName: 'مريض جديد',
            doctor: 'د. أحمد محمد',
            date: new Date().toLocaleDateString('ar-SA'),
            time: '10:00 ص'
        };
        this.appointments.push(appointment);
        this.showMessage(\`تم حجز الموعد رقم \${appointmentId} 🏥\`, 'success');
    }

    viewMedicalRecord() {
        this.showMessage('عرض السجل الطبي... 📋', 'info');
        // يمكن إضافة عرض السجل الطبي هنا
    }

    viewPrescriptions() {
        this.showMessage('عرض الوصفات الطبية... 💊', 'info');
        // يمكن إضافة عرض الوصفات هنا
    }` : ''}

    // إضافة تأثير النقر
    addClickEffect(element) {
        element.style.transform = 'scale(0.95)';
        setTimeout(() => {
            element.style.transform = 'scale(1)';
        }, 150);
    }

    // عرض الرسائل
    showMessage(message, type = 'info') {
        // إنشاء عنصر الرسالة
        const messageDiv = document.createElement('div');
        messageDiv.className = \`message message-\${type}\`;
        messageDiv.textContent = message;

        // تنسيق الرسالة
        Object.assign(messageDiv.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '15px 20px',
            borderRadius: '8px',
            color: 'white',
            fontWeight: 'bold',
            zIndex: '9999',
            transform: 'translateX(100%)',
            transition: 'transform 0.3s ease'
        });

        // ألوان حسب النوع
        const colors = {
            success: '#27ae60',
            error: '#e74c3c',
            info: '#3498db',
            warning: '#f39c12'
        };
        messageDiv.style.backgroundColor = colors[type] || colors.info;

        // إضافة للصفحة
        document.body.appendChild(messageDiv);

        // إظهار الرسالة
        setTimeout(() => {
            messageDiv.style.transform = 'translateX(0)';
        }, 100);

        // إخفاء الرسالة
        setTimeout(() => {
            messageDiv.style.transform = 'translateX(100%)';
            setTimeout(() => {
                messageDiv.remove();
            }, 300);
        }, 3000);
    }
}

// تشغيل التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.websiteManager = new WebsiteManager();
});

// إضافة أنماط CSS ديناميكية
const dynamicStyles = \`
    .selected-row {
        background-color: #e3f2fd !important;
    }

    .content-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin: 20px 0;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }

    .dynamic-content {
        animation: fadeInUp 0.6s ease;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
\`;

// إضافة الأنماط للصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = dynamicStyles;
document.head.appendChild(styleSheet);

// إنشاء وتشغيل مدير الموقع
document.addEventListener('DOMContentLoaded', () => {
    console.log('🔄 بدء تحميل مدير الموقع...');
    try {
        window.websiteManager = new ${getManagerClassName(analysis.projectType || 'موقع ويب عام')}();
        console.log('✅ تم تحميل مدير الموقع بنجاح');

        // اختبار سريع للتأكد من عمل الأزرار
        setTimeout(() => {
            const buttons = document.querySelectorAll('button[data-prompt]');
            console.log(\`🔍 تم العثور على \${buttons.length} أزرار تفاعلية\`);
            buttons.forEach((btn, index) => {
                console.log(\`زر \${index + 1}: \${btn.textContent} - البرومبت: \${btn.getAttribute('data-prompt')}\`);
            });
        }, 1000);

    } catch (error) {
        console.error('❌ خطأ في تحميل مدير الموقع:', error);
        alert('حدث خطأ في تحميل الموقع. يرجى إعادة تحميل الصفحة.');
    }
});`;

  return js;
}

function generateServer(analysis: any): string {
  return `// خادم Node.js/Express تم إنشاؤه بواسطة AI Website Builder
const express = require('express');
const cors = require('cors');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('.'));

// الصفحة الرئيسية
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

${analysis.apiEndpoints?.map((endpoint: any) => `
// ${endpoint.purpose}
app.${endpoint.method.toLowerCase()}('${endpoint.path}', (req, res) => {
    try {
        // معالجة الطلب
        console.log('${endpoint.purpose}:', req.body);

        // هنا يمكن إضافة منطق المعالجة
        res.json({
            success: true,
            message: 'تم تنفيذ العملية بنجاح',
            data: req.body
        });
    } catch (error) {
        console.error('خطأ في ${endpoint.purpose}:', error);
        res.status(500).json({
            success: false,
            message: 'حدث خطأ في الخادم'
        });
    }
});`).join('') || ''}

// تشغيل الخادم
app.listen(PORT, () => {
    console.log(\`الخادم يعمل على المنفذ \${PORT}\`);
    console.log(\`افتح المتصفح على: http://localhost:\${PORT}\`);
});`;
}

function generatePackageJson(projectName: string, projectDescription: string): string {
  return JSON.stringify({
    name: projectName?.toLowerCase().replace(/\s+/g, '-') || 'ai-generated-project',
    version: '1.0.0',
    description: projectDescription || 'مشروع تم إنشاؤه بواسطة AI Website Builder',
    main: 'server.js',
    scripts: {
      start: 'node server.js',
      dev: 'nodemon server.js'
    },
    dependencies: {
      express: '^4.18.2',
      cors: '^2.8.5'
    },
    devDependencies: {
      nodemon: '^3.0.1'
    },
    keywords: ['ai', 'website', 'builder', 'generated'],
    author: 'AI Website Builder',
    license: 'MIT'
  }, null, 2);
}

function generateDatabase(schema: any[]): string {
  let sql = `-- قاعدة بيانات تم إنشاؤها بواسطة AI Website Builder
-- تاريخ الإنشاء: ${new Date().toISOString()}

`;

  schema.forEach(table => {
    sql += `-- إنشاء جدول ${table.name}
CREATE TABLE IF NOT EXISTS ${table.name} (
`;

    table.fields?.forEach((field: any, index: number) => {
      const isLast = index === table.fields.length - 1;
      sql += `    ${field.name} ${field.type}${field.required ? ' NOT NULL' : ''}${field.unique ? ' UNIQUE' : ''}${isLast ? '' : ','}\n`;
    });

    sql += `);\n\n`;

    // إضافة بيانات تجريبية
    sql += `-- إدراج بيانات تجريبية في جدول ${table.name}
INSERT INTO ${table.name} (${table.fields?.map((f: any) => f.name).join(', ')}) VALUES
`;

    // بيانات تجريبية بسيطة
    const sampleData = table.fields?.map((field: any) => {
      switch (field.type.toLowerCase()) {
        case 'integer': return '1';
        case 'varchar': case 'text': return "'بيانات تجريبية'";
        case 'timestamp': return 'NOW()';
        default: return "'قيمة افتراضية'";
      }
    }).join(', ');

    sql += `    (${sampleData});\n\n`;
  });

  return sql;
}

function generateReadme(projectName: string, projectDescription: string, analysis: any): string {
  return `# ${projectName || 'مشروع مولد بالذكاء الاصطناعي'}

${projectDescription || 'هذا مشروع تم إنشاؤه بواسطة AI Website Builder'}

## 🎯 نوع المشروع
${analysis.projectType || 'تطبيق ويب تفاعلي'}

## 🚀 كيفية التشغيل

### متطلبات النظام
- Node.js (الإصدار 14 أو أحدث)
- npm أو yarn

### خطوات التشغيل

1. **تثبيت التبعيات:**
   \`\`\`bash
   npm install
   \`\`\`

2. **تشغيل الخادم:**
   \`\`\`bash
   npm start
   \`\`\`

3. **فتح المتصفح:**
   افتح المتصفح على: \`http://localhost:3000\`

## 📁 بنية المشروع

- \`index.html\` - الصفحة الرئيسية
- \`styles.css\` - ملف التنسيقات
- \`script.js\` - ملف JavaScript التفاعلي
- \`server.js\` - خادم Node.js/Express
- \`database.sql\` - هيكل قاعدة البيانات
- \`package.json\` - تبعيات المشروع

## 🔧 الميزات

${analysis.suggestedFeatures?.map((feature: string) => `- ${feature}`).join('\n') || '- واجهة مستخدم تفاعلية'}

## 🗄️ قاعدة البيانات

${analysis.databaseSchema?.map((table: any) => `
### جدول ${table.name}
${table.fields?.map((field: any) => `- **${field.name}**: ${field.type}${field.required ? ' (مطلوب)' : ''}`).join('\n')}
`).join('\n') || 'لا توجد قاعدة بيانات'}

## 🔗 واجهات البرمجة (API)

${analysis.apiEndpoints?.map((endpoint: any) => `
### ${endpoint.method} ${endpoint.path}
**الغرض:** ${endpoint.purpose}
**المعاملات:** ${endpoint.parameters?.join(', ') || 'لا توجد'}
`).join('\n') || 'لا توجد واجهات برمجة'}

## 📝 ملاحظات

- تم إنشاء هذا المشروع بواسطة AI Website Builder
- يمكن تخصيص وتطوير المشروع حسب احتياجاتك
- للدعم والمساعدة، راجع الوثائق

## 🎨 التخصيص

يمكنك تخصيص المشروع عبر:
- تعديل ملف \`styles.css\` لتغيير التصميم
- تطوير ملف \`script.js\` لإضافة وظائف جديدة
- تحديث \`server.js\` لإضافة API endpoints جديدة

---

**تم الإنشاء بواسطة:** AI Website Builder
**التاريخ:** ${new Date().toLocaleDateString('ar-SA')}
`;
}
