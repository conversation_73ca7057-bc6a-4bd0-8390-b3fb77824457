<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>النظام المبسط - 3 أحجام فقط</title>
    <style>
        /* تم إنشاء هذا الملف بواسطة AI Website Builder */
        /* النظام المبسط: موبايل، تابلت، كمبيوتر */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            width: 1280px;
            min-height: 720px;
            max-width: 1280px;
            position: relative;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.15);
            overflow: hidden;
            margin: auto;
            border: 3px solid rgba(255,255,255,0.2);
        }

        .main-content {
            position: relative;
            width: 100%;
            min-height: 720px;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        /* العناصر الأساسية - مواضع الكمبيوتر */
        #element_1 {
            position: absolute;
            left: 100px;
            top: 50px;
            width: 200px;
            height: 60px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        #element_2 {
            position: absolute;
            left: 400px;
            top: 150px;
            width: 300px;
            height: 40px;
            background: #10b981;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        #element_3 {
            position: absolute;
            left: 800px;
            top: 100px;
            width: 250px;
            height: 50px;
            background: #f59e0b;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 15px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
        }

        /* 🖥️ كمبيوتر: > 1024px */
        @media (min-width: 1025px) {
            /* مواضع الكمبيوتر المخصصة (إذا تم ترتيبها) */
            #element_1 {
                left: 150px !important;
                top: 80px !important;
                position: absolute !important;
            }

            #element_2 {
                left: 500px !important;
                top: 200px !important;
                position: absolute !important;
            }

            #element_3 {
                left: 900px !important;
                top: 120px !important;
                position: absolute !important;
            }
        }

        /* 📟 تابلت: 769px - 1024px */
        @media (min-width: 769px) and (max-width: 1024px) {
            .container {
                width: 100vw !important;
                max-width: 100vw !important;
                border-radius: 0 !important;
                box-shadow: none !important;
                border: none !important;
            }

            .main-content {
                padding: 20px !important;
            }

            /* ترتيب العناصر للتابلت */
            #element_1 {
                left: 50px !important;
                top: 50px !important;
                position: absolute !important;
            }

            #element_2 {
                left: 350px !important;
                top: 50px !important;
                position: absolute !important;
            }

            #element_3 {
                left: 200px !important;
                top: 150px !important;
                position: absolute !important;
            }
        }

        /* 📱 موبايل: ≤ 768px */
        @media (max-width: 768px) {
            body {
                padding: 0 !important;
                margin: 0 !important;
            }

            .container {
                margin: 0 !important;
                padding: 0 !important;
                width: 100vw !important;
                min-height: 100vh !important;
                max-width: 100vw !important;
                border-radius: 0 !important;
                box-shadow: none !important;
                border: none !important;
            }

            .main-content {
                min-height: 100vh !important;
                padding: 20px !important;
                margin: 0 !important;
            }

            /* ترتيب العناصر عمودياً في الموبايل */
            #element_1 {
                left: 20px !important;
                top: 50px !important;
                position: absolute !important;
                width: calc(100vw - 40px) !important;
                max-width: 300px !important;
            }

            #element_2 {
                left: 20px !important;
                top: 130px !important;
                position: absolute !important;
                width: calc(100vw - 40px) !important;
                max-width: 300px !important;
            }

            #element_3 {
                left: 20px !important;
                top: 210px !important;
                position: absolute !important;
                width: calc(100vw - 40px) !important;
                max-width: 300px !important;
            }
        }

        /* مؤشر النظام المبسط */
        .system-indicator {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 1000;
            font-family: monospace;
            text-align: center;
            min-width: 200px;
        }

        .system-indicator .title {
            font-weight: bold;
            margin-bottom: 5px;
            color: #fbbf24;
        }

    </style>
</head>
<body>
    <div class="system-indicator" id="systemIndicator">
        <div class="title">النظام المبسط</div>
        <div id="deviceInfo">🖥️ كمبيوتر: 1920×1080</div>
        <div id="breakpoints">3 أحجام فقط</div>
    </div>

    <div class="container">
        <main class="main-content">
            <button id="element_1">زر كمبيوتر 1</button>
            <button id="element_2">زر كمبيوتر 2</button>
            <button id="element_3">زر كمبيوتر 3</button>
        </main>
    </div>

    <script>
        // تحديث مؤشر النظام المبسط
        function updateSystemIndicator() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const deviceInfo = document.getElementById('deviceInfo');
            const breakpoints = document.getElementById('breakpoints');
            
            let deviceType = '';
            let emoji = '';
            let description = '';
            
            if (width <= 768) {
                deviceType = 'موبايل';
                emoji = '📱';
                description = '≤ 768px';
            } else if (width <= 1024) {
                deviceType = 'تابلت';
                emoji = '📟';
                description = '769px - 1024px';
            } else {
                deviceType = 'كمبيوتر';
                emoji = '🖥️';
                description = '> 1024px';
            }
            
            deviceInfo.textContent = `${emoji} ${deviceType}: ${width}×${height}`;
            breakpoints.textContent = description;
        }

        // تحديث عند التحميل
        updateSystemIndicator();

        // تحديث عند تغيير حجم الشاشة
        window.addEventListener('resize', updateSystemIndicator);

        // إضافة أحداث للأزرار
        document.getElementById('element_1').addEventListener('click', () => {
            alert('زر 1 - يتغير موضعه حسب الجهاز!');
        });

        document.getElementById('element_2').addEventListener('click', () => {
            alert('زر 2 - نظام مبسط: 3 أحجام فقط!');
        });

        document.getElementById('element_3').addEventListener('click', () => {
            alert('زر 3 - موبايل، تابلت، كمبيوتر!');
        });
    </script>
</body>
</html>
