{"pages": [{"id": "page_test", "name": "اختبار التمدد", "title": "اختبار الحاويات المتداخلة", "backgroundColor": "#f8fafc", "textColor": "#1e293b", "fontFamily": "<PERSON>l, sans-serif", "elements": [{"id": "outer_container", "type": "container", "x": 50, "y": 50, "width": 800, "height": 200, "zIndex": 1, "properties": {"backgroundColor": "#e5e7eb", "borderRadius": 8, "padding": 16, "layoutType": "flex-row", "justifyContent": "flex-start", "alignItems": "stretch", "gap": 16}}, {"id": "fixed_element", "type": "button", "x": 20, "y": 20, "width": 120, "height": 40, "zIndex": 2, "parentId": "outer_container", "properties": {"text": "عنصر ثابت", "fontSize": 14, "color": "#ffffff", "backgroundColor": "#3b82f6", "borderRadius": 6, "fontWeight": "600"}}, {"id": "nested_container", "type": "container", "x": 160, "y": 20, "width": 300, "height": 160, "zIndex": 2, "parentId": "outer_container", "properties": {"backgroundColor": "#ddd6fe", "borderRadius": 6, "padding": 12, "layoutType": "flex-col", "justifyContent": "center", "alignItems": "center", "gap": 8}}, {"id": "nested_button_1", "type": "button", "x": 10, "y": 10, "width": 100, "height": 30, "zIndex": 3, "parentId": "nested_container", "properties": {"text": "متداخل 1", "fontSize": 12, "color": "#ffffff", "backgroundColor": "#8b5cf6", "borderRadius": 4, "fontWeight": "600"}}, {"id": "nested_button_2", "type": "button", "x": 10, "y": 50, "width": 100, "height": 30, "zIndex": 3, "parentId": "nested_container", "properties": {"text": "متدا<PERSON>ل 2", "fontSize": 12, "color": "#ffffff", "backgroundColor": "#8b5cf6", "borderRadius": 4, "fontWeight": "600"}}, {"id": "nested_button_3", "type": "button", "x": 10, "y": 90, "width": 100, "height": 30, "zIndex": 3, "parentId": "nested_container", "properties": {"text": "متدا<PERSON>ل 3", "fontSize": 12, "color": "#ffffff", "backgroundColor": "#8b5cf6", "borderRadius": 4, "fontWeight": "600"}}, {"id": "another_fixed", "type": "button", "x": 480, "y": 20, "width": 120, "height": 40, "zIndex": 2, "parentId": "outer_container", "properties": {"text": "عنصر آخر", "fontSize": 14, "color": "#ffffff", "backgroundColor": "#10b981", "borderRadius": 6, "fontWeight": "600"}}]}]}