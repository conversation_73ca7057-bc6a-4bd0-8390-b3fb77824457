import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { element, prompt } = await request.json();

    if (!element || !prompt) {
      return NextResponse.json(
        { error: 'العنصر والبرومبت مطلوبان' },
        { status: 400 }
      );
    }

    // إرسال البرومبت للذكاء الاصطناعي لتوليد الكود
    const generatedCode = await generateElementCodeWithAI(element, prompt);

    return NextResponse.json({
      success: true,
      code: generatedCode
    });

  } catch (error) {
    console.error('خطأ في توليد كود العنصر:', error);
    return NextResponse.json(
      { error: 'حدث خطأ في توليد الكود' },
      { status: 500 }
    );
  }
}

// دالة لتوليد الكود باستخدام الذكاء الاصطناعي
async function generateElementCodeWithAI(element: any, prompt: string, availableElements?: any[]): Promise<string> {
  const { type, properties } = element;

  // إنشاء قائمة بالعناصر المتاحة للتفاعل
  let elementsContext = '';
  if (availableElements && availableElements.length > 0) {
    elementsContext = `

Available elements: ${availableElements.map(el => `${el.id} (${el.type})`).join(', ')}`;
  }

  // Optimized short prompt for cost efficiency
  const aiPrompt = `Create complete HTML page for ${type} element.

Element: id="${element.id}", type="${type}"
Request: ${prompt}${elementsContext}

Requirements:
- Complete HTML with CSS/JS
- Element centered, responsive
- Use id="${element.id}" for main element
- If targeting other elements, use exact IDs from list above
- Return only HTML code, start with <!DOCTYPE html>

Properties: ${JSON.stringify(properties)}`;

  try {
    // استدعاء OpenAI API
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        model: 'gpt-4.1-nano',
        messages: [
          {
            role: 'system',
            content: 'Expert web developer. Create clean HTML/CSS/JS code.'
          },
          {
            role: 'user',
            content: aiPrompt
          }
        ],
        max_tokens: 2000,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    let generatedCode = data.choices[0]?.message?.content || '';

    // تنظيف الرد من النصوص الإضافية والحصول على الكود فقط
    generatedCode = cleanAIResponse(generatedCode);

    return generatedCode;

  } catch (error) {
    console.error('خطأ في استدعاء OpenAI:', error);
    // fallback إلى الكود الأساسي في حالة الخطأ
    return generateBasicElementCode(element, prompt);
  }
}

// دالة مساعدة لترجمة نوع العنصر
function getElementTypeInArabic(type: string): string {
  const translations: { [key: string]: string } = {
    'button': 'زر',
    'text': 'نص',
    'image': 'صورة',
    'input': 'حقل إدخال',
    'container': 'حاوي'
  };
  return translations[type] || type;
}

// دالة لتنظيف رد الذكاء الاصطناعي والحصول على الكود فقط
function cleanAIResponse(response: string): string {
  try {
    console.log('🧹 تنظيف رد الذكاء الاصطناعي...');

    // إزالة النصوص التفسيرية في البداية والنهاية
    let cleanedResponse = response.trim();

    // البحث عن كود HTML كامل
    const htmlMatch = cleanedResponse.match(/<!DOCTYPE html>[\s\S]*?<\/html>/i);
    if (htmlMatch) {
      console.log('✅ تم العثور على كود HTML كامل');
      return htmlMatch[0];
    }

    // البحث عن كود HTML بدون DOCTYPE
    const htmlWithoutDoctypeMatch = cleanedResponse.match(/<html[\s\S]*?<\/html>/i);
    if (htmlWithoutDoctypeMatch) {
      console.log('✅ تم العثور على كود HTML بدون DOCTYPE');
      return `<!DOCTYPE html>\n${htmlWithoutDoctypeMatch[0]}`;
    }

    // البحث عن كود داخل ```html blocks
    const codeBlockMatch = cleanedResponse.match(/```html\s*([\s\S]*?)\s*```/i);
    if (codeBlockMatch && codeBlockMatch[1]) {
      console.log('✅ تم العثور على كود داخل code block');
      let code = codeBlockMatch[1].trim();

      // إضافة DOCTYPE إذا لم يكن موجود
      if (!code.includes('<!DOCTYPE')) {
        if (code.startsWith('<html')) {
          code = `<!DOCTYPE html>\n${code}`;
        }
      }

      return code;
    }

    // البحث عن أي كود HTML
    const anyHtmlMatch = cleanedResponse.match(/<[^>]+>[\s\S]*<\/[^>]+>/);
    if (anyHtmlMatch) {
      console.log('✅ تم العثور على كود HTML جزئي');
      return generateBasicElementCode({ id: 'temp', type: 'container' }, 'كود أساسي');
    }

    // إذا لم يتم العثور على كود صالح، إرجاع كود أساسي
    console.log('⚠️ لم يتم العثور على كود صالح، إرجاع كود أساسي');
    return generateBasicElementCode({ id: 'temp', type: 'container' }, 'كود أساسي');

  } catch (error) {
    console.error('❌ خطأ في تنظيف الرد:', error);
    return generateBasicElementCode({ id: 'temp', type: 'container' }, 'كود أساسي');
  }
}

// دالة احتياطية لتوليد كود أساسي
function generateBasicElementCode(element: any, prompt: string): string {
  const { type, properties } = element;

  let html = '';
  let css = '';
  let js = '';

  switch (type) {
    case 'text':
      html = `<div id="${element.id}" class="generated-element">${properties.text || 'نص تجريبي'}</div>`;
      css = `
        .generated-element {
          font-size: ${properties.fontSize || '16'}px;
          color: ${properties.color || '#333'};
          text-align: center;
          padding: 20px;
        }
      `;
      break;

    case 'button':
      html = `<button id="${element.id}" class="generated-element">${properties.text || 'زر'}</button>`;
      css = `
        .generated-element {
          padding: 12px 24px;
          font-size: 16px;
          border: none;
          border-radius: 6px;
          cursor: pointer;
          background: ${properties.backgroundColor || '#007bff'};
          color: ${properties.color || 'white'};
          transition: all 0.3s ease;
        }
        .generated-element:hover {
          opacity: 0.9;
          transform: translateY(-2px);
        }
      `;
      js = `
        document.getElementById('${element.id}').addEventListener('click', function() {
          alert('تم النقر على الزر!');
        });
      `;
      break;

    case 'image':
      html = `<img id="${element.id}" class="generated-element" src="${properties.src || '/placeholder.jpg'}" alt="${properties.alt || 'صورة'}" />`;
      css = `
        .generated-element {
          max-width: 100%;
          height: auto;
          border-radius: 8px;
          box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
      `;
      break;

    default:
      html = `<div id="${element.id}" class="generated-element">عنصر</div>`;
      css = `
        .generated-element {
          padding: 20px;
          border: 1px solid #ccc;
          border-radius: 4px;
          text-align: center;
        }
      `;
  }

  return `<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>معاينة العنصر</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        ${css}
    </style>
</head>
<body>
    ${html}
    <script>
        ${js}
    </script>
</body>
</html>`;
}
