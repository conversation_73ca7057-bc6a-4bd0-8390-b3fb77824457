
'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
// استيراد Hugeicons بالطريقة الصحيحة
import { HugeiconsIcon } from '@hugeicons/react';
const Icons = {
  Grid: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <rect x="3" y="3" width="7" height="7"/>
      <rect x="14" y="3" width="7" height="7"/>
      <rect x="14" y="14" width="7" height="7"/>
      <rect x="3" y="14" width="7" height="7"/>
    </svg>
  ),
  Settings: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <circle cx="12" cy="12" r="3"/>
      <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m17-4a4 4 0 0 0-8 0m8 8a4 4 0 0 0-8 0"/>
    </svg>
  ),
  Plus: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <line x1="12" y1="5" x2="12" y2="19"/>
      <line x1="5" y1="12" x2="19" y2="12"/>
    </svg>
  ),
  Text: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <polyline points="4,7 4,4 20,4 20,7"/>
      <line x1="9" y1="20" x2="15" y2="20"/>
      <line x1="12" y1="4" x2="12" y2="20"/>
    </svg>
  ),
  Image: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
      <circle cx="8.5" cy="8.5" r="1.5"/>
      <polyline points="21,15 16,10 5,21"/>
    </svg>
  ),
  Button: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <rect x="3" y="8" width="18" height="8" rx="2" ry="2"/>
      <path d="M7 12h10"/>
    </svg>
  ),
  Input: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <rect x="3" y="6" width="18" height="12" rx="2" ry="2"/>
      <line x1="7" y1="12" x2="17" y2="12"/>
    </svg>
  ),
  RotateLeft: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <path d="M2.5 2v6h6M2.66 15.57a10 10 0 1 0 .57-8.38"/>
    </svg>
  ),
  RotateRight: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <path d="M21.5 2v6h-6M21.34 15.57a10 10 0 1 1-.57-8.38"/>
    </svg>
  ),
  Reset: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/>
      <path d="M3 3v5h5"/>
    </svg>
  ),
  Magic: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <path d="M15 4V2m0 16v-2m8-6h-2M4 12H2m15.314-6.314l-1.414 1.414M7.757 16.243l-1.414 1.414m0-11.314l1.414 1.414m9.9 9.9l-1.414-1.414M22 12a3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3 3 3 0 0 0 3-3Z"/>
    </svg>
  ),
  Tag: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"/>
      <line x1="7" y1="7" x2="7.01" y2="7"/>
    </svg>
  ),
  FileText: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
      <polyline points="14,2 14,8 20,8"/>
      <line x1="16" y1="13" x2="8" y2="13"/>
      <line x1="16" y1="17" x2="8" y2="17"/>
      <line x1="10" y1="9" x2="8" y2="9"/>
    </svg>
  ),
  Palette: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <circle cx="13.5" cy="6.5" r=".5"/>
      <circle cx="17.5" cy="10.5" r=".5"/>
      <circle cx="8.5" cy="7.5" r=".5"/>
      <circle cx="6.5" cy="12.5" r=".5"/>
      <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z"/>
    </svg>
  ),
  Type: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <polyline points="4,7 4,4 20,4 20,7"/>
      <line x1="9" y1="20" x2="15" y2="20"/>
      <line x1="12" y1="4" x2="12" y2="20"/>
    </svg>
  ),
  Code: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <polyline points="16,18 22,12 16,6"/>
      <polyline points="8,6 2,12 8,18"/>
    </svg>
  ),
  Resize: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
    </svg>
  ),
  Repeat: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <path d="M17 1l4 4-4 4"/>
      <path d="M3 11V9a4 4 0 0 1 4-4h14"/>
      <path d="M7 23l-4-4 4-4"/>
      <path d="M21 13v2a4 4 0 0 1-4 4H3"/>
    </svg>
  ),
  MapPin: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
      <circle cx="12" cy="10" r="3"/>
    </svg>
  ),
  FilePlus: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
      <polyline points="14,2 14,8 20,8"/>
      <line x1="12" y1="11" x2="12" y2="17"/>
      <line x1="9" y1="14" x2="15" y2="14"/>
    </svg>
  ),
  Download: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
      <polyline points="7,10 12,15 17,10"/>
      <line x1="12" y1="15" x2="12" y2="3"/>
    </svg>
  ),
  Upload: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
      <polyline points="17,8 12,3 7,8"/>
      <line x1="12" y1="3" x2="12" y2="15"/>
    </svg>
  ),
  Eye: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
      <circle cx="12" cy="12" r="3"/>
    </svg>
  ),
  Bot: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <rect x="3" y="11" width="18" height="10" rx="2" ry="2"/>
      <circle cx="12" cy="5" r="2"/>
      <path d="M12 7v4"/>
      <line x1="8" y1="16" x2="8" y2="16"/>
      <line x1="16" y1="16" x2="16" y2="16"/>
    </svg>
  ),
  Search: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <circle cx="11" cy="11" r="8"/>
      <path d="M21 21l-4.35-4.35"/>
    </svg>
  ),
  ZoomIn: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <circle cx="11" cy="11" r="8"/>
      <line x1="11" y1="8" x2="11" y2="14"/>
      <line x1="8" y1="11" x2="14" y2="11"/>
      <path d="M21 21l-4.35-4.35"/>
    </svg>
  ),
  ZoomOut: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <circle cx="11" cy="11" r="8"/>
      <line x1="8" y1="11" x2="14" y2="11"/>
      <path d="M21 21l-4.35-4.35"/>
    </svg>
  ),
  Smartphone: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <rect x="5" y="2" width="14" height="20" rx="2" ry="2"/>
      <line x1="12" y1="18" x2="12.01" y2="18"/>
    </svg>
  ),
  Info: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <circle cx="12" cy="12" r="10"/>
      <line x1="12" y1="16" x2="12" y2="12"/>
      <line x1="12" y1="8" x2="12.01" y2="8"/>
    </svg>
  ),
  ChevronDown: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <polyline points="6,9 12,15 18,9"/>
    </svg>
  ),
  Hand: ({ size = 16, className = "" }: { size?: number; className?: string }) => (
    <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className={className}>
      <path d="M18 11V6a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v0"/>
      <path d="M14 10V4a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v2"/>
      <path d="M10 10.5V6a2 2 0 0 0-2-2v0a2 2 0 0 0-2 2v8"/>
      <path d="M18 8a2 2 0 1 1 4 0v6a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15"/>
    </svg>
  )
};

// أنواع العناصر المتقدمة
interface Element {
  id: string;
  type: 'button' | 'input' | 'textarea' | 'select' | 'table' | 'form' | 'div' | 'image' | 'text' | 'content' | 'circle' | 'square' | 'arrow' | 'star' | 'container';
  x: number;
  y: number;
  width: number;
  height: number;
  rotation?: number; // زاوية الدوران بالدرجات

  // النظام الهرمي
  parentId?: string; // معرف العنصر الأب
  children?: string[]; // معرفات العناصر الأطفال
  isVisible?: boolean; // حالة الإظهار/الإخفاء
  isLocked?: boolean; // حالة القفل
  zIndex?: number; // ترتيب الطبقة

  responsivePositions?: {
    [screenSize: string]: {
      x: number;
      y: number;
      customized: boolean;
    };
  };
  responsiveSizes?: {
    [screenSize: string]: {
      width: number;
      height: number;
      customized: boolean;
    };
  };
  properties: {
    text?: string;
    placeholder?: string;
    label?: string;
    color?: string;
    backgroundColor?: string;
    fontSize?: number;
    borderRadius?: number;
    padding?: number;
    margin?: number;
    inputType?: 'text' | 'email' | 'password' | 'number' | 'tel';
    options?: string[]; // للـ select
    columns?: string[]; // للـ table
    rows?: string[][]; // للـ table
    prompt?: string;
    generatedCode?: string;
    required?: boolean;
    validation?: string;
    // خصائص الصورة
    imageUrl?: string;
    imageMode?: 'cover' | 'contain' | 'repeat' | 'stretch';
    // خصائص العناصر التفاعلية
    title?: string;
    description?: string;
    icon?: string;
    link?: string; // رابط العنصر

    // خصائص الحاوي
    layoutType?: 'flex-row' | 'flex-col' | 'grid' | 'absolute'; // نوع التخطيط
    justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
    alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
    gap?: number; // المسافة بين العناصر
    gridColumns?: number; // عدد الأعمدة في الشبكة
    gridRows?: number; // عدد الصفوف في الشبكة
    // محاذاة خاصة بالـ Grid
    justifyItems?: 'start' | 'center' | 'end' | 'stretch';
    alignContent?: 'start' | 'center' | 'end' | 'stretch' | 'space-between' | 'space-around' | 'space-evenly';

    // نظام الحجم الديناميكي المتجاوب - للحاوي فقط
    responsiveSize?: {
      [device: string]: {
        widthMode: 'fixed' | 'percentage';
        heightMode: 'fixed' | 'percentage';
        widthValue: number;
        heightValue: number;
      };
    };

    // تخطيط متجاوب للحاوي
    responsiveLayout?: {
      [device: string]: {
        layoutType: 'flex-row' | 'flex-col' | 'grid' | 'absolute';
        justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around' | 'space-evenly';
        alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
        gap?: number;
        gridColumns?: number;
        gridRows?: number;
        // محاذاة خاصة بالـ Grid
        justifyItems?: 'start' | 'center' | 'end' | 'stretch';
        alignContent?: 'start' | 'center' | 'end' | 'stretch' | 'space-between' | 'space-around' | 'space-evenly';
      };
    };


  };
}

// نوع الصفحة
interface Page {
  id: string;
  name: string;
  elements: Element[];
  pageHeight?: number;             // ارتفاع الصفحة المخصص
  responsivePageHeights?: {        // ارتفاعات متجاوبة لكل جهاز
    [screenSize: string]: {
      height: number;
      customized: boolean;
    };
  };
  properties?: {
    title?: string;
    backgroundColor?: string;        // لون الخلفية العامة (body)
    canvasBackgroundColor?: string;  // لون خلفية إطار العمل
    backgroundImage?: string;        // صورة خلفية إطار العمل
    backgroundSize?: string;
    backgroundRepeat?: string;
    backgroundPosition?: string;
    textColor?: string;
    fontFamily?: string;
    customCSS?: string;
    mobileResponsive?: boolean;      // خيار إعادة ترتيب العناصر على الموبايل
  };
}

// نوع المشروع
interface Project {
  id: string;
  name: string;
  description: string;
  pages: Page[];
  canvasSize: { width: number; height: number };
  settings: {
    theme: 'light' | 'dark';
    primaryColor: string;
    secondaryColor: string;
    fontFamily: string;
  };
}

// مكون العنصر القابل للسحب
const DraggableElement = ({ element, elements, onSelect, isSelected, onUpdatePosition, onUpdateSize, onUpdateRotation, onDoubleClick, onContextMenu, onMoveToParent, canvasSize, currentDevice, currentPage }: {
  element: Element;
  elements: Element[];
  onSelect: (id: string) => void;
  isSelected: boolean;
  onUpdatePosition: (id: string, x: number, y: number) => void;
  onUpdateSize: (id: string, width: number, height: number) => void;
  onUpdateRotation?: (id: string, rotation: number) => void;
  onDoubleClick?: (elementId: string, event: React.MouseEvent) => void;
  onContextMenu?: (id: string, x: number, y: number) => void;
  onMoveToParent?: (elementId: string, newParentId: string | null, newIndex: number) => void;
  canvasSize: { width: number; height: number };
  currentDevice: string;
  currentPage: Page;
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isResizing, setIsResizing] = useState(false);
  const [resizeHandle, setResizeHandle] = useState<string>('');
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [elementStart, setElementStart] = useState({ x: 0, y: 0, width: 0, height: 0 });

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation(); // منع انتشار الحدث إلى Canvas
    setIsDragging(true);
    setDragStart({ x: e.clientX, y: e.clientY });
    setElementStart({ x: element.x, y: element.y, width: element.width, height: element.height });
    onSelect(element.id);
  };

  const handleResizeMouseDown = (e: React.MouseEvent, handle: string) => {
    e.preventDefault();
    e.stopPropagation();
    setIsResizing(true);
    setResizeHandle(handle);
    setDragStart({ x: e.clientX, y: e.clientY });
    setElementStart({ x: element.x, y: element.y, width: element.width, height: element.height });
    onSelect(element.id);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging && !isResizing) {
      // سحب العنصر
      const deltaX = e.clientX - dragStart.x;
      const deltaY = e.clientY - dragStart.y;

      // حرية كاملة في الحركة - بدون قيود
      const newX = elementStart.x + deltaX;
      const newY = elementStart.y + deltaY;

      onUpdatePosition(element.id, newX, newY);
    } else if (isResizing) {
      // تغيير حجم العنصر
      const deltaX = e.clientX - dragStart.x;
      const deltaY = e.clientY - dragStart.y;

      let newWidth = elementStart.width;
      let newHeight = elementStart.height;
      let newX = elementStart.x;
      let newY = elementStart.y;

      switch (resizeHandle) {
        case 'se': // الزاوية اليمنى السفلى
          newWidth = Math.max(50, elementStart.width + deltaX);
          newHeight = Math.max(30, elementStart.height + deltaY);
          break;
        case 'sw': // الزاوية اليسرى السفلى
          newWidth = Math.max(50, elementStart.width - deltaX);
          newHeight = Math.max(30, elementStart.height + deltaY);
          newX = elementStart.x + deltaX;
          break;
        case 'ne': // الزاوية اليمنى العلوية
          newWidth = Math.max(50, elementStart.width + deltaX);
          newHeight = Math.max(30, elementStart.height - deltaY);
          newY = elementStart.y + deltaY;
          break;
        case 'nw': // الزاوية اليسرى العلوية
          newWidth = Math.max(50, elementStart.width - deltaX);
          newHeight = Math.max(30, elementStart.height - deltaY);
          newX = elementStart.x + deltaX;
          newY = elementStart.y + deltaY;
          break;
      }

      onUpdateSize(element.id, newWidth, newHeight);
      if (newX !== elementStart.x || newY !== elementStart.y) {
        onUpdatePosition(element.id, newX, newY);
      }
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    setIsResizing(false);
    setResizeHandle('');
  };

  // إضافة event listeners للماوس
  React.useEffect(() => {
    if (isDragging || isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, isResizing, dragStart, elementStart, resizeHandle]);

  const handleClick = (e: React.MouseEvent) => {
    // منع انتشار الحدث إلى Canvas لتجنب إلغاء التحديد
    e.stopPropagation();

    // إذا كان العنصر له كود مولد، لا نمنع الحدث الافتراضي
    if (!element.properties.generatedCode) {
      e.preventDefault();
    }
    if (!isDragging) {
      onSelect(element.id);
    }
  };

  const handleContextMenu = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // إظهار قائمة السياق
    onContextMenu?.(element.id, e.clientX, e.clientY);
  };

  const handleDoubleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (onDoubleClick) {
      onDoubleClick(element.id, e);
    }
  };

  // دالة للحصول على التخطيط المتجاوب للحاوي (نسخة محلية)
  const getResponsiveLayout = (element: Element, device: string = currentDevice) => {
    if (element.type !== 'container') {
      return {
        layoutType: 'relative' as const,
        justifyContent: 'flex-start' as const,
        alignItems: 'flex-start' as const,
        gap: 0
      };
    }

    // إذا كان هناك تخطيط متجاوب للجهاز الحالي
    if (element.properties.responsiveLayout?.[device]) {
      return element.properties.responsiveLayout[device];
    }

    // العودة للتخطيط الافتراضي
    return {
      layoutType: element.properties.layoutType || 'flex-row' as const,
      justifyContent: element.properties.justifyContent || 'flex-start' as const,
      alignItems: element.properties.alignItems || 'stretch' as const, // توحيد مع المحرر والتصدير
      gap: element.properties.gap || 8
    };
  };

  const renderElement = () => {
    const baseStyle = {
      fontSize: element.properties.fontSize || 14,
      color: element.properties.color || '#000000',
      backgroundColor: element.properties.backgroundColor || 'transparent',
      borderRadius: element.properties.borderRadius || 4,
      padding: element.properties.padding || 8,
      fontFamily: currentPage?.properties?.fontFamily || 'Arial, sans-serif',
    };

    const selectedClass = isSelected ? 'ring-2 ring-blue-500' : '';

    switch (element.type) {
      case 'button':
        return (
          <button
            className={`rounded transition-all hover:opacity-90 ${selectedClass}`}
            style={{
              ...baseStyle,
              backgroundColor: element.properties.backgroundColor || '#3b82f6',
              color: element.properties.color || 'white',
              border: 'none',
              cursor: 'pointer',
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: Math.min(element.properties.fontSize || 14, element.height / 3),
              fontWeight: '600',
              textOverflow: 'ellipsis',
              overflow: 'hidden',
              whiteSpace: 'nowrap',
            }}
            onClick={handleClick}
          >
            {element.properties.text || 'زر جديد'}
          </button>
        );

      case 'input':
        const labelHeight = element.properties.label ? 20 : 0;
        const inputHeight = element.height - labelHeight - 8; // 8px for margins
        return (
          <div
            className={`${selectedClass} rounded`}
            onClick={handleClick}
            style={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}
          >
            {element.properties.label && (
              <label
                className="block font-medium text-right"
                style={{
                  fontSize: Math.min(12, element.height / 6),
                  marginBottom: '4px',
                  color: element.properties.color || '#374151'
                }}
              >
                {element.properties.label}
              </label>
            )}
            <input
              type={element.properties.inputType || 'text'}
              placeholder={element.properties.placeholder || 'أدخل النص...'}
              className="border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              style={{
                ...baseStyle,
                width: '100%',
                height: `${Math.max(inputHeight, 30)}px`,
                fontSize: Math.min(element.properties.fontSize || 14, inputHeight / 2.5),
                padding: `${Math.min(8, inputHeight / 4)}px`,
              }}
              readOnly
            />
          </div>
        );

      case 'textarea':
        const textareaLabelHeight = (element.properties.title || element.properties.label) ? 20 : 0;
        const textareaHeight = element.height - textareaLabelHeight - 8;
        return (
          <div
            className={`${selectedClass} rounded`}
            onClick={handleClick}
            style={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}
          >
            {(element.properties.title || element.properties.label) && (
              <label
                className="block font-medium text-right"
                style={{
                  fontSize: Math.min(12, element.height / 8),
                  marginBottom: '4px',
                  color: element.properties.color || '#374151'
                }}
              >
                {element.properties.title || element.properties.label}
              </label>
            )}
            <textarea
              value={element.properties.text || ''}
              placeholder={element.properties.placeholder || 'أدخل النص الطويل...'}
              className="border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
              style={{
                ...baseStyle,
                width: '100%',
                height: `${Math.max(textareaHeight, 60)}px`,
                fontSize: Math.min(element.properties.fontSize || 14, textareaHeight / 6),
                padding: `${Math.min(8, textareaHeight / 8)}px`,
              }}
              readOnly
            />
          </div>
        );

      case 'select':
        const selectIconSize = Math.min(24, element.height / 6);
        const selectTitleSize = Math.min(14, element.height / 8);
        const selectDescSize = Math.min(12, element.height / 10);

        return (
          <div
            className={`${selectedClass} rounded border-2 border-dashed border-gray-300 bg-gray-50`}
            style={{
              ...baseStyle,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              padding: `${Math.min(8, element.height / 8)}px`,
              cursor: (element.properties.prompt && element.properties.prompt.trim() !== '') ? 'pointer' : 'default'
            }}
            onClick={handleClick}
          >
            <div className="text-center text-blue-600">
              <div style={{ fontSize: `${selectIconSize}px`, marginBottom: `${selectIconSize / 4}px` }}>
                {element.properties.icon || '🔽'}
              </div>
              <div style={{ fontSize: `${selectTitleSize}px`, fontWeight: '500', marginBottom: `${selectTitleSize / 4}px` }}>
                {element.properties.title || 'قائمة اختيار'}
              </div>
              <div style={{ fontSize: `${selectDescSize}px` }}>
                {element.properties.description || 'اختر من الخيارات المتاحة'}
              </div>
              {element.properties.options && element.properties.options.length > 0 && (
                <div style={{ fontSize: `${selectDescSize - 1}px`, marginTop: `${selectDescSize / 2}px`, color: '#6b7280' }}>
                  ({element.properties.options.length} خيارات)
                </div>
              )}
            </div>
          </div>
        );

      case 'table':
        const columns = element.properties.columns || ['العمود 1', 'العمود 2', 'العمود 3'];
        const rows = element.properties.rows || [['بيانات 1', 'بيانات 2', 'بيانات 3']];
        const cellHeight = Math.max(20, (element.height - 40) / (rows.length + 1)); // +1 for header
        const fontSize = Math.min(12, cellHeight / 2);

        return (
          <div
            className={`${selectedClass} rounded overflow-hidden`}
            onClick={handleClick}
            style={{ width: '100%', height: '100%' }}
          >
            <table
              className="border-collapse border border-gray-300"
              style={{ width: '100%', height: '100%', fontSize: `${fontSize}px` }}
            >
              <thead className="bg-gray-50">
                <tr>
                  {columns.map((col, index) => (
                    <th
                      key={index}
                      className="border border-gray-300 text-right font-medium"
                      style={{
                        height: `${cellHeight}px`,
                        padding: `${Math.min(4, cellHeight / 4)}px`,
                        fontSize: `${fontSize}px`
                      }}
                    >
                      {col}
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {rows.map((row, rowIndex) => (
                  <tr key={rowIndex}>
                    {row.map((cell, cellIndex) => (
                      <td
                        key={cellIndex}
                        className="border border-gray-300 text-right"
                        style={{
                          height: `${cellHeight}px`,
                          padding: `${Math.min(4, cellHeight / 4)}px`,
                          fontSize: `${fontSize}px`
                        }}
                      >
                        {cell}
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        );

      case 'form':
        const formIconSize = Math.min(32, element.height / 4);
        const formTitleSize = Math.min(14, element.height / 8);
        const formDescSize = Math.min(12, element.height / 10);

        return (
          <div
            className={`border-2 border-dashed border-gray-300 rounded ${selectedClass}`}
            style={{
              ...baseStyle,
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: `${Math.min(16, element.height / 8)}px`,
            }}
            onClick={handleClick}
          >
            <div className="text-center text-gray-500">
              <div style={{ fontSize: `${formIconSize}px`, marginBottom: `${formIconSize / 4}px` }}>
                {element.properties.icon || '📋'}
              </div>
              <div style={{ fontSize: `${formTitleSize}px`, fontWeight: '500', marginBottom: `${formTitleSize / 4}px` }}>
                {element.properties.title || 'نموذج'}
              </div>
              <div style={{ fontSize: `${formDescSize}px` }}>
                {element.properties.description || 'اسحب العناصر هنا'}
              </div>
            </div>
          </div>
        );

      case 'div':
        const iconSize = Math.min(32, element.height / 4);
        const titleSize = Math.min(14, element.height / 8);
        const textSize = Math.min(12, element.height / 10);

        return (
          <div
            className={`border-2 border-dashed border-gray-300 rounded ${selectedClass}`}
            style={{
              ...baseStyle,
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: `${Math.min(16, element.height / 8)}px`,
            }}
            onClick={handleClick}
          >
            <div className="text-center text-gray-500">
              <div style={{ fontSize: `${iconSize}px`, marginBottom: `${iconSize / 4}px` }}>
                {element.properties.icon || '📦'}
              </div>
              <div style={{ fontSize: `${titleSize}px`, fontWeight: '500', marginBottom: `${titleSize / 4}px` }}>
                {element.properties.title || 'حاوي'}
              </div>
              <div style={{ fontSize: `${textSize}px` }}>
                {element.properties.description || element.properties.text || 'منطقة المحتوى'}
              </div>
            </div>
          </div>
        );

      case 'image':
        const imageIconSize = Math.min(48, element.height / 3);
        const imageLabelSize = Math.min(14, element.height / 8);
        const imageUrl = element.properties.imageUrl;
        const imageMode = element.properties.imageMode || 'cover';

        // تحديد CSS background properties حسب نمط الصورة
        const getImageStyle = () => {
          if (!imageUrl) return {};

          switch (imageMode) {
            case 'cover':
              return {
                backgroundImage: `url(${imageUrl})`,
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat'
              };
            case 'contain':
              return {
                backgroundImage: `url(${imageUrl})`,
                backgroundSize: 'contain',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat'
              };
            case 'repeat':
              return {
                backgroundImage: `url(${imageUrl})`,
                backgroundSize: 'auto',
                backgroundPosition: 'top left',
                backgroundRepeat: 'repeat'
              };
            case 'stretch':
              return {
                backgroundImage: `url(${imageUrl})`,
                backgroundSize: '100% 100%',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat'
              };
            default:
              return {};
          }
        };

        return (
          <div
            className={`border border-gray-300 rounded overflow-hidden ${selectedClass}`}
            onClick={handleClick}
            style={{ width: '100%', height: '100%' }}
          >
            <div
              className="bg-gray-100 flex items-center justify-center"
              style={{
                width: '100%',
                height: '100%',
                ...getImageStyle()
              }}
            >
              {!imageUrl && (
                <div className="text-center text-gray-500">
                  <div style={{ fontSize: `${imageIconSize}px`, marginBottom: `${imageIconSize / 4}px` }}>🖼️</div>
                  <div style={{ fontSize: `${imageLabelSize}px` }}>صورة</div>
                </div>
              )}
            </div>
          </div>
        );

      case 'text':
        return (
          <div
            className={`cursor-pointer ${selectedClass} rounded`}
            style={{
              ...baseStyle,
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: Math.min(element.properties.fontSize || 16, element.height / 2),
              padding: `${Math.min(8, element.height / 4)}px`,
              textAlign: 'center',
              wordWrap: 'break-word',
              overflow: 'hidden',
            }}
            onClick={handleClick}
          >
            {element.properties.text || 'نص تجريبي'}
          </div>
        );

      case 'content':
        const contentIconSize = Math.min(32, element.height / 4);
        const contentTitleSize = Math.min(14, element.height / 8);
        const contentDescSize = Math.min(12, element.height / 10);

        return (
          <div
            className={`border-2 border-dashed border-blue-300 rounded ${selectedClass}`}
            style={{
              ...baseStyle,
              width: '100%',
              height: '100%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              padding: `${Math.min(16, element.height / 8)}px`,
              backgroundColor: element.properties.backgroundColor || '#f0f9ff',
            }}
            onClick={handleClick}
          >
            <div className="text-center text-blue-600">
              <div style={{ fontSize: `${contentIconSize}px`, marginBottom: `${contentIconSize / 4}px` }}>
                {element.properties.icon || '📄'}
              </div>
              <div style={{ fontSize: `${contentTitleSize}px`, fontWeight: '500', marginBottom: `${contentTitleSize / 4}px` }}>
                {element.properties.title || 'منطقة محتوى'}
              </div>
              <div style={{ fontSize: `${contentDescSize}px` }}>
                {element.properties.description || 'منطقة محتوى تفاعلية'}
              </div>
            </div>
          </div>
        );

      case 'circle':
        return (
          <div
            className={`rounded-full ${selectedClass}`}
            style={{
              ...baseStyle,
              width: '100%',
              height: '100%',
              backgroundColor: element.properties.backgroundColor || '#3b82f6',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: '50%',
            }}
            onClick={handleClick}
          >
            {element.properties.text}
          </div>
        );

      case 'square':
        return (
          <div
            className={`${selectedClass}`}
            style={{
              ...baseStyle,
              width: '100%',
              height: '100%',
              backgroundColor: element.properties.backgroundColor || '#ef4444',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: 0,
            }}
            onClick={handleClick}
          >
            {element.properties.text}
          </div>
        );

      case 'arrow':
        return (
          <div
            className={`${selectedClass}`}
            style={{
              ...baseStyle,
              width: '100%',
              height: '100%',
              backgroundColor: element.properties.backgroundColor || '#10b981',
              color: element.properties.color || '#ffffff',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: Math.min(element.properties.fontSize || 24, element.height * 0.8),
              fontWeight: 'bold',
            }}
            onClick={handleClick}
          >
            {element.properties.text || '→'}
          </div>
        );



      case 'star':
        return (
          <div
            className={`${selectedClass}`}
            style={{
              ...baseStyle,
              width: '100%',
              height: '100%',
              backgroundColor: element.properties.backgroundColor || '#8b5cf6',
              color: element.properties.color || '#ffffff',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: Math.min(element.properties.fontSize || 32, element.height * 0.8),
              fontWeight: 'bold',
            }}
            onClick={handleClick}
          >
            {element.properties.text || '★'}
          </div>
        );

      case 'container':
        // الحصول على العناصر الأطفال
        const childElements = elements.filter(el => el.parentId === element.id);

        return (
          <div
            className={`${isSelected ? 'ring-2 ring-blue-500' : childElements.length > 0 ? 'border border-dashed border-gray-200' : 'border-2 border-dashed border-gray-300'} rounded ${childElements.length > 0 ? 'bg-transparent' : 'bg-gray-50/30'} relative`}
            onClick={handleClick}
            style={{
              width: '100%',
              height: '100%',
              display: (() => {
                const layout = getResponsiveLayout(element, currentDevice);
                return layout.layoutType === 'flex-row' ? 'flex' :
                       layout.layoutType === 'flex-col' ? 'flex' :
                       layout.layoutType === 'grid' ? 'grid' : 'flex'; // إصلاح: استخدم 'flex' بدلاً من 'relative'
              })(),
              flexDirection: (() => {
                const layout = getResponsiveLayout(element, currentDevice);
                return layout.layoutType === 'flex-col' ? 'column' : 'row';
              })(),
              justifyContent: getResponsiveLayout(element, currentDevice).justifyContent || 'flex-start',
              alignItems: getResponsiveLayout(element, currentDevice).alignItems || 'stretch', // توحيد مع الحاوي المتداخل
              gap: `${getResponsiveLayout(element, currentDevice).gap || 8}px`,
              gridTemplateColumns: (() => {
                const layout = getResponsiveLayout(element, currentDevice);
                return layout.layoutType === 'grid' ? `repeat(${layout.gridColumns || 2}, 1fr)` : undefined;
              })(),
              gridTemplateRows: (() => {
                const layout = getResponsiveLayout(element, currentDevice);
                return layout.layoutType === 'grid' ? `repeat(${layout.gridRows || 2}, 1fr)` : undefined;
              })(),
              justifyItems: (() => {
                const layout = getResponsiveLayout(element, currentDevice);
                return layout.layoutType === 'grid' ? (layout.justifyItems || 'stretch') : undefined;
              })(),
              alignContent: (() => {
                const layout = getResponsiveLayout(element, currentDevice);
                return layout.layoutType === 'grid' ? (layout.alignContent || 'start') : undefined;
              })(),
              padding: '8px',
              overflow: 'hidden'
            }}
            onDragOver={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            onDrop={(e) => {
              e.preventDefault();
              e.stopPropagation();

              // التحقق من وجود عنصر مسحوب
              const draggedElementId = e.dataTransfer.getData('text/plain');
              if (draggedElementId && draggedElementId !== element.id && onMoveToParent) {
                // جعل العنصر المسحوب طفل لهذا الحاوي
                onMoveToParent(draggedElementId, element.id, 0);
              }
            }}
          >
            {childElements.length === 0 ? (
              <div className="absolute inset-0 flex items-center justify-center text-gray-400 text-sm pointer-events-none">
                <div className="text-center">
                  <Icons.Grid size={24} className="mx-auto mb-2 opacity-50" />
                  <div>اسحب العناصر هنا</div>
                </div>
              </div>
            ) : (
              // رسم العناصر الأطفال بشكل فعلي
              childElements.map((childElement, index) => {
                const renderChildElement = () => {
                  const childBaseStyle = {
                    fontSize: Math.min(childElement.properties.fontSize || 14, 12),
                    color: childElement.properties.color || '#000000',
                    backgroundColor: childElement.properties.backgroundColor || 'transparent',
                    borderRadius: `${childElement.properties.borderRadius || 4}px`,
                    padding: `${Math.min(childElement.properties.padding || 8, 4)}px`,
                  };

                  switch (childElement.type) {
                    case 'button':
                      return (
                        <button
                          className="rounded transition-all cursor-default"
                          style={{
                            ...childBaseStyle,
                            backgroundColor: childElement.properties.backgroundColor || '#3b82f6',
                            color: childElement.properties.color || 'white',
                            border: 'none',
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            fontWeight: '600',
                            textOverflow: 'ellipsis',
                            overflow: 'hidden',
                            whiteSpace: 'nowrap',
                          }}
                        >
                          {childElement.properties.text || 'زر'}
                        </button>
                      );

                    case 'image':
                      const imageUrl = childElement.properties.imageUrl || '';
                      const imageMode = childElement.properties.imageMode || 'cover';

                      // استخدام نفس منطق الصور العادية - background-image
                      const getChildImageStyle = () => {
                        if (!imageUrl) return {};

                        switch (imageMode) {
                          case 'cover':
                            return {
                              backgroundImage: `url(${imageUrl})`,
                              backgroundSize: 'cover',
                              backgroundPosition: 'center',
                              backgroundRepeat: 'no-repeat'
                            };
                          case 'contain':
                            return {
                              backgroundImage: `url(${imageUrl})`,
                              backgroundSize: 'contain',
                              backgroundPosition: 'center',
                              backgroundRepeat: 'no-repeat'
                            };
                          case 'repeat':
                            return {
                              backgroundImage: `url(${imageUrl})`,
                              backgroundSize: 'auto',
                              backgroundPosition: 'top left',
                              backgroundRepeat: 'repeat'
                            };
                          case 'stretch':
                            return {
                              backgroundImage: `url(${imageUrl})`,
                              backgroundSize: '100% 100%',
                              backgroundPosition: 'center',
                              backgroundRepeat: 'no-repeat'
                            };
                          default:
                            return {};
                        }
                      };

                      return (
                        <div
                          className="border border-gray-300 rounded overflow-hidden relative"
                          style={{
                            width: '100%',
                            height: '100%',
                            backgroundColor: '#f8f9fa',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            ...getChildImageStyle() // تطبيق نمط الصورة
                          }}
                        >
                          {!imageUrl && (
                            <div className="text-center text-gray-500 text-xs">
                              <div className="text-lg mb-1">🖼️</div>
                              <div>صورة</div>
                            </div>
                          )}
                        </div>
                      );

                    case 'text':
                      return (
                        <div
                          className="flex items-center justify-center"
                          style={{
                            ...childBaseStyle,
                            width: '100%',
                            height: '100%',
                            textAlign: 'center',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            whiteSpace: 'nowrap'
                          }}
                        >
                          {childElement.properties.text || 'نص'}
                        </div>
                      );

                    case 'container':
                      // رسم الحاوي المتداخل مع محتوياته
                      const nestedChildElements = elements.filter(el => el.parentId === childElement.id);
                      const nestedLayout = getResponsiveLayout(childElement, currentDevice);

                      return (
                        <div
                          key={`nested-${childElement.id}-${currentDevice}-${JSON.stringify(childElement.properties.responsiveSize?.[currentDevice] || {})}`}
                          className={`border border-dashed border-blue-300 rounded bg-blue-50/30 relative`}
                          style={{
                            width: '100%',
                            height: '100%',
                            minHeight: '60px',
                            display: nestedLayout.layoutType === 'flex-row' ? 'flex' :
                                     nestedLayout.layoutType === 'flex-col' ? 'flex' :
                                     nestedLayout.layoutType === 'grid' ? 'grid' : 'flex', // استخدم flex كافتراضي
                            flexDirection: nestedLayout.layoutType === 'flex-col' ? 'column' : 'row',
                            justifyContent: nestedLayout.justifyContent || 'flex-start',
                            alignItems: nestedLayout.alignItems || 'stretch', // غير من flex-start إلى stretch
                            gap: `${nestedLayout.gap || 8}px`,
                            gridTemplateColumns: nestedLayout.layoutType === 'grid' ? `repeat(${nestedLayout.gridColumns || 2}, 1fr)` : undefined,
                            gridTemplateRows: nestedLayout.layoutType === 'grid' ? `repeat(${nestedLayout.gridRows || 2}, 1fr)` : undefined,
                            justifyItems: nestedLayout.layoutType === 'grid' ? (nestedLayout.justifyItems || 'stretch') : undefined,
                            alignContent: nestedLayout.layoutType === 'grid' ? (nestedLayout.alignContent || 'start') : undefined,
                            padding: '4px',
                            overflow: 'visible' // غير من hidden إلى visible
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            onSelect(childElement.id);
                          }}
                        >
                          {nestedChildElements.length > 0 ? (
                            nestedChildElements.map((nestedChild) => (
                              <div
                                key={`${nestedChild.id}-${currentDevice}-${JSON.stringify(nestedChild.properties.responsiveSize?.[currentDevice] || {})}`}
                                className="relative"
                                style={{
                                  // استخدام الحجم الفعلي للعنصر المتداخل مع مراعاة responsiveSize
                                  width: (() => {
                                    // للحاويات مع responsiveSize، استخدم نفس منطق التوليد
                                    if (nestedChild.type === 'container' && nestedChild.properties.responsiveSize?.[currentDevice]) {
                                      const deviceSize = nestedChild.properties.responsiveSize[currentDevice];

                                      if (deviceSize.widthMode === 'percentage') {
                                        // استخدم النسبة المئوية مباشرة - نفس منطق التوليد
                                        return `${deviceSize.widthValue}%`;
                                      } else {
                                        // استخدم القيمة الثابتة
                                        return `${deviceSize.widthValue}px`;
                                      }
                                    }

                                    // للعناصر الأخرى - استخدم flex-grow
                                    return nestedLayout.layoutType === 'absolute' ? `${nestedChild.width}px` :
                                           nestedLayout.layoutType === 'grid' ? '100%' :
                                           nestedLayout.layoutType?.startsWith('flex') ? 'auto' : `${Math.min(nestedChild.width, 80)}px`;
                                  })(),
                                  height: (() => {
                                    // للحاويات مع responsiveSize وheightMode: 'percentage'، استخدم النسبة المئوية مباشرة
                                    if (nestedChild.type === 'container' &&
                                        nestedChild.properties.responsiveSize?.[currentDevice]?.heightMode === 'percentage') {
                                      const heightValue = nestedChild.properties.responsiveSize[currentDevice].heightValue;
                                      return `${heightValue}%`; // استخدم النسبة المئوية مباشرة
                                    }

                                    // للعناصر الأخرى
                                    return nestedLayout.layoutType === 'absolute' ? `${nestedChild.height}px` :
                                           nestedLayout.layoutType === 'grid' ? '100%' :
                                           nestedLayout.layoutType?.startsWith('flex') ? 'auto' : `${Math.min(nestedChild.height, 40)}px`;
                                  })(),
                                  minWidth: currentDevice === 'mobile' ? '30px' : '20px',
                                  minHeight: currentDevice === 'mobile' ? '30px' : '20px',
                                  // منطق flex-grow المحسن للحاويات مع responsiveSize
                                  flexGrow: (() => {
                                    if (!nestedLayout.layoutType?.startsWith('flex')) {
                                      return undefined; // ليس flex layout
                                    }

                                    // للحاويات مع responsiveSize
                                    if (nestedChild.type === 'container' && nestedChild.properties.responsiveSize?.[currentDevice]) {
                                      const deviceSize = nestedChild.properties.responsiveSize[currentDevice];

                                      // إذا كان widthMode: 'percentage'، استخدم النسبة كـ flex-grow
                                      if (deviceSize.widthMode === 'percentage') {
                                        return deviceSize.widthValue / 100; // 50% = 0.5, 100% = 1
                                      } else {
                                        return 0; // حجم ثابت
                                      }
                                    }

                                    // للعناصر الأخرى، استخدم flex-grow عادي
                                    return 1;
                                  })(),
                                  flexShrink: nestedLayout.layoutType?.startsWith('flex') ? 0 : undefined,
                                  position: nestedLayout.layoutType === 'absolute' ? 'absolute' : 'relative',
                                  left: nestedLayout.layoutType === 'absolute' ? `${nestedChild.x}px` : 'auto',
                                  top: nestedLayout.layoutType === 'absolute' ? `${nestedChild.y}px` : 'auto',
                                  zIndex: nestedChild.zIndex || 0,
                                  display: nestedLayout.layoutType === 'grid' ? 'flex' : 'block',
                                  alignItems: nestedLayout.layoutType === 'grid' ? 'center' : 'initial',
                                  justifyContent: nestedLayout.layoutType === 'grid' ? 'center' : 'initial',
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onSelect(nestedChild.id);
                                }}
                              >
                                {(() => {
                                  // استخدام renderChildElement العادي للعناصر في الحاوي المتداخل
                                  const renderNestedElement = () => {
                                    switch (nestedChild.type) {
                                      case 'image':
                                        const imageUrl = nestedChild.properties.imageUrl || '';
                                        const imageMode = nestedChild.properties.imageMode || 'cover';

                                        // استخدام نفس منطق الصور العادية - background-image
                                        const getNestedImageStyle = () => {
                                          if (!imageUrl) return {};

                                          switch (imageMode) {
                                            case 'cover':
                                              return {
                                                backgroundImage: `url(${imageUrl})`,
                                                backgroundSize: 'cover',
                                                backgroundPosition: 'center',
                                                backgroundRepeat: 'no-repeat'
                                              };
                                            case 'contain':
                                              return {
                                                backgroundImage: `url(${imageUrl})`,
                                                backgroundSize: 'contain',
                                                backgroundPosition: 'center',
                                                backgroundRepeat: 'no-repeat'
                                              };
                                            case 'repeat':
                                              return {
                                                backgroundImage: `url(${imageUrl})`,
                                                backgroundSize: 'auto',
                                                backgroundPosition: 'top left',
                                                backgroundRepeat: 'repeat'
                                              };
                                            case 'stretch':
                                              return {
                                                backgroundImage: `url(${imageUrl})`,
                                                backgroundSize: '100% 100%',
                                                backgroundPosition: 'center',
                                                backgroundRepeat: 'no-repeat'
                                              };
                                            default:
                                              return {};
                                          }
                                        };

                                        return (
                                          <div
                                            className="border border-gray-300 rounded overflow-hidden relative"
                                            style={{
                                              width: '100%',
                                              height: '100%',
                                              backgroundColor: '#f8f9fa',
                                              display: 'flex',
                                              alignItems: 'center',
                                              justifyContent: 'center',
                                              minHeight: currentDevice === 'mobile' ? '40px' : '30px',
                                              minWidth: currentDevice === 'mobile' ? '40px' : '30px',
                                              boxSizing: 'border-box',
                                              flexShrink: 0,
                                              ...getNestedImageStyle() // تطبيق نمط الصورة
                                            }}
                                          >
                                            {!imageUrl && (
                                              <div className="text-center text-gray-500 text-xs">
                                                <div className="text-lg mb-1">🖼️</div>
                                                <div>صورة</div>
                                              </div>
                                            )}
                                          </div>
                                        );

                                      case 'button':
                                        return (
                                          <button
                                            className="rounded transition-all cursor-default"
                                            style={{
                                              width: '100%',
                                              height: '100%',
                                              backgroundColor: nestedChild.properties.backgroundColor || '#3b82f6',
                                              color: nestedChild.properties.color || '#ffffff',
                                              fontSize: currentDevice === 'mobile' ? '8px' : '10px',
                                              minHeight: currentDevice === 'mobile' ? '24px' : '20px',
                                              border: 'none',
                                              display: 'flex',
                                              alignItems: 'center',
                                              justifyContent: 'center'
                                            }}
                                          >
                                            {nestedChild.properties.text || 'زر'}
                                          </button>
                                        );

                                      case 'text':
                                        return (
                                          <div
                                            className="flex items-center justify-center"
                                            style={{
                                              width: '100%',
                                              height: '100%',
                                              textAlign: 'center',
                                              overflow: 'hidden',
                                              textOverflow: 'ellipsis',
                                              whiteSpace: 'nowrap',
                                              fontSize: currentDevice === 'mobile' ? '8px' : '10px',
                                              minHeight: currentDevice === 'mobile' ? '16px' : '20px'
                                            }}
                                          >
                                            {nestedChild.properties.text || 'نص'}
                                          </div>
                                        );

                                      default:
                                        return (
                                          <div
                                            className="border border-gray-200 rounded p-1 bg-gray-50 text-center text-xs"
                                            style={{
                                              width: '100%',
                                              height: '100%',
                                              display: 'flex',
                                              alignItems: 'center',
                                              justifyContent: 'center',
                                              fontSize: currentDevice === 'mobile' ? '8px' : '10px',
                                              minHeight: currentDevice === 'mobile' ? '16px' : '20px'
                                            }}
                                          >
                                            {nestedChild.properties.text || nestedChild.type}
                                          </div>
                                        );
                                    }
                                  };

                                  return renderNestedElement();
                                })()}
                              </div>
                            ))
                          ) : (
                            <div className="absolute inset-0 flex items-center justify-center text-blue-400 text-xs">
                              حاوي فارغ
                            </div>
                          )}
                        </div>
                      );

                    default:
                      return (
                        <div
                          className="border border-gray-200 rounded p-1 bg-gray-50 text-center text-xs"
                          style={{
                            width: '100%',
                            height: '100%',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center'
                          }}
                        >
                          {childElement.properties.text || childElement.type}
                        </div>
                      );
                  }
                };

                return (
                  <div
                    key={`${childElement.id}-${currentDevice}-${JSON.stringify(childElement.properties.responsiveSize?.[currentDevice] || {})}`}
                    className="relative"
                    style={(() => {
                      const layout = getResponsiveLayout(element, currentDevice);

                      return {
                        width: (() => {
                          // للحاويات مع responsiveSize، استخدم النسبة المحددة
                          if (childElement.type === 'container' &&
                              childElement.properties.responsiveSize?.[currentDevice]?.widthMode === 'percentage') {
                            const widthValue = childElement.properties.responsiveSize[currentDevice].widthValue;
                            return `${widthValue}%`; // استخدم النسبة المئوية مباشرة
                          }

                          // للعناصر الأخرى، استخدم المنطق العادي
                          return layout.layoutType === 'absolute' ? `${childElement.width}px` :
                                 layout.layoutType === 'grid' ? '100%' :
                                 layout.layoutType?.startsWith('flex') ? 'auto' : `${Math.min(childElement.width, 150)}px`;
                        })(),
                        height: layout.layoutType === 'absolute' ? `${childElement.height}px` :
                                layout.layoutType === 'grid' ? '100%' :
                                layout.layoutType?.startsWith('flex') ? 'auto' : `${Math.min(childElement.height, 80)}px`,
                        minWidth: layout.layoutType === 'grid' ? '60px' : '40px',
                        minHeight: layout.layoutType === 'grid' ? '40px' : '30px',
                        // إضافة flex-grow للتمدد في Flex - مع احترام responsiveSize
                        flexGrow: (() => {
                          if (!layout.layoutType?.startsWith('flex')) {
                            return undefined; // ليس flex layout
                          }

                          // للحاويات مع responsiveSize، استخدم النسبة المحددة
                          if (childElement.type === 'container' && childElement.properties.responsiveSize?.[currentDevice]) {
                            const deviceSize = childElement.properties.responsiveSize[currentDevice];

                            if (deviceSize.widthMode === 'percentage') {
                              return deviceSize.widthValue / 100; // 50% = 0.5, 30% = 0.3
                            } else {
                              return 0; // حجم ثابت، لا تمدد
                            }
                          }

                          // للعناصر الأخرى، استخدم flex-grow عادي
                          return 1;
                        })(),
                        flexShrink: layout.layoutType?.startsWith('flex') ? 0 : undefined,
                        flexBasis: layout.layoutType?.startsWith('flex') ? 'auto' : undefined,
                        position: layout.layoutType === 'absolute' ? 'absolute' : 'relative',
                        left: layout.layoutType === 'absolute' ? `${childElement.x}px` : 'auto',
                        top: layout.layoutType === 'absolute' ? `${childElement.y}px` : 'auto',
                        zIndex: childElement.zIndex || 0,
                        // إضافة تنسيق خاص للـ Grid
                        display: layout.layoutType === 'grid' ? 'flex' : 'block',
                        alignItems: layout.layoutType === 'grid' ? 'center' : 'initial',
                        justifyContent: layout.layoutType === 'grid' ? 'center' : 'initial',
                      };
                    })()}
                    title={`${childElement.type} - ${childElement.properties.text || childElement.id.slice(-4)}`}
                  >
                    {renderChildElement()}
                  </div>
                );
              })
            )}
          </div>
        );

      default:
        return (
          <div className={`p-2 border border-gray-300 rounded ${selectedClass}`} onClick={handleClick}>
            عنصر غير معروف
          </div>
        );
    }
  };

  return (
    <div
      className={`absolute select-none ${
        isDragging ? 'cursor-grabbing z-50' : isResizing ? 'z-50' : 'cursor-grab'
      } ${isSelected ? 'ring-2 ring-blue-500' : ''} ${isResizing ? 'ring-4 ring-green-400' : ''}`}
      draggable={!isResizing}
      onDragStart={(e) => {
        if (!isResizing) {
          e.dataTransfer.setData('text/plain', element.id);
        }
      }}
      style={{
        left: (() => {
          // أولاً: تحقق من وجود موضع مخصص للشاشة الحالية
          if (element.responsivePositions && element.responsivePositions[currentDevice]) {
            return element.responsivePositions[currentDevice].x;
          }
          // ثانياً: استخدم الموضع الأساسي
          return element.x;
        })(),
        top: (() => {
          // أولاً: تحقق من وجود موضع مخصص للشاشة الحالية
          if (element.responsivePositions && element.responsivePositions[currentDevice]) {
            return element.responsivePositions[currentDevice].y;
          }
          // ثانياً: استخدم الموضع الأساسي
          return element.y;
        })(),
        width: (() => {
          // أولاً: تحقق من وجود حجم مخصص للشاشة الحالية
          if (element.responsiveSizes && element.responsiveSizes[currentDevice]) {
            return element.responsiveSizes[currentDevice].width;
          }
          // ثانياً: استخدم الحجم الأساسي
          return element.width;
        })(),
        height: (() => {
          // أولاً: تحقق من وجود حجم مخصص للشاشة الحالية
          if (element.responsiveSizes && element.responsiveSizes[currentDevice]) {
            return element.responsiveSizes[currentDevice].height;
          }
          // ثانياً: استخدم الحجم الأساسي
          return element.height;
        })(),
        transform: `rotate(${element.rotation || 0}deg)`,
        transition: isDragging || isResizing ? 'none' : 'transform 0.2s ease',
        opacity: isResizing ? 0.8 : 1,
        transformOrigin: 'center center',
        zIndex: element.zIndex || 0,
      }}
      onMouseDown={handleMouseDown}
      onClick={handleClick}
      onDoubleClick={handleDoubleClick}
      onContextMenu={handleContextMenu}
      title={
        isResizing ? 'جاري تغيير الحجم...' :
        element.properties.generatedCode ? 'هذا العنصر له وظيفة مفعلة ✅' :
        'اسحب لتحريك العنصر أو استخدم النقاط الزرقاء لتغيير الحجم'
      }
    >
      {renderElement()}
      {isSelected && (
        <>
          {/* نقاط التحكم في الزوايا */}
          <div
            className="absolute -top-1 -left-1 w-3 h-3 bg-blue-500 rounded-full cursor-nw-resize hover:bg-blue-600 transition-colors"
            onMouseDown={(e) => handleResizeMouseDown(e, 'nw')}
            title="تغيير الحجم من الزاوية اليسرى العلوية"
          ></div>
          <div
            className="absolute -top-1 -right-1 w-3 h-3 bg-blue-500 rounded-full cursor-ne-resize hover:bg-blue-600 transition-colors"
            onMouseDown={(e) => handleResizeMouseDown(e, 'ne')}
            title="تغيير الحجم من الزاوية اليمنى العلوية"
          ></div>
          <div
            className="absolute -bottom-1 -left-1 w-3 h-3 bg-blue-500 rounded-full cursor-sw-resize hover:bg-blue-600 transition-colors"
            onMouseDown={(e) => handleResizeMouseDown(e, 'sw')}
            title="تغيير الحجم من الزاوية اليسرى السفلى"
          ></div>
          <div
            className="absolute -bottom-1 -right-1 w-3 h-3 bg-blue-500 rounded-full cursor-se-resize hover:bg-blue-600 transition-colors"
            onMouseDown={(e) => handleResizeMouseDown(e, 'se')}
            title="تغيير الحجم من الزاوية اليمنى السفلى"
          ></div>

          {/* معلومات الموقع والحجم والدوران */}
          <div className="absolute -top-8 left-0 bg-blue-500/70 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
            <div className="flex items-center gap-1">
              <Icons.MapPin size={10} />
              <span>{Math.round(element.x)}, {Math.round(element.y)}</span>
              <span>|</span>
              <Icons.Resize size={10} />
              <span>{element.width}×{element.height}</span>
              <span>|</span>
              <Icons.RotateRight size={10} />
              <span>{element.rotation || 0}°</span>
            </div>
          </div>

          {/* مؤشر حالة التغيير */}
          {isResizing && (
            <div className="absolute -top-12 left-0 bg-green-500/70 text-white text-xs px-2 py-1 rounded whitespace-nowrap">
              <div className="flex items-center gap-1">
                <Icons.Resize size={10} />
                <span>تغيير الحجم...</span>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
};

// مكون قائمة الطبقات الهرمية
const LayersPanel = ({
  elements,
  selectedElementId,
  onSelectElement,
  onToggleVisibility,
  onToggleLock,
  onMoveElement,
  onDeleteElement,
  onMoveLayer,
  onReorderElements,
  currentPageId,
  isCollapsed,
  setIsCollapsed
}: {
  elements: Element[];
  selectedElementId: string | null;
  onSelectElement: (id: string) => void;
  onToggleVisibility: (id: string) => void;
  onToggleLock: (id: string) => void;
  onMoveElement: (elementId: string, newParentId: string | null, newIndex: number) => void;
  onDeleteElement: (id: string) => void;
  onMoveLayer: (elementId: string, direction: 'up' | 'down') => void;
  onReorderElements: (newElements: Element[]) => void;
  currentPageId: string;
  isCollapsed: boolean;
  setIsCollapsed: (collapsed: boolean) => void;
}) => {

  const [expandedElements, setExpandedElements] = useState<Set<string>>(new Set());
  const [draggedElement, setDraggedElement] = useState<string | null>(null);
  const [dragOverElement, setDragOverElement] = useState<string | null>(null);
  const [dragPosition, setDragPosition] = useState<'above' | 'below' | 'inside' | null>(null);

  // دوال السحب والإفلات
  const handleDragStart = (e: React.DragEvent, elementId: string) => {
    setDraggedElement(elementId);
    e.dataTransfer.effectAllowed = 'move';
    e.dataTransfer.setData('text/plain', elementId);
  };

  const handleDragOver = (e: React.DragEvent, targetElementId: string) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';

    const targetEl = elements.find(el => el.id === targetElementId);
    if (!targetEl) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const y = e.clientY - rect.top;
    const height = rect.height;

    if (targetEl.type === 'container') {
      // للحاويات: إظهار مؤشر النقل للداخل
      setDragOverElement(targetElementId);
      setDragPosition('inside'); // مؤشر خاص للحاويات
    } else {
      // للعناصر العادية: إظهار مؤشر الترتيب
      const position = y < height / 2 ? 'above' : 'below';
      setDragOverElement(targetElementId);
      setDragPosition(position);
    }
  };

  const handleDragLeave = () => {
    setDragOverElement(null);
    setDragPosition(null);
  };

  const handleDrop = (e: React.DragEvent, targetElementId: string) => {
    e.preventDefault();

    if (!draggedElement || draggedElement === targetElementId) {
      setDraggedElement(null);
      setDragOverElement(null);
      setDragPosition(null);
      return;
    }

    const draggedEl = elements.find(el => el.id === draggedElement);
    const targetEl = elements.find(el => el.id === targetElementId);

    if (!draggedEl || !targetEl) return;

    // تحديد نوع العملية حسب dragPosition
    if (dragPosition === 'inside' && targetEl.type === 'container') {
      // نقل العنصر إلى داخل الحاوي (parent/child)
      onMoveElement(draggedElement, targetElementId, 0);
      console.log(`نقل العنصر ${draggedElement} إلى داخل الحاوي ${targetElementId}`);
    } else if (dragPosition === 'above' || dragPosition === 'below') {
      // إعادة ترتيب العناصر بطريقة بسيطة ومباشرة
      const rootElements = elements.filter(el => !el.parentId);
      const childElements = elements.filter(el => el.parentId);

      // ترتيب العناصر الجذر حسب z-index (من الأعلى للأسفل)
      const sortedRootElements = [...rootElements].sort((a, b) => (b.zIndex || 0) - (a.zIndex || 0));

      const draggedIndex = sortedRootElements.findIndex(el => el.id === draggedElement);
      const targetIndex = sortedRootElements.findIndex(el => el.id === targetElementId);

      if (draggedIndex === -1 || targetIndex === -1) return;

      // إزالة العنصر المسحوب
      const [draggedItem] = sortedRootElements.splice(draggedIndex, 1);

      // إدراج العنصر في الموضع الجديد
      if (dragPosition === 'above') {
        // فوق العنصر المستهدف (z-index أعلى)
        sortedRootElements.splice(targetIndex, 0, draggedItem);
      } else {
        // تحت العنصر المستهدف (z-index أقل)
        sortedRootElements.splice(targetIndex + 1, 0, draggedItem);
      }

      // إعادة تعيين z-index حسب الترتيب الجديد
      // العنصر الأول في القائمة = أعلى z-index = يظهر في المقدمة
      const updatedRootElements = sortedRootElements.map((el, index) => ({
        ...el,
        zIndex: sortedRootElements.length - index // العنصر الأول يحصل على أعلى z-index
      }));

      // دمج العناصر الجذر المُحدثة مع العناصر الأطفال
      const allUpdatedElements = [...updatedRootElements, ...childElements];

      // تحديث العناصر
      onReorderElements(allUpdatedElements);

      console.log(`إعادة ترتيب العنصر ${draggedElement} ${dragPosition === 'above' ? 'فوق' : 'تحت'} ${targetElementId}`);
    }

    setDraggedElement(null);
    setDragOverElement(null);
    setDragPosition(null);
  };

  const handleDragEnd = () => {
    setDraggedElement(null);
    setDragOverElement(null);
    setDragPosition(null);
  };

  // الحصول على أيقونة العنصر
  const getElementIcon = (element: Element) => {
    const iconProps = { size: 16, className: "text-gray-600" };

    switch (element.type) {
      case 'container': return <Icons.Grid {...iconProps} />;
      case 'button': return <Icons.Plus {...iconProps} />;
      case 'input': return <Icons.Type {...iconProps} />;
      case 'textarea': return <Icons.FileText {...iconProps} />;
      case 'select': return <Icons.ChevronDown {...iconProps} />;
      case 'table': return <Icons.Grid {...iconProps} />;
      case 'form': return <Icons.FileText {...iconProps} />;
      case 'image': return <Icons.Image {...iconProps} />;
      case 'text': return <Icons.Type {...iconProps} />;
      case 'div': return <Icons.Grid {...iconProps} />;
      case 'circle': return <div className="w-4 h-4 rounded-full border-2 border-gray-600"></div>;
      case 'square': return <div className="w-4 h-4 border-2 border-gray-600"></div>;
      case 'arrow': return <div className="w-4 h-4 text-gray-600">→</div>;
      case 'star': return <div className="w-4 h-4 text-gray-600">★</div>;
      default: return <Icons.Grid {...iconProps} />;
    }
  };

  // بناء الشجرة الهرمية
  const buildElementTree = (elements: Element[]): Element[] => {
    const rootElements = elements.filter(el => !el.parentId);
    // ترتيب من الأعلى للأسفل (z-index عالي في الأعلى)
    return rootElements.sort((a, b) => (b.zIndex || 0) - (a.zIndex || 0));
  };

  // تبديل حالة التوسيع
  const toggleExpanded = (elementId: string) => {
    setExpandedElements(prev => {
      const newSet = new Set(prev);
      if (newSet.has(elementId)) {
        newSet.delete(elementId);
      } else {
        newSet.add(elementId);
      }
      return newSet;
    });
  };

  // رسم عنصر في الشجرة
  const renderTreeElement = (element: Element, level: number = 0): React.ReactNode => {
    const childElements = elements.filter(el => el.parentId === element.id);
    const hasChildren = childElements.length > 0;
    const isExpanded = expandedElements.has(element.id);
    const isSelected = selectedElementId === element.id;
    const isDragging = draggedElement === element.id;

    return (
      <div key={element.id} className={`select-none ${isDragging ? 'opacity-50 scale-95' : ''} transition-all duration-200`}>
        {/* العنصر الرئيسي */}
        <div
          className={`flex items-center gap-1 px-2 py-1 text-xs hover:bg-gray-100 cursor-pointer transition-colors ${
            isSelected ? 'bg-blue-100 border-l-2 border-blue-500' : ''
          } ${dragOverElement === element.id ?
            dragPosition === 'above' ? 'border-t-2 border-blue-400' :
            dragPosition === 'below' ? 'border-b-2 border-blue-400' :
            dragPosition === 'inside' ? 'bg-green-100 border-2 border-green-400' : ''
            : ''}`}
          style={{ paddingLeft: `${8 + level * 16}px` }}
          onClick={() => onSelectElement(element.id)}
          onDragOver={(e) => handleDragOver(e, element.id)}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, element.id)}
        >
          {/* أيقونة التوسيع/الطي */}
          {hasChildren ? (
            <button
              onClick={(e) => {
                e.stopPropagation();
                toggleExpanded(element.id);
              }}
              className="w-4 h-4 flex items-center justify-center hover:bg-gray-200 rounded"
            >
              <Icons.ChevronDown
                size={12}
                className={`text-gray-500 transition-transform ${isExpanded ? '' : '-rotate-90'}`}
              />
            </button>
          ) : (
            <div className="w-4" />
          )}

          {/* أيقونة نوع العنصر */}
          <div className={`${element.type === 'container' ? 'text-purple-600' : ''}`}>
            {getElementIcon(element)}
          </div>

          {/* اسم العنصر */}
          <span className={`flex-1 truncate ${isSelected ? 'font-medium text-blue-700' : 'text-gray-700'}`}>
            {element.properties.title || element.properties.text || `${element.type} ${element.id.slice(-4)}`}
          </span>

          {/* أزرار التحكم */}
          <div className="flex items-center gap-1">
            {/* زر الإظهار/الإخفاء */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                onToggleVisibility(element.id);
              }}
              className="w-4 h-4 flex items-center justify-center hover:bg-gray-200 rounded"
              title={element.isVisible === false ? "إظهار" : "إخفاء"}
            >
              <Icons.Eye
                size={12}
                className={element.isVisible === false ? "text-gray-400" : "text-gray-600"}
              />
            </button>

            {/* زر القفل */}
            <button
              onClick={(e) => {
                e.stopPropagation();
                onToggleLock(element.id);
              }}
              className="w-4 h-4 flex items-center justify-center hover:bg-gray-200 rounded"
              title={element.isLocked ? "إلغاء القفل" : "قفل"}
            >
              <Icons.Settings
                size={12}
                className={element.isLocked ? "text-red-500" : "text-gray-400"}
              />
            </button>

            {/* أيقونة السحب */}
            <div
              draggable
              onDragStart={(e) => handleDragStart(e, element.id)}
              onDragEnd={handleDragEnd}
              className="w-4 h-4 flex items-center justify-center cursor-grab active:cursor-grabbing hover:bg-gray-200 rounded text-gray-400 hover:text-gray-600"
              title="اسحب لإعادة ترتيب الطبقة"
            >
              <div className="flex flex-col gap-0.5">
                <div className="w-3 h-0.5 bg-current rounded"></div>
                <div className="w-3 h-0.5 bg-current rounded"></div>
                <div className="w-3 h-0.5 bg-current rounded"></div>
              </div>
            </div>
          </div>
        </div>

        {/* العناصر الأطفال */}
        {hasChildren && isExpanded && (
          <div>
            {childElements
              .sort((a, b) => (a.zIndex || 0) - (b.zIndex || 0))
              .map(childElement => renderTreeElement(childElement, level + 1))}
          </div>
        )}
      </div>
    );
  };

  const rootElements = buildElementTree(elements);

  return (
    <div className={`${isCollapsed ? 'w-12' : 'w-64'} bg-white border-r border-gray-200 flex flex-col h-full transition-all duration-300 ease-in-out`}>
      {/* رأس القائمة */}
      <div className="p-2 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50 flex-shrink-0">
        <div className="flex items-center justify-between">
          {/* زر الطي/الفتح */}
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="w-6 h-6 flex items-center justify-center hover:bg-white/50 rounded transition-colors"
            title={isCollapsed ? "فتح قائمة الطبقات" : "طي قائمة الطبقات"}
          >
            <Icons.ChevronDown
              size={14}
              className={`text-blue-600 transition-transform duration-300 ${
                isCollapsed ? 'rotate-90' : 'rotate-0'
              }`}
            />
          </button>

          {/* العنوان - يختفي عند الطي */}
          {!isCollapsed && (
            <h3 className="text-sm font-bold text-right text-gray-800 flex items-center gap-2">
              <Icons.Grid size={16} className="text-blue-600" />
              طبقات الصفحة
            </h3>
          )}
        </div>
      </div>

      {/* قائمة العناصر - تختفي عند الطي */}
      {!isCollapsed && (
        <div className="flex-1 overflow-y-auto">
          {rootElements.length === 0 ? (
            <div className="p-4 text-center text-gray-500 text-xs">
              <Icons.Grid size={24} className="mx-auto mb-2 text-gray-300" />
              لا توجد عناصر في هذه الصفحة
            </div>
          ) : (
            <div className="py-1">
              {rootElements.map(element => renderTreeElement(element))}
            </div>
          )}
        </div>
      )}

      {/* عرض مبسط عند الطي */}
      {isCollapsed && (
        <div className="flex-1 flex flex-col items-center justify-start pt-4 gap-2">
          <Icons.Grid size={20} className="text-blue-600" />
          <div
            className="text-xs text-gray-500 text-center"
            style={{
              writingMode: 'vertical-rl',
              textOrientation: 'mixed'
            }}
          >
            طبقات
          </div>
          {rootElements.length > 0 && (
            <div className="text-xs text-blue-600 font-bold">
              {rootElements.length}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// مكون لوحة العناصر المتقدمة
const ElementPanel = ({ onAddElement, isFloating = false, isCollapsed, setIsCollapsed }: {
  onAddElement: (type: Element['type']) => void;
  isFloating?: boolean;
  isCollapsed?: boolean;
  setIsCollapsed?: (collapsed: boolean) => void;
}) => {
  // حالة الطي والفتح المحلية للعناصر العائمة
  const [localIsCollapsed, setLocalIsCollapsed] = useState(false);

  // استخدام الحالة المحلية أو الخارجية حسب النوع
  const collapsed = isFloating ? localIsCollapsed : (isCollapsed ?? false);
  const setCollapsed = isFloating ? setLocalIsCollapsed : (setIsCollapsed ?? (() => {}));

  const elementCategories = [
    {
      name: 'أساسيات',
      icon: <Icons.Grid size={16} className="text-blue-600" />,
      elements: [
        { type: 'button' as const, name: 'زر', icon: <Icons.Button size={16} className="text-white" />, color: 'bg-blue-500' },
        { type: 'text' as const, name: 'نص', icon: <Icons.Text size={16} className="text-white" />, color: 'bg-green-500' },
        { type: 'container' as const, name: 'حاوي', icon: <Icons.Grid size={16} className="text-white" />, color: 'bg-purple-500' },
        { type: 'content' as const, name: 'منطقة محتوى', icon: <Icons.Text size={16} className="text-white" />, color: 'bg-cyan-500' },
        { type: 'image' as const, name: 'صورة', icon: <Icons.Image size={16} className="text-white" />, color: 'bg-pink-500' },
      ]
    },
    {
      name: 'نماذج',
      icon: <Icons.Input size={16} className="text-orange-600" />,
      elements: [
        { type: 'input' as const, name: 'حقل إدخال', icon: <Icons.Input size={16} className="text-white" />, color: 'bg-orange-500' },
        { type: 'textarea' as const, name: 'نص طويل', icon: <Icons.Text size={16} className="text-white" />, color: 'bg-yellow-500' },
        { type: 'select' as const, name: 'قائمة اختيار', icon: <Icons.Button size={16} className="text-white" />, color: 'bg-indigo-500' },
        { type: 'form' as const, name: 'نموذج', icon: <Icons.Grid size={16} className="text-white" />, color: 'bg-teal-500' },
      ]
    },
    {
      name: 'بيانات',
      icon: <Icons.Grid size={16} className="text-gray-600" />,
      elements: [
        { type: 'table' as const, name: 'جدول', icon: <Icons.Grid size={16} className="text-white" />, color: 'bg-gray-600' },
      ]
    },
    {
      name: 'أشكال',
      icon: <Icons.Plus size={16} className="text-red-600" />,
      elements: [
        { type: 'circle' as const, name: 'دائرة', icon: <div className="w-4 h-4 rounded-full bg-white border-2 border-white"></div>, color: 'bg-red-500' },
        { type: 'square' as const, name: 'مربع', icon: <div className="w-4 h-4 bg-white border-2 border-white"></div>, color: 'bg-blue-500' },
        { type: 'arrow' as const, name: 'سهم', icon: <div className="text-white text-xs">→</div>, color: 'bg-green-500' },
        { type: 'star' as const, name: 'نجمة', icon: <div className="text-white text-xs">★</div>, color: 'bg-purple-500' },
      ]
    }
  ];

  return (
    <div className={`${isFloating ? 'w-full bg-transparent' : collapsed ? 'w-12' : 'w-72'} bg-slate-50 border-r border-gray-300 overflow-y-auto flex flex-col transition-all duration-300 ease-in-out`} style={!isFloating ? { height: 'calc(100vh + 20px)', borderBottom: 'none' } : {}}>
      {!isFloating && (
        <div className="p-2 border-b border-gray-200 bg-gradient-to-r from-blue-50 to-purple-50 flex-shrink-0">
          <div className="flex items-center justify-between">
            {/* زر الطي/الفتح */}
            <button
              onClick={() => setCollapsed(!collapsed)}
              className="w-6 h-6 flex items-center justify-center hover:bg-white/50 rounded transition-colors"
              title={collapsed ? "فتح مكتبة العناصر" : "طي مكتبة العناصر"}
            >
              <Icons.ChevronDown
                size={14}
                className={`text-blue-600 transition-transform duration-300 ${
                  collapsed ? '-rotate-90' : 'rotate-0'
                }`}
              />
            </button>

            {/* العنوان - يختفي عند الطي */}
            {!collapsed && (
              <h3 className="text-sm font-bold text-right text-gray-800 flex items-center gap-2">
                <Icons.Grid size={16} className="text-blue-600" />
                مكتبة العناصر
              </h3>
            )}
          </div>
        </div>
      )}

      {/* محتوى العناصر - يختفي عند الطي */}
      {!collapsed && !isFloating && (
        <div className="overflow-y-auto flex-1 p-2 space-y-4">
          {elementCategories.map((category, categoryIndex) => (
            <div key={categoryIndex} className="space-y-2">
              <h4 className="text-sm font-semibold text-gray-800 text-right flex items-center justify-end gap-2">
                <span>{category.name}</span>
                {category.icon}
              </h4>
              <div className="grid grid-cols-2 gap-2">
                {category.elements.map((element, elementIndex) => (
                  <button
                    key={elementIndex}
                    onClick={() => onAddElement(element.type)}
                    className={`${element.color} text-white p-3 rounded-lg hover:opacity-90 transition-all transform hover:scale-105`}
                    title={`إضافة ${element.name}`}
                  >
                    <div className="mb-1 flex justify-center">{element.icon}</div>
                    <div className="text-xs font-medium">{element.name}</div>
                  </button>
                ))}
              </div>
            </div>
          ))}
          {/* مساحة إضافية في الأسفل لضمان ظهور الأزرار */}
          <div style={{ height: '100px' }}></div>
        </div>
      )}

      {/* محتوى العناصر العادي للـ floating mode */}
      {isFloating && (
        <div className="p-2 space-y-4">
          {elementCategories.map((category, categoryIndex) => (
            <div key={categoryIndex} className="space-y-2">
              <h4 className="text-sm font-semibold text-gray-800 text-right flex items-center justify-end gap-2">
                <span>{category.name}</span>
                {category.icon}
              </h4>
              <div className="grid grid-cols-2 gap-2">
                {category.elements.map((element, elementIndex) => (
                  <button
                    key={elementIndex}
                    onClick={() => onAddElement(element.type)}
                    className={`${element.color} text-white p-3 rounded-lg hover:opacity-90 transition-all transform hover:scale-105`}
                    title={`إضافة ${element.name}`}
                  >
                    <div className="mb-1 flex justify-center">{element.icon}</div>
                    <div className="text-xs font-medium">{element.name}</div>
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* عرض مبسط عند الطي */}
      {collapsed && !isFloating && (
        <div className="flex-1 flex flex-col items-center justify-start pt-4 gap-3">
          <Icons.Grid size={20} className="text-blue-600" />
          <div
            className="text-xs text-gray-500 text-center"
            style={{
              writingMode: 'vertical-rl',
              textOrientation: 'mixed'
            }}
          >
            عناصر
          </div>
          <div className="text-xs text-blue-600 font-bold">
            {elementCategories.reduce((total, category) => total + category.elements.length, 0)}
          </div>
        </div>
      )}
    </div>
  );
};

// مكون نافذة الخصائص
const PropertiesPanel = ({
  selectedElement,
  onUpdateElement,
  onDeleteElement,
  onUpdateRotation,
  isFloating = false,
  isGeneratingElement,
  onGenerateElement,
  onPreviewStoredCode,
  isPageSelected = false,
  currentPage = null,
  onUpdatePageProperties,
  currentDevice = 'mobile',
  onUpdateElementSize,
  canvasSize = { width: 800, height: 600 }, // قيم افتراضية
  pageHeight = 600 // ارتفاع الصفحة الحالي
}: {
  selectedElement: Element | null;
  onUpdateElement: (id: string, properties: Partial<Element['properties']>) => void;
  onDeleteElement: (id: string) => void;
  onUpdateRotation?: (id: string, rotation: number) => void;
  isFloating?: boolean;
  isGeneratingElement: boolean;
  onGenerateElement: () => void;
  onPreviewStoredCode?: (elementId: string) => void;
  isPageSelected?: boolean;
  currentPage?: Page | null;
  onUpdatePageProperties?: (pageId: string, properties: Partial<Page['properties']>) => void;
  currentDevice?: string;
  onUpdateElementSize?: (id: string, width: number, height: number) => void;
  canvasSize?: { width: number; height: number }; // حجم الكانفاس
  pageHeight?: number; // ارتفاع الصفحة الحالي
}) => {
  // حالة الطي والفتح
  const [isCollapsed, setIsCollapsed] = useState(false);

  // حالة العصا السحرية داخل المكون
  const [showMagicCommands, setShowMagicCommands] = useState(false);

  // قوائم الأوامر الاحترافية لكل نوع عنصر
  const magicCommands = {
    text: [
      'أضف تأثير كتابة تدريجية (typewriter effect)',
      'اجعل النص يظهر عند التمرير (scroll animation)',
      'أضف تأثير تدرج لوني متحرك للنص',
      'اجعل النص قابل للنسخ بنقرة واحدة',
      'أضف عداد تنازلي أو تصاعدي للأرقام',
      'اجعل النص يتغير كل ثانية (rotating text)',
      'أضف تأثير highlight عند التمرير',
      'اجعل النص responsive للشاشات المختلفة'
    ],
    image: [
      'أضف lazy loading للصورة لتحسين الأداء',
      'اجعل الصورة تكبر عند النقر (lightbox)',
      'أضف تأثير parallax scrolling للصورة',
      'اجعل الصورة تتغير عند hover (image swap)',
      'أضف watermark أو علامة مائية للصورة',
      'اجعل الصورة تدور 360 درجة عند التفاعل',
      'أضف تأثير zoom in/out عند التمرير',
      'اجعل الصورة تحمل من placeholder إلى الصورة الفعلية'
    ],
    button: [
      'أضف تأثير ripple عند النقر (material design)',
      'اجعل الزر يهتز عند النقر الخاطئ',
      'أضف loading spinner عند النقر',
      'اجعل الزر يتحول إلى checkmark عند النجاح',
      'أضف تأثير pulse للفت الانتباه',
      'اجعل الزر يعرض tooltip عند التمرير',
      'أضف تأثير magnetic (يجذب المؤشر)',
      'اجعل الزر يغير شكله حسب الحالة (success/error)'
    ],
    input: [
      'أضف validation فوري أثناء الكتابة',
      'اجعل الحقل يعرض اقتراحات تلقائية',
      'أضف تأثير floating label',
      'اجعل الحقل يحفظ القيمة في localStorage',
      'أضف character counter للحقل',
      'اجعل الحقل يدعم drag & drop للملفات',
      'أضف تأثير shake عند الخطأ',
      'اجعل الحقل يدعم البحث المباشر'
    ],
    container: [
      'أضف تأثير sticky header عند التمرير',
      'اجعل الحاوي يدعم infinite scroll',
      'أضف تأثير fade in عند دخول الشاشة',
      'اجعل الحاوي قابل للسحب والإفلات',
      'أضف تأثير masonry layout للعناصر',
      'اجعل الحاوي يدعم swipe للموبايل',
      'أضف تأثير accordion للمحتوى',
      'اجعل الحاوي يدعم virtual scrolling للأداء'
    ]
  };

  // دالة للحصول على الأوامر المناسبة للعنصر المحدد
  const getMagicCommandsForElement = (elementType: string) => {
    switch (elementType) {
      case 'text':
        return magicCommands.text;
      case 'image':
        return magicCommands.image;
      case 'button':
        return magicCommands.button;
      case 'input':
        return magicCommands.input;
      case 'container':
        return magicCommands.container;
      default:
        return magicCommands.text; // افتراضي
    }
  };

  // دالة لإضافة أمر سحري إلى البرومبت
  const addMagicCommand = (command: string) => {
    if (selectedElement) {
      const currentPrompt = selectedElement.properties.prompt || '';
      const newPrompt = currentPrompt.trim() === '' ? command : currentPrompt + ', ' + command;
      onUpdateElement(selectedElement.id, { prompt: newPrompt });
    }
  };



  // إغلاق نافذة الأوامر السحرية عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showMagicCommands) {
        const target = event.target as HTMLElement;
        if (!target.closest('.magic-commands-container')) {
          setShowMagicCommands(false);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showMagicCommands]);
  // عرض خصائص الصفحة إذا كانت محددة
  if (isPageSelected && currentPage && onUpdatePageProperties) {
    return (
      <div className={`${isFloating ? 'w-full bg-transparent' : 'w-64 bg-slate-50 border-l border-gray-300 overflow-hidden flex flex-col'}`} style={!isFloating ? { height: 'calc(100vh + 20px)', borderBottom: 'none' } : {}}>
        {!isFloating && (
          <div className="p-2 bg-slate-50 border-b border-gray-200 flex-shrink-0">
            <h3 className="text-sm font-bold text-left text-gray-800 flex items-center justify-start gap-2">
              <Icons.Settings size={16} className="text-blue-600" />
              خصائص الصفحة
            </h3>
          </div>
        )}

        <div className="flex-1 overflow-y-auto p-2 space-y-3">
          {/* عنوان الصفحة */}
          <div>
            <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
              <Icons.Tag size={14} className="text-gray-600" />
              عنوان الصفحة
            </label>
            <input
              type="text"
              value={currentPage.properties?.title || ''}
              onChange={(e) => onUpdatePageProperties(currentPage.id, { title: e.target.value })}
              className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
              placeholder="أدخل عنوان الصفحة"
            />
          </div>

          {/* لون الخلفية العامة */}
          <div>
            <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
              <Icons.Palette size={14} className="text-gray-600" />
              لون الخلفية العامة
            </label>
            <div className="flex gap-1">
              <input
                type="color"
                value={currentPage.properties?.backgroundColor || '#f5f5f5'}
                onChange={(e) => onUpdatePageProperties(currentPage.id, { backgroundColor: e.target.value })}
                className="w-10 h-8 border border-gray-300 rounded cursor-pointer flex-shrink-0"
              />
              <input
                type="text"
                value={currentPage.properties?.backgroundColor || '#f5f5f5'}
                onChange={(e) => onUpdatePageProperties(currentPage.id, { backgroundColor: e.target.value })}
                className="flex-1 min-w-0 p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                placeholder="#f5f5f5"
              />
            </div>
            <div className="mt-1 text-xs text-gray-500 text-right">
              💡 لون الخلفية خلف إطار العمل
            </div>
          </div>

          {/* لون خلفية إطار العمل */}
          <div>
            <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
              <Icons.Palette size={14} className="text-gray-600" />
              لون خلفية إطار العمل
            </label>
            <div className="flex gap-1">
              <input
                type="color"
                value={currentPage.properties?.canvasBackgroundColor || '#ffffff'}
                onChange={(e) => onUpdatePageProperties(currentPage.id, { canvasBackgroundColor: e.target.value })}
                className="w-10 h-8 border border-gray-300 rounded cursor-pointer flex-shrink-0"
              />
              <input
                type="text"
                value={currentPage.properties?.canvasBackgroundColor || '#ffffff'}
                onChange={(e) => onUpdatePageProperties(currentPage.id, { canvasBackgroundColor: e.target.value })}
                className="flex-1 min-w-0 p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                placeholder="#ffffff"
              />
            </div>
            <div className="mt-1 text-xs text-gray-500 text-right">
              💡 لون خلفية منطقة العمل نفسها
            </div>
          </div>

          {/* صورة الخلفية */}
          <div>
            <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
              <Icons.Image size={14} className="text-gray-600" />
              صورة الخلفية
            </label>
            <input
              type="url"
              value={currentPage.properties?.backgroundImage || ''}
              onChange={(e) => onUpdatePageProperties(currentPage.id, { backgroundImage: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none text-xs"
              placeholder="https://example.com/background.jpg"
            />

            {/* خيارات الخلفية */}
            {currentPage.properties?.backgroundImage && (
              <div className="mt-2 space-y-2">
                <div>
                  <label className="block text-xs font-medium mb-1 text-right text-gray-600 flex items-center justify-end gap-1">
                    <Icons.Resize size={12} className="text-gray-500" />
                    حجم الخلفية
                  </label>
                  <select
                    value={currentPage.properties?.backgroundSize || 'cover'}
                    onChange={(e) => onUpdatePageProperties(currentPage.id, { backgroundSize: e.target.value })}
                    className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                  >
                    <option value="cover">تغطية كاملة (Cover)</option>
                    <option value="contain">احتواء (Contain)</option>
                    <option value="auto">حجم طبيعي (Auto)</option>
                    <option value="100% 100%">تمدد كامل (Stretch)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-xs font-medium mb-1 text-right text-gray-600 flex items-center justify-end gap-1">
                    <Icons.Repeat size={12} className="text-gray-500" />
                    تكرار الخلفية
                  </label>
                  <select
                    value={currentPage.properties?.backgroundRepeat || 'no-repeat'}
                    onChange={(e) => onUpdatePageProperties(currentPage.id, { backgroundRepeat: e.target.value })}
                    className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                  >
                    <option value="no-repeat">بدون تكرار</option>
                    <option value="repeat">تكرار في جميع الاتجاهات</option>
                    <option value="repeat-x">تكرار أفقي فقط</option>
                    <option value="repeat-y">تكرار عمودي فقط</option>
                    <option value="space">تكرار مع مسافات</option>
                    <option value="round">تكرار مع تقريب</option>
                  </select>
                </div>

                <div>
                  <label className="block text-xs font-medium mb-1 text-right text-gray-600 flex items-center justify-end gap-1">
                    <Icons.MapPin size={12} className="text-gray-500" />
                    موضع الخلفية
                  </label>
                  <select
                    value={currentPage.properties?.backgroundPosition || 'center'}
                    onChange={(e) => onUpdatePageProperties(currentPage.id, { backgroundPosition: e.target.value })}
                    className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                  >
                    <option value="center">وسط</option>
                    <option value="top">أعلى</option>
                    <option value="bottom">أسفل</option>
                    <option value="left">يسار</option>
                    <option value="right">يمين</option>
                    <option value="top left">أعلى يسار</option>
                    <option value="top right">أعلى يمين</option>
                    <option value="bottom left">أسفل يسار</option>
                    <option value="bottom right">أسفل يمين</option>
                  </select>
                </div>
              </div>
            )}
          </div>

          {/* لون النص الافتراضي */}
          <div>
            <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
              <Icons.Type size={14} className="text-gray-600" />
              لون النص الافتراضي
            </label>
            <div className="flex gap-1">
              <input
                type="color"
                value={currentPage.properties?.textColor || '#000000'}
                onChange={(e) => onUpdatePageProperties(currentPage.id, { textColor: e.target.value })}
                className="w-10 h-8 border border-gray-300 rounded cursor-pointer flex-shrink-0"
              />
              <input
                type="text"
                value={currentPage.properties?.textColor || '#000000'}
                onChange={(e) => onUpdatePageProperties(currentPage.id, { textColor: e.target.value })}
                className="flex-1 min-w-0 p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                placeholder="#000000"
              />
            </div>
          </div>

          {/* خط الصفحة */}
          <div>
            <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
              <Icons.Type size={14} className="text-gray-600" />
              خط الصفحة
            </label>
            <select
              value={currentPage.properties?.fontFamily || 'Arial, sans-serif'}
              onChange={(e) => onUpdatePageProperties(currentPage.id, { fontFamily: e.target.value })}
              className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
            >
              <option value="Arial, sans-serif">Arial</option>
              <option value="'Times New Roman', serif">Times New Roman</option>
              <option value="'Courier New', monospace">Courier New</option>
              <option value="'Helvetica Neue', sans-serif">Helvetica</option>
              <option value="'Georgia', serif">Georgia</option>
              <option value="'Verdana', sans-serif">Verdana</option>
              <option value="'Tahoma', sans-serif">Tahoma</option>
              <option value="'Cairo', sans-serif">Cairo (عربي)</option>
              <option value="'Amiri', serif">Amiri (عربي)</option>
            </select>
          </div>

          {/* CSS مخصص */}
          <div>
            <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
              <Icons.Code size={14} className="text-gray-600" />
              CSS مخصص
            </label>
            <textarea
              value={currentPage.properties?.customCSS || ''}
              onChange={(e) => onUpdatePageProperties(currentPage.id, { customCSS: e.target.value })}
              className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none resize-none"
              rows={4}
              placeholder="/* أضف CSS مخصص هنا */"
            />
          </div>

          {/* خيار التصميم المتجاوب */}
          <div>
            <label className="block text-xs font-medium mb-2 text-right text-gray-700 flex items-center justify-end gap-1">
              <Icons.Smartphone size={14} className="text-gray-600" />
              التصميم المتجاوب
            </label>
            <div className="flex items-center justify-between bg-gray-50 p-2 rounded border">
              <input
                type="checkbox"
                id="mobileResponsive"
                checked={currentPage.properties?.mobileResponsive || false}
                onChange={(e) => onUpdatePageProperties(currentPage.id, { mobileResponsive: e.target.checked })}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
              />
              <label htmlFor="mobileResponsive" className="text-xs text-gray-700 text-right flex-1 mr-2">
                إعادة ترتيب العناصر عند خروجها من الشاشة
              </label>
            </div>
            <p className="text-xs text-gray-500 text-right mt-1">
              عند التفعيل: العناصر ستترتب عمودياً إذا كانت الشاشة أصغر من عرض المشروع
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (!selectedElement) {
    return (
      <div className={`${isFloating ? 'w-full bg-transparent' : isCollapsed ? 'w-12' : 'w-64'} bg-slate-50 border-l border-gray-300 overflow-y-auto flex flex-col transition-all duration-300 ease-in-out`} style={!isFloating ? { height: 'calc(100vh + 20px)', borderBottom: 'none' } : {}}>
        {!isFloating && (
          <div className="p-2 bg-slate-50 border-b border-gray-200 flex-shrink-0">
            <div className="flex items-center justify-between">
              {/* العنوان - يختفي عند الطي */}
              {!isCollapsed && (
                <h3 className="text-sm font-bold text-left text-gray-800 flex items-center gap-2">
                  <Icons.Settings size={16} className="text-blue-600" />
                  الخصائص
                </h3>
              )}

              {/* زر الطي/الفتح */}
              <button
                onClick={() => setIsCollapsed(!isCollapsed)}
                className="w-6 h-6 flex items-center justify-center hover:bg-white/50 rounded transition-colors"
                title={isCollapsed ? "فتح قائمة الخصائص" : "طي قائمة الخصائص"}
              >
                <Icons.ChevronDown
                  size={14}
                  className={`text-blue-600 transition-transform duration-300 ${
                    isCollapsed ? 'rotate-90' : 'rotate-0'
                  }`}
                />
              </button>
            </div>
          </div>
        )}

        {/* المحتوى - يختفي عند الطي */}
        {!isCollapsed && (
          <div className="p-2">
            <p className="text-xs text-gray-700 text-right">اختر عنصراً أو انقر على مكان فارغ لتحرير خصائص الصفحة</p>
          </div>
        )}

        {/* عرض مبسط عند الطي */}
        {isCollapsed && (
          <div className="flex-1 flex flex-col items-center justify-start pt-4 gap-3">
            <Icons.Settings size={20} className="text-blue-600" />
            <div
              className="text-xs text-gray-500 text-center"
              style={{
                writingMode: 'vertical-rl',
                textOrientation: 'mixed'
              }}
            >
              خصائص
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`${isFloating ? 'w-full bg-transparent' : 'w-64 bg-slate-50 border-l border-gray-300 overflow-hidden flex flex-col'}`} style={!isFloating ? { height: '100vh', paddingBottom: '80px', borderBottom: 'none', boxSizing: 'border-box' } : {}}>
      {!isFloating && (
        <div className="p-2 bg-slate-50 border-b border-gray-200 flex-shrink-0">
          <h3 className="text-sm font-bold text-left text-gray-800 flex items-center justify-start gap-2">
            <Icons.Settings size={16} className="text-blue-600" />
            الخصائص
          </h3>
        </div>
      )}
      <div className={`${isFloating ? '' : 'p-2 overflow-y-auto flex-1'} space-y-3`}>
        {/* خصائص خاصة بالحاوي */}
        {selectedElement.type === 'container' && (
          <div className="space-y-3 mb-4 p-3 bg-gray-50 rounded border border-gray-200">
            <h4 className="text-sm font-medium text-gray-700 text-right flex items-center gap-2">
              <Icons.Grid size={16} />
              خصائص الحاوي
            </h4>

            {/* مؤشر الجهاز الحالي للتخطيط */}
            <div className="bg-blue-50 p-2 rounded text-xs text-blue-800 text-right mb-3">
              <div className="flex items-center justify-end gap-1">
                <Icons.Smartphone size={12} />
                <span>التخطيط للجهاز: {currentDevice === 'mobile' ? 'موبايل' : currentDevice === 'tablet' ? 'تابلت' : 'ديسكتوب'}</span>
              </div>
            </div>

            {/* نوع التخطيط المتجاوب */}
            <div>
              <label className="block text-xs font-medium mb-1 text-right text-gray-700">
                نوع التخطيط
              </label>
              <select
                value={(() => {
                  const responsiveLayout = selectedElement.properties.responsiveLayout?.[currentDevice];
                  return responsiveLayout?.layoutType || selectedElement.properties.layoutType || 'flex-row';
                })()}
                onChange={(e) => {
                  const newLayoutType = e.target.value as 'flex-row' | 'flex-col' | 'grid' | 'absolute';

                  onUpdateElement(selectedElement.id, {
                    responsiveLayout: {
                      ...selectedElement.properties.responsiveLayout,
                      [currentDevice]: {
                        ...selectedElement.properties.responsiveLayout?.[currentDevice],
                        layoutType: newLayoutType,
                        justifyContent: selectedElement.properties.responsiveLayout?.[currentDevice]?.justifyContent || selectedElement.properties.justifyContent || 'flex-start',
                        alignItems: selectedElement.properties.responsiveLayout?.[currentDevice]?.alignItems || selectedElement.properties.alignItems || 'flex-start',
                        gap: selectedElement.properties.responsiveLayout?.[currentDevice]?.gap || selectedElement.properties.gap || 8
                      }
                    }
                  });
                }}
                className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-700"
              >
                <option value="flex-row">أفقي (صف)</option>
                <option value="flex-col">عمودي (عمود)</option>
                <option value="grid">شبكة</option>
                <option value="absolute">موضع مطلق</option>
              </select>
            </div>

            {/* المحاذاة الأفقية المتجاوبة */}
            {(() => {
              const currentLayout = selectedElement.properties.responsiveLayout?.[currentDevice]?.layoutType || selectedElement.properties.layoutType || 'flex-row';
              return (currentLayout === 'flex-row' || currentLayout === 'flex-col');
            })() && (
              <div>
                <label className="block text-xs font-medium mb-1 text-right text-gray-700">
                  المحاذاة الأفقية
                </label>
                <select
                  value={(() => {
                    const responsiveLayout = selectedElement.properties.responsiveLayout?.[currentDevice];
                    return responsiveLayout?.justifyContent || selectedElement.properties.justifyContent || 'flex-start';
                  })()}
                  onChange={(e) => {
                    onUpdateElement(selectedElement.id, {
                      responsiveLayout: {
                        ...selectedElement.properties.responsiveLayout,
                        [currentDevice]: {
                          ...selectedElement.properties.responsiveLayout?.[currentDevice],
                          layoutType: selectedElement.properties.responsiveLayout?.[currentDevice]?.layoutType || selectedElement.properties.layoutType || 'flex-row',
                          justifyContent: e.target.value as any,
                          alignItems: selectedElement.properties.responsiveLayout?.[currentDevice]?.alignItems || selectedElement.properties.alignItems || 'flex-start',
                          gap: selectedElement.properties.responsiveLayout?.[currentDevice]?.gap || selectedElement.properties.gap || 8
                        }
                      }
                    });
                  }}
                  className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-700"
                >
                  <option value="flex-start">البداية</option>
                  <option value="center">الوسط</option>
                  <option value="flex-end">النهاية</option>
                  <option value="space-between">توزيع متباعد</option>
                  <option value="space-around">توزيع حول</option>
                  <option value="space-evenly">توزيع متساوي</option>
                </select>
              </div>
            )}

            {/* المحاذاة العمودية المتجاوبة */}
            {(() => {
              const currentLayout = selectedElement.properties.responsiveLayout?.[currentDevice]?.layoutType || selectedElement.properties.layoutType || 'flex-row';
              return (currentLayout === 'flex-row' || currentLayout === 'flex-col');
            })() && (
              <div>
                <label className="block text-xs font-medium mb-1 text-right text-gray-700">
                  المحاذاة العمودية
                </label>
                <select
                  value={(() => {
                    const responsiveLayout = selectedElement.properties.responsiveLayout?.[currentDevice];
                    return responsiveLayout?.alignItems || selectedElement.properties.alignItems || 'flex-start';
                  })()}
                  onChange={(e) => {
                    onUpdateElement(selectedElement.id, {
                      responsiveLayout: {
                        ...selectedElement.properties.responsiveLayout,
                        [currentDevice]: {
                          ...selectedElement.properties.responsiveLayout?.[currentDevice],
                          layoutType: selectedElement.properties.responsiveLayout?.[currentDevice]?.layoutType || selectedElement.properties.layoutType || 'flex-row',
                          justifyContent: selectedElement.properties.responsiveLayout?.[currentDevice]?.justifyContent || selectedElement.properties.justifyContent || 'flex-start',
                          alignItems: e.target.value as any,
                          gap: selectedElement.properties.responsiveLayout?.[currentDevice]?.gap || selectedElement.properties.gap || 8
                        }
                      }
                    });
                  }}
                  className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-700"
                >
                  <option value="flex-start">البداية</option>
                  <option value="center">الوسط</option>
                  <option value="flex-end">النهاية</option>
                  <option value="stretch">تمدد</option>
                </select>
              </div>
            )}

            {/* المسافة بين العناصر المتجاوبة */}
            <div>
              <label className="block text-xs font-medium mb-1 text-right text-gray-700">
                المسافة بين العناصر (px)
              </label>
              <input
                type="number"
                value={(() => {
                  const responsiveLayout = selectedElement.properties.responsiveLayout?.[currentDevice];
                  return responsiveLayout?.gap ?? selectedElement.properties.gap ?? 8;
                })()}
                onChange={(e) => {
                  const newGap = parseInt(e.target.value) || 0;
                  onUpdateElement(selectedElement.id, {
                    responsiveLayout: {
                      ...selectedElement.properties.responsiveLayout,
                      [currentDevice]: {
                        ...selectedElement.properties.responsiveLayout?.[currentDevice],
                        layoutType: selectedElement.properties.responsiveLayout?.[currentDevice]?.layoutType || selectedElement.properties.layoutType || 'flex-row',
                        justifyContent: selectedElement.properties.responsiveLayout?.[currentDevice]?.justifyContent || selectedElement.properties.justifyContent || 'flex-start',
                        alignItems: selectedElement.properties.responsiveLayout?.[currentDevice]?.alignItems || selectedElement.properties.alignItems || 'flex-start',
                        gap: newGap
                      }
                    }
                  });
                }}
                className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-700"
                min="0"
                max="50"
              />
            </div>

            {/* خصائص الشبكة المتجاوبة */}
            {(() => {
              const currentLayout = selectedElement.properties.responsiveLayout?.[currentDevice]?.layoutType || selectedElement.properties.layoutType || 'flex-row';
              return currentLayout === 'grid';
            })() && (
              <>
                <div>
                  <label className="block text-xs font-medium mb-1 text-right text-gray-700">
                    عدد الأعمدة
                  </label>
                  <input
                    type="number"
                    value={(() => {
                      const responsiveLayout = selectedElement.properties.responsiveLayout?.[currentDevice];
                      return responsiveLayout?.gridColumns ?? selectedElement.properties.gridColumns ?? 2;
                    })()}
                    onChange={(e) => {
                      const newColumns = parseInt(e.target.value) || 1;
                      onUpdateElement(selectedElement.id, {
                        responsiveLayout: {
                          ...selectedElement.properties.responsiveLayout,
                          [currentDevice]: {
                            ...selectedElement.properties.responsiveLayout?.[currentDevice],
                            layoutType: selectedElement.properties.responsiveLayout?.[currentDevice]?.layoutType || selectedElement.properties.layoutType || 'grid',
                            justifyContent: selectedElement.properties.responsiveLayout?.[currentDevice]?.justifyContent || selectedElement.properties.justifyContent || 'flex-start',
                            alignItems: selectedElement.properties.responsiveLayout?.[currentDevice]?.alignItems || selectedElement.properties.alignItems || 'flex-start',
                            gap: selectedElement.properties.responsiveLayout?.[currentDevice]?.gap || selectedElement.properties.gap || 8,
                            gridColumns: newColumns,
                            gridRows: selectedElement.properties.responsiveLayout?.[currentDevice]?.gridRows || selectedElement.properties.gridRows || 2
                          }
                        }
                      });
                    }}
                    className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-700"
                    min="1"
                    max="6"
                  />
                </div>
                <div>
                  <label className="block text-xs font-medium mb-1 text-right text-gray-700">
                    عدد الصفوف
                  </label>
                  <input
                    type="number"
                    value={(() => {
                      const responsiveLayout = selectedElement.properties.responsiveLayout?.[currentDevice];
                      return responsiveLayout?.gridRows ?? selectedElement.properties.gridRows ?? 2;
                    })()}
                    onChange={(e) => {
                      const newRows = parseInt(e.target.value) || 1;
                      onUpdateElement(selectedElement.id, {
                        responsiveLayout: {
                          ...selectedElement.properties.responsiveLayout,
                          [currentDevice]: {
                            ...selectedElement.properties.responsiveLayout?.[currentDevice],
                            layoutType: selectedElement.properties.responsiveLayout?.[currentDevice]?.layoutType || selectedElement.properties.layoutType || 'grid',
                            justifyContent: selectedElement.properties.responsiveLayout?.[currentDevice]?.justifyContent || selectedElement.properties.justifyContent || 'flex-start',
                            alignItems: selectedElement.properties.responsiveLayout?.[currentDevice]?.alignItems || selectedElement.properties.alignItems || 'flex-start',
                            gap: selectedElement.properties.responsiveLayout?.[currentDevice]?.gap || selectedElement.properties.gap || 8,
                            gridColumns: selectedElement.properties.responsiveLayout?.[currentDevice]?.gridColumns || selectedElement.properties.gridColumns || 2,
                            gridRows: newRows
                          }
                        }
                      });
                    }}
                    className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-700"
                    min="1"
                    max="6"
                  />
                </div>

                {/* محاذاة العناصر داخل الخلايا */}
                <div>
                  <label className="block text-xs font-medium mb-1 text-right text-gray-700">
                    محاذاة العناصر (أفقي)
                  </label>
                  <select
                    value={(() => {
                      const responsiveLayout = selectedElement.properties.responsiveLayout?.[currentDevice];
                      return responsiveLayout?.justifyItems ?? selectedElement.properties.justifyItems ?? 'stretch';
                    })()}
                    onChange={(e) => {
                      const newJustifyItems = e.target.value as 'start' | 'center' | 'end' | 'stretch';
                      onUpdateElement(selectedElement.id, {
                        responsiveLayout: {
                          ...selectedElement.properties.responsiveLayout,
                          [currentDevice]: {
                            ...selectedElement.properties.responsiveLayout?.[currentDevice],
                            layoutType: selectedElement.properties.responsiveLayout?.[currentDevice]?.layoutType || selectedElement.properties.layoutType || 'grid',
                            justifyContent: selectedElement.properties.responsiveLayout?.[currentDevice]?.justifyContent || selectedElement.properties.justifyContent || 'flex-start',
                            alignItems: selectedElement.properties.responsiveLayout?.[currentDevice]?.alignItems || selectedElement.properties.alignItems || 'flex-start',
                            gap: selectedElement.properties.responsiveLayout?.[currentDevice]?.gap || selectedElement.properties.gap || 8,
                            gridColumns: selectedElement.properties.responsiveLayout?.[currentDevice]?.gridColumns || selectedElement.properties.gridColumns || 2,
                            gridRows: selectedElement.properties.responsiveLayout?.[currentDevice]?.gridRows || selectedElement.properties.gridRows || 2,
                            justifyItems: newJustifyItems,
                            alignContent: selectedElement.properties.responsiveLayout?.[currentDevice]?.alignContent || selectedElement.properties.alignContent || 'start'
                          }
                        }
                      });
                    }}
                    className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-700"
                  >
                    <option value="start">البداية</option>
                    <option value="center">الوسط</option>
                    <option value="end">النهاية</option>
                    <option value="stretch">تمدد</option>
                  </select>
                </div>

                {/* محاذاة الشبكة ككل */}
                <div>
                  <label className="block text-xs font-medium mb-1 text-right text-gray-700">
                    محاذاة الشبكة (عمودي)
                  </label>
                  <select
                    value={(() => {
                      const responsiveLayout = selectedElement.properties.responsiveLayout?.[currentDevice];
                      return responsiveLayout?.alignContent ?? selectedElement.properties.alignContent ?? 'start';
                    })()}
                    onChange={(e) => {
                      const newAlignContent = e.target.value as 'start' | 'center' | 'end' | 'stretch' | 'space-between' | 'space-around' | 'space-evenly';
                      onUpdateElement(selectedElement.id, {
                        responsiveLayout: {
                          ...selectedElement.properties.responsiveLayout,
                          [currentDevice]: {
                            ...selectedElement.properties.responsiveLayout?.[currentDevice],
                            layoutType: selectedElement.properties.responsiveLayout?.[currentDevice]?.layoutType || selectedElement.properties.layoutType || 'grid',
                            justifyContent: selectedElement.properties.responsiveLayout?.[currentDevice]?.justifyContent || selectedElement.properties.justifyContent || 'flex-start',
                            alignItems: selectedElement.properties.responsiveLayout?.[currentDevice]?.alignItems || selectedElement.properties.alignItems || 'flex-start',
                            gap: selectedElement.properties.responsiveLayout?.[currentDevice]?.gap || selectedElement.properties.gap || 8,
                            gridColumns: selectedElement.properties.responsiveLayout?.[currentDevice]?.gridColumns || selectedElement.properties.gridColumns || 2,
                            gridRows: selectedElement.properties.responsiveLayout?.[currentDevice]?.gridRows || selectedElement.properties.gridRows || 2,
                            justifyItems: selectedElement.properties.responsiveLayout?.[currentDevice]?.justifyItems || selectedElement.properties.justifyItems || 'stretch',
                            alignContent: newAlignContent
                          }
                        }
                      });
                    }}
                    className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-700"
                  >
                    <option value="start">البداية</option>
                    <option value="center">الوسط</option>
                    <option value="end">النهاية</option>
                    <option value="stretch">تمدد</option>
                    <option value="space-between">مسافة بين</option>
                    <option value="space-around">مسافة حول</option>
                    <option value="space-evenly">مسافة متساوية</option>
                  </select>
                </div>
              </>
            )}


          </div>
        )}

        {/* النص - فقط للعناصر غير الحاوي */}
        {selectedElement.type !== 'container' && (
          <div>
            <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
              <Icons.Text size={14} className="text-gray-600" />
              النص
            </label>
            <input
              type="text"
              value={selectedElement.properties.text || ''}
              onChange={(e) => onUpdateElement(selectedElement.id, { text: e.target.value })}
              className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
              placeholder="أدخل النص"
            />
          </div>
        )}

        {/* الرابط - لجميع العناصر */}
        <div>
          <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
            🔗
            الرابط
          </label>
          <input
            type="url"
            value={selectedElement.properties.link || ''}
            onChange={(e) => onUpdateElement(selectedElement.id, { link: e.target.value })}
            className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
            placeholder="https://example.com"
          />
        </div>

        {/* خصائص العناصر التفاعلية (form, div, content, textarea) */}
        {(selectedElement.type === 'form' || selectedElement.type === 'div' || selectedElement.type === 'content' || selectedElement.type === 'textarea') && (
          <>
            <div>
              <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
                <Icons.Tag size={14} className="text-gray-600" />
                العنوان
              </label>
              <input
                type="text"
                value={selectedElement.properties.title || ''}
                onChange={(e) => onUpdateElement(selectedElement.id, { title: e.target.value })}
                className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                placeholder="أدخل العنوان"
              />
            </div>

            <div>
              <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
                <Icons.FileText size={14} className="text-gray-600" />
                الوصف
              </label>
              <textarea
                value={selectedElement.properties.description || ''}
                onChange={(e) => onUpdateElement(selectedElement.id, { description: e.target.value })}
                className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none resize-none"
                placeholder="أدخل الوصف"
                rows={2}
              />
            </div>

            <div>
              <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
                <Icons.Image size={14} className="text-gray-600" />
                الأيقونة
              </label>
              <input
                type="text"
                value={selectedElement.properties.icon || ''}
                onChange={(e) => onUpdateElement(selectedElement.id, { icon: e.target.value })}
                className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                placeholder="أدخل الأيقونة (مثل: 📋, 📦, ⭐)"
              />
            </div>
          </>
        )}

        {/* خصائص العناصر التي تحتاج label (input, textarea, select) */}
        {(selectedElement.type === 'input' || selectedElement.type === 'textarea' || selectedElement.type === 'select') && (
          <div>
            <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
              <Icons.Tag size={14} className="text-gray-600" />
              التسمية (Label)
            </label>
            <input
              type="text"
              value={selectedElement.properties.label || ''}
              onChange={(e) => onUpdateElement(selectedElement.id, { label: e.target.value })}
              className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
              placeholder="أدخل التسمية"
            />
          </div>
        )}

        {/* خصائص خاصة بقائمة الاختيار */}
        {selectedElement.type === 'select' && (
          <>
            <div>
              <label className="block text-xs font-medium mb-1 text-right text-gray-700">📋 الخيارات</label>
              <textarea
                value={(selectedElement.properties.options || []).join('\n')}
                onChange={(e) => {
                  // تحديث الخيارات مباشرة من النص
                  const allLines = e.target.value.split('\n');
                  // حفظ جميع الأسطر (حتى الفارغة) أثناء الكتابة
                  onUpdateElement(selectedElement.id, {
                    options: allLines
                  });
                }}
                onBlur={(e) => {
                  // عند فقدان التركيز، إزالة الأسطر الفارغة
                  const cleanOptions = e.target.value.split('\n').filter(opt => opt.trim() !== '');
                  if (cleanOptions.length === 0) {
                    // إذا لم تكن هناك خيارات، إضافة خيارات افتراضية
                    onUpdateElement(selectedElement.id, {
                      options: ['الخيار الأول', 'الخيار الثاني', 'الخيار الثالث']
                    });
                  } else {
                    onUpdateElement(selectedElement.id, {
                      options: cleanOptions
                    });
                  }
                }}
                onKeyDown={(e) => {
                  // السماح بـ Enter لإنشاء سطر جديد
                  if (e.key === 'Enter') {
                    e.stopPropagation();
                  }
                  // Ctrl+Enter لإضافة خيار جديد سريع
                  if (e.key === 'Enter' && e.ctrlKey) {
                    e.preventDefault();
                    const currentValue = e.currentTarget.value;
                    const newValue = currentValue + (currentValue.endsWith('\n') ? '' : '\n') + 'خيار جديد';
                    const allLines = newValue.split('\n');
                    onUpdateElement(selectedElement.id, { options: allLines });
                    // تحديث قيمة textarea
                    setTimeout(() => {
                      e.currentTarget.value = newValue;
                      e.currentTarget.focus();
                      e.currentTarget.setSelectionRange(newValue.length, newValue.length);
                    }, 0);
                  }
                }}
                className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none resize-none"
                placeholder="الخيار الأول&#10;الخيار الثاني&#10;الخيار الثالث"
                rows={5}
                style={{ lineHeight: '1.5' }}
              />
              <p className="text-xs text-gray-500 text-right mt-1">
                اضغط Enter لسطر جديد • Ctrl+Enter لإضافة "خيار جديد" سريع
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1 text-right text-gray-700">🏷️ العنوان</label>
              <input
                type="text"
                value={selectedElement.properties.title || ''}
                onChange={(e) => onUpdateElement(selectedElement.id, { title: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                placeholder="أدخل العنوان"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1 text-right text-gray-700">📄 الوصف</label>
              <input
                type="text"
                value={selectedElement.properties.description || ''}
                onChange={(e) => onUpdateElement(selectedElement.id, { description: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                placeholder="أدخل الوصف"
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1 text-right text-gray-700">🎭 الأيقونة</label>
              <input
                type="text"
                value={selectedElement.properties.icon || ''}
                onChange={(e) => onUpdateElement(selectedElement.id, { icon: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                placeholder="🔽"
              />
            </div>
          </>
        )}
        
        {/* الألوان والخط - فقط للعناصر غير الحاوي */}
        {selectedElement.type !== 'container' && (
          <>
            <div>
              <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
                <Icons.Palette size={14} className="text-gray-600" />
                لون الخلفية
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="color"
                  value={selectedElement.properties.backgroundColor || (selectedElement.type === 'button' ? '#3b82f6' : '#ffffff')}
                  onChange={(e) => onUpdateElement(selectedElement.id, { backgroundColor: e.target.value })}
                  className="w-10 h-8 border border-gray-300 rounded cursor-pointer flex-shrink-0"
                />
                <span className="text-xs text-gray-800 bg-gray-200 px-2 py-1 rounded font-mono">
                  {selectedElement.properties.backgroundColor || (selectedElement.type === 'button' ? '#3b82f6' : '#ffffff')}
                </span>
              </div>
            </div>

            <div>
              <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
                <Icons.Type size={14} className="text-gray-600" />
                لون النص
              </label>
              <div className="flex items-center gap-2">
                <input
                  type="color"
                  value={selectedElement.properties.color || (selectedElement.type === 'button' ? '#ffffff' : '#000000')}
                  onChange={(e) => onUpdateElement(selectedElement.id, { color: e.target.value })}
                  className="w-10 h-8 border border-gray-300 rounded cursor-pointer flex-shrink-0"
                />
                <span className="text-xs text-gray-800 bg-gray-200 px-2 py-1 rounded font-mono">
                  {selectedElement.properties.color || (selectedElement.type === 'button' ? '#ffffff' : '#000000')}
                </span>
              </div>
            </div>

            <div>
              <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
                <Icons.Type size={14} className="text-gray-600" />
                حجم الخط
              </label>
              <input
                type="number"
                value={selectedElement.properties.fontSize || 14}
                onChange={(e) => onUpdateElement(selectedElement.id, { fontSize: parseInt(e.target.value) })}
                className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                min="8"
                max="72"
              />
            </div>
          </>
        )}

        {/* خيارات الدوران */}
        <div>
          <label className="block text-xs font-medium mb-1 text-right text-gray-700">🔄 الدوران</label>
          <div className="space-y-2">
            {/* مؤشر الدوران الحالي */}
            <div className="bg-gray-100 p-1 rounded text-center">
              <span className="text-xs font-mono text-gray-700">{selectedElement.rotation || 0}°</span>
            </div>

            {/* أزرار الدوران السريع */}
            <div className="grid grid-cols-3 gap-1">
              <button
                onClick={() => onUpdateRotation?.(selectedElement.id, (selectedElement.rotation || 0) - 15)}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 text-xs px-2 py-1 rounded transition-colors flex items-center justify-center gap-1"
                title="دوران يسار 15°"
              >
                <Icons.RotateLeft size={12} />
                -15°
              </button>
              <button
                onClick={() => onUpdateRotation?.(selectedElement.id, 0)}
                className="bg-gray-500 hover:bg-gray-600 text-white text-xs px-2 py-1 rounded transition-colors flex items-center justify-center gap-1"
                title="إعادة تعيين"
              >
                <Icons.Reset size={12} />
                0°
              </button>
              <button
                onClick={() => onUpdateRotation?.(selectedElement.id, (selectedElement.rotation || 0) + 15)}
                className="bg-gray-300 hover:bg-gray-400 text-gray-700 text-xs px-2 py-1 rounded transition-colors flex items-center justify-center gap-1"
                title="دوران يمين 15°"
              >
                <Icons.RotateRight size={12} />
                +15°
              </button>
            </div>

            {/* مدخل الدوران المخصص */}
            <div className="flex items-center gap-2">
              <input
                type="number"
                value={selectedElement.rotation || 0}
                onChange={(e) => onUpdateRotation?.(selectedElement.id, parseInt(e.target.value) || 0)}
                className="flex-1 p-1 text-xs border border-gray-300 rounded text-center bg-white text-gray-900 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                min="-360"
                max="360"
                step="1"
              />
              <span className="text-xs text-gray-500">درجة</span>
            </div>

            <div className="text-xs text-gray-500 text-right">
              💡 استخدم الأزرار للدوران السريع أو أدخل قيمة مخصصة
            </div>
          </div>
        </div>
        
        <div>
          <div className="flex items-center justify-between mb-1">
            <button
              onClick={() => setShowMagicCommands(!showMagicCommands)}
              className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-2 py-1 rounded text-xs transition-colors flex items-center gap-1"
              title="أوامر سحرية جاهزة"
            >
              <Icons.Magic size={14} />
              <span className="text-xs">أوامر جاهزة</span>
            </button>
            <label className="text-xs font-medium text-gray-700">🤖 البرومبت (الوظيفة)</label>
          </div>

          <div className="relative magic-commands-container">
            <textarea
              value={selectedElement.properties.prompt || ''}
              onChange={(e) => onUpdateElement(selectedElement.id, { prompt: e.target.value })}
              className="w-full p-1 text-xs border border-gray-300 rounded text-right h-16 bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none resize-none"
              placeholder={selectedElement.type === 'button'
                ? "مثال: طباعة الصفحة، حفظ البيانات، إرسال نموذج..."
                : "اكتب وصف الوظيفة المطلوبة..."}
            />

            {/* نافذة الأوامر السحرية العائمة */}
            {showMagicCommands && (
              <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-300 rounded shadow-lg z-50 max-h-48 overflow-y-auto">
                <div className="p-2 bg-purple-50 border-b border-purple-200">
                  <h4 className="text-sm font-medium text-purple-800 text-right">
                    <Icons.Magic size={14} className="inline ml-1" />
                    أوامر جاهزة لـ {
                      selectedElement.type === 'text' ? 'النص' :
                      selectedElement.type === 'image' ? 'الصورة' :
                      selectedElement.type === 'button' ? 'الزر' :
                      selectedElement.type === 'input' ? 'حقل الإدخال' :
                      selectedElement.type === 'div' ? 'الحاوي' : 'العنصر'
                    }
                  </h4>
                </div>
                <div className="p-1">
                  {getMagicCommandsForElement(selectedElement.type).map((command, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        addMagicCommand(command);
                        setShowMagicCommands(false);
                      }}
                      className="w-full text-right p-2 hover:bg-purple-50 rounded text-sm transition-colors border-b border-gray-100 last:border-b-0 text-gray-600 hover:text-gray-800"
                    >
                      ✨ {command}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          <div className="mt-1 text-xs text-gray-500 text-right">
            💡 <strong>نصائح:</strong> اكتب الوظيفة المطلوبة بوضوح مثل "طباعة الصفحة" أو "عرض رسالة ترحيب". بعد توليد الكود، اضغط "▶️ تشغيل الكود" لتفعيل الوظيفة.
          </div>

          {/* زر تشغيل العنصر مع مؤشرات الحالة */}
          {selectedElement.properties.prompt && (
            <div className="mt-2 flex justify-end items-center gap-1">
              {/* مؤشر التوليد الصغير */}
              {selectedElement.properties.generatedCode && (
                <span className="text-xs text-green-600" title="تم توليد كود لهذا العنصر">✅</span>
              )}

              {/* زر معاينة الكود المخزن */}
              {selectedElement.properties.generatedCode && (
                <button
                  onClick={() => {
                    // عرض الكود المخزن مباشرة بدون توليد جديد
                    onPreviewStoredCode?.(selectedElement.id);
                  }}
                  className="px-2 py-1 bg-blue-500 hover:bg-blue-600 text-white text-xs rounded transition-colors flex items-center gap-1"
                  title="معاينة الكود المولد المخزن"
                >
                  <Icons.Eye size={12} />
                  <span>معاينة</span>
                </button>
              )}

              {/* سهم نسيان التوليد */}
              {selectedElement.properties.generatedCode && (
                <button
                  onClick={() => {
                    if (confirm('هل تريد نسيان التوليد السابق؟\n\nسيتم حذف الكود المولد والعودة للحالة الأصلية.')) {
                      onUpdateElement(selectedElement.id, {
                        generatedCode: undefined
                      });
                    }
                  }}
                  className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
                  title="نسيان التوليد السابق"
                >
                  🔄
                </button>
              )}

              <button
                onClick={onGenerateElement}
                disabled={isGeneratingElement}
                className={`px-3 py-1 text-xs rounded transition-colors ${
                  isGeneratingElement
                    ? 'bg-gray-400 cursor-not-allowed text-white'
                    : 'bg-green-500 hover:bg-green-600 text-white'
                }`}
                title="توليد ومعاينة كود هذا العنصر فقط"
              >
                {isGeneratingElement ? '⏳ جاري التوليد...' : '▶️ تشغيل'}
              </button>
            </div>
          )}
        </div>

        {/* خصائص الصورة */}
        {selectedElement.type === 'image' && (
          <>
            <div>
              <label className="block text-xs font-medium mb-1 text-right text-gray-700 flex items-center justify-end gap-1">
                <Icons.Image size={14} className="text-gray-600" />
                رابط الصورة
              </label>
              <input
                type="url"
                value={selectedElement.properties.imageUrl || ''}
                onChange={(e) => onUpdateElement(selectedElement.id, { imageUrl: e.target.value })}
                className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 placeholder-gray-400 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                placeholder="https://example.com/image.jpg"
              />
              <div className="mt-1 text-xs text-gray-500 text-right">
                💡 أدخل رابط الصورة من الإنترنت
              </div>
            </div>

            <div>
              <label className="block text-xs font-medium mb-1 text-right text-gray-700">📐 نمط العرض</label>
              <select
                value={selectedElement.properties.imageMode || 'cover'}
                onChange={(e) => onUpdateElement(selectedElement.id, { imageMode: e.target.value as 'cover' | 'contain' | 'repeat' | 'stretch' })}
                className="w-full p-1 text-xs border border-gray-300 rounded text-right bg-white text-gray-900 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
              >
                <option value="cover">ملء (Cover) - تغطية كاملة</option>
                <option value="contain">احتواء (Contain) - إظهار كامل</option>
                <option value="stretch">تمدد (Stretch) - تمديد للحجم</option>
                <option value="repeat">تكرار (Repeat) - تكرار النمط</option>
              </select>
              <div className="mt-1 text-xs text-gray-500 text-right">
                💡 اختر كيفية عرض الصورة داخل الإطار
              </div>
            </div>
          </>
        )}

        {/* قسم الحجم والموضع */}
        <div className="pt-4 border-t space-y-3">
          <h4 className="text-sm font-bold text-gray-800 text-right flex items-center justify-end gap-2">
            <Icons.Resize size={14} className="text-blue-600" />
            الحجم والموضع
          </h4>

          {/* العرض والارتفاع - مع دعم النسبة المئوية المتجاوبة للحاوي */}
          {selectedElement.type === 'container' ? (
            /* نظام متقدم للحاوي - ثابت أو نسبة مئوية لكل جهاز */
            <div className="space-y-4">
              {/* مؤشر الجهاز الحالي */}
              <div className="bg-blue-50 p-2 rounded text-xs text-blue-800 text-right">
                <div className="flex items-center justify-end gap-1">
                  <span>📱 إعدادات الحجم لجهاز: {currentDevice === 'mobile' ? 'موبايل' : currentDevice === 'tablet' ? 'تابلت' : 'ديسكتوب'}</span>
                </div>
              </div>

              {/* العرض للحاوي */}
              <div>
                <label className="block text-xs font-medium mb-2 text-right text-gray-700">
                  📏 العرض
                </label>

                {/* اختيار نوع العرض */}
                <div className="flex gap-2 mb-2">
                  <label className="flex items-center gap-1 text-xs cursor-pointer">
                    <input
                      type="radio"
                      name="widthMode"
                      checked={!selectedElement.properties.responsiveSize?.[currentDevice]?.widthMode || selectedElement.properties.responsiveSize?.[currentDevice]?.widthMode === 'fixed'}
                      onChange={() => {
                        const currentValue = selectedElement.width;
                        onUpdateElement(selectedElement.id, {
                          responsiveSize: {
                            ...selectedElement.properties.responsiveSize,
                            [currentDevice]: {
                              ...selectedElement.properties.responsiveSize?.[currentDevice],
                              widthMode: 'fixed',
                              widthValue: currentValue,
                              heightMode: selectedElement.properties.responsiveSize?.[currentDevice]?.heightMode || 'fixed',
                              heightValue: selectedElement.properties.responsiveSize?.[currentDevice]?.heightValue || selectedElement.height
                            }
                          }
                        });

                        // تحديث الحجم الفعلي
                        if (onUpdateElementSize) {
                          onUpdateElementSize(selectedElement.id, currentValue, selectedElement.height);
                        }
                      }}
                    />
                    <span className="text-gray-700 font-medium">ثابت (px)</span>
                  </label>
                  <label className="flex items-center gap-1 text-xs cursor-pointer">
                    <input
                      type="radio"
                      name="widthMode"
                      checked={selectedElement.properties.responsiveSize?.[currentDevice]?.widthMode === 'percentage'}
                      onChange={() => {
                        const defaultPercentage = 50;
                        const effectiveWidth = canvasSize.width - 9; // تقليل 9px للحدود
                        const actualWidth = (defaultPercentage / 100) * effectiveWidth;

                        onUpdateElement(selectedElement.id, {
                          responsiveSize: {
                            ...selectedElement.properties.responsiveSize,
                            [currentDevice]: {
                              ...selectedElement.properties.responsiveSize?.[currentDevice],
                              widthMode: 'percentage',
                              widthValue: defaultPercentage,
                              heightMode: selectedElement.properties.responsiveSize?.[currentDevice]?.heightMode || 'fixed',
                              heightValue: selectedElement.properties.responsiveSize?.[currentDevice]?.heightValue || selectedElement.height
                            }
                          }
                        });

                        // تحديث الحجم الفعلي
                        if (onUpdateElementSize) {
                          onUpdateElementSize(selectedElement.id, actualWidth, selectedElement.height);
                        }
                      }}
                    />
                    <span className="text-gray-700 font-medium">نسبة (%)</span>
                  </label>
                </div>

                {/* حقل إدخال العرض */}
                <div className="flex items-center gap-1">
                  <input
                    type="number"
                    value={selectedElement.properties.responsiveSize?.[currentDevice]?.widthValue || selectedElement.width}
                    onChange={(e) => {
                      const newValue = Math.max(1, parseInt(e.target.value) || 1);

                      // حساب الحجم الفعلي الجديد مع مراعاة حدود الورقة
                      let actualWidth = newValue;
                      if (selectedElement.properties.responsiveSize?.[currentDevice]?.widthMode === 'percentage') {
                        const effectiveWidth = (canvasSize?.width || 800) - 9; // تقليل 9px للحدود
                        actualWidth = (newValue / 100) * effectiveWidth;
                      }

                      // تحديث القيمة والحجم الفعلي
                      onUpdateElement(selectedElement.id, {
                        responsiveSize: {
                          ...selectedElement.properties.responsiveSize,
                          [currentDevice]: {
                            ...selectedElement.properties.responsiveSize?.[currentDevice],
                            widthValue: newValue,
                            widthMode: selectedElement.properties.responsiveSize?.[currentDevice]?.widthMode || 'fixed',
                            heightMode: selectedElement.properties.responsiveSize?.[currentDevice]?.heightMode || 'fixed',
                            heightValue: selectedElement.properties.responsiveSize?.[currentDevice]?.heightValue || selectedElement.height
                          }
                        }
                      });

                      // تحديث الحجم الفعلي للعنصر
                      if (onUpdateElementSize) {
                        onUpdateElementSize(selectedElement.id, actualWidth, selectedElement.height);
                      }
                    }}
                    className="flex-1 p-1 text-xs border border-gray-300 rounded text-center bg-white text-gray-900 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                    min="1"
                    max={selectedElement.properties.responsiveSize?.[currentDevice]?.widthMode === 'percentage' ? 100 : 2000}
                  />
                  <span className="text-xs text-gray-500">
                    {selectedElement.properties.responsiveSize?.[currentDevice]?.widthMode === 'percentage' ? '%' : 'px'}
                  </span>
                </div>


              </div>

              {/* الارتفاع للحاوي */}
              <div>
                <label className="block text-xs font-medium mb-2 text-right text-gray-700">
                  📐 الارتفاع
                </label>

                {/* اختيار نوع الارتفاع */}
                <div className="flex gap-2 mb-2">
                  <label className="flex items-center gap-1 text-xs cursor-pointer">
                    <input
                      type="radio"
                      name="heightMode"
                      checked={!selectedElement.properties.responsiveSize?.[currentDevice]?.heightMode || selectedElement.properties.responsiveSize?.[currentDevice]?.heightMode === 'fixed'}
                      onChange={() => {
                        const currentValue = selectedElement.height;
                        onUpdateElement(selectedElement.id, {
                          responsiveSize: {
                            ...selectedElement.properties.responsiveSize,
                            [currentDevice]: {
                              ...selectedElement.properties.responsiveSize?.[currentDevice],
                              heightMode: 'fixed',
                              heightValue: currentValue,
                              widthMode: selectedElement.properties.responsiveSize?.[currentDevice]?.widthMode || 'fixed',
                              widthValue: selectedElement.properties.responsiveSize?.[currentDevice]?.widthValue || selectedElement.width
                            }
                          }
                        });

                        // تحديث الحجم الفعلي
                        if (onUpdateElementSize) {
                          onUpdateElementSize(selectedElement.id, selectedElement.width, currentValue);
                        }
                      }}
                    />
                    <span className="text-gray-700 font-medium">ثابت (px)</span>
                  </label>
                  <label className="flex items-center gap-1 text-xs cursor-pointer">
                    <input
                      type="radio"
                      name="heightMode"
                      checked={selectedElement.properties.responsiveSize?.[currentDevice]?.heightMode === 'percentage'}
                      onChange={() => {
                        const defaultPercentage = 50;
                        const actualHeight = (defaultPercentage / 100) * pageHeight;

                        onUpdateElement(selectedElement.id, {
                          responsiveSize: {
                            ...selectedElement.properties.responsiveSize,
                            [currentDevice]: {
                              ...selectedElement.properties.responsiveSize?.[currentDevice],
                              heightMode: 'percentage',
                              heightValue: defaultPercentage,
                              widthMode: selectedElement.properties.responsiveSize?.[currentDevice]?.widthMode || 'fixed',
                              widthValue: selectedElement.properties.responsiveSize?.[currentDevice]?.widthValue || selectedElement.width
                            }
                          }
                        });

                        // تحديث الحجم الفعلي
                        if (onUpdateElementSize) {
                          onUpdateElementSize(selectedElement.id, selectedElement.width, actualHeight);
                        }
                      }}
                    />
                    <span className="text-gray-700 font-medium">نسبة (%)</span>
                  </label>
                </div>

                {/* حقل إدخال الارتفاع */}
                <div className="flex items-center gap-1">
                  <input
                    type="number"
                    value={selectedElement.properties.responsiveSize?.[currentDevice]?.heightValue || selectedElement.height}
                    onChange={(e) => {
                      const newValue = Math.max(1, parseInt(e.target.value) || 1);

                      // حساب الحجم الفعلي الجديد مع مراعاة حدود الورقة
                      let actualHeight = newValue;
                      if (selectedElement.properties.responsiveSize?.[currentDevice]?.heightMode === 'percentage') {
                        const effectiveHeight = (pageHeight || 600) - 10; // تقليل 10px للحدود
                        actualHeight = (newValue / 100) * effectiveHeight;
                      }

                      // تحديث القيمة والحجم الفعلي
                      onUpdateElement(selectedElement.id, {
                        responsiveSize: {
                          ...selectedElement.properties.responsiveSize,
                          [currentDevice]: {
                            ...selectedElement.properties.responsiveSize?.[currentDevice],
                            heightValue: newValue,
                            heightMode: selectedElement.properties.responsiveSize?.[currentDevice]?.heightMode || 'fixed',
                            widthMode: selectedElement.properties.responsiveSize?.[currentDevice]?.widthMode || 'fixed',
                            widthValue: selectedElement.properties.responsiveSize?.[currentDevice]?.widthValue || selectedElement.width
                          }
                        }
                      });

                      // تحديث الحجم الفعلي للعنصر
                      if (onUpdateElementSize) {
                        onUpdateElementSize(selectedElement.id, selectedElement.width, actualHeight);
                      }
                    }}
                    className="flex-1 p-1 text-xs border border-gray-300 rounded text-center bg-white text-gray-900 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                    min="1"
                    max={selectedElement.properties.responsiveSize?.[currentDevice]?.heightMode === 'percentage' ? 100 : 2000}
                  />
                  <span className="text-xs text-gray-500">
                    {selectedElement.properties.responsiveSize?.[currentDevice]?.heightMode === 'percentage' ? '%' : 'px'}
                  </span>
                </div>


              </div>
            </div>
          ) : (
            /* النظام العادي لباقي العناصر */
            <div className="grid grid-cols-2 gap-2">
              <div>
                <label className="block text-xs font-medium mb-1 text-right text-gray-700">
                  العرض
                  {selectedElement.responsiveSizes && selectedElement.responsiveSizes[currentDevice] && (
                    <span className="text-blue-600 mr-1" title="مخصص لهذا الجهاز">📱</span>
                  )}
                </label>
                <input
                  type="number"
                  value={(() => {
                    if (selectedElement.responsiveSizes && selectedElement.responsiveSizes[currentDevice]) {
                      return selectedElement.responsiveSizes[currentDevice].width;
                    }
                    return selectedElement.width;
                  })()}
                  onChange={(e) => {
                    const newWidth = Math.max(10, parseInt(e.target.value) || 10);
                    const currentHeight = (() => {
                      if (selectedElement.responsiveSizes && selectedElement.responsiveSizes[currentDevice]) {
                        return selectedElement.responsiveSizes[currentDevice].height;
                      }
                      return selectedElement.height;
                    })();
                    if (onUpdateElementSize) {
                      onUpdateElementSize(selectedElement.id, newWidth, currentHeight);
                    }
                  }}
                  className="w-full p-1 text-xs border border-gray-300 rounded text-center bg-white text-gray-900 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                  min="10"
                  max="2000"
                />
              </div>

            <div>
              <label className="block text-xs font-medium mb-1 text-right text-gray-700">
                الارتفاع
                {selectedElement.responsiveSizes && selectedElement.responsiveSizes[currentDevice] && (
                  <span className="text-blue-600 mr-1" title="مخصص لهذا الجهاز">📱</span>
                )}
              </label>
              <input
                type="number"
                value={(() => {
                  if (selectedElement.responsiveSizes && selectedElement.responsiveSizes[currentDevice]) {
                    return selectedElement.responsiveSizes[currentDevice].height;
                  }
                  return selectedElement.height;
                })()}
                onChange={(e) => {
                  const newHeight = Math.max(10, parseInt(e.target.value) || 10);
                  const currentWidth = (() => {
                    if (selectedElement.responsiveSizes && selectedElement.responsiveSizes[currentDevice]) {
                      return selectedElement.responsiveSizes[currentDevice].width;
                    }
                    return selectedElement.width;
                  })();
                  if (onUpdateElementSize) {
                    onUpdateElementSize(selectedElement.id, currentWidth, newHeight);
                  }
                }}
                className="w-full p-1 text-xs border border-gray-300 rounded text-center bg-white text-gray-900 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none"
                min="10"
                max="2000"
              />
            </div>
            </div>
          )}

          {/* معلومات الحجم المتجاوب */}
          {selectedElement.responsiveSizes && selectedElement.responsiveSizes[currentDevice] && (
            <div className="bg-blue-50 p-2 rounded text-xs text-blue-800 text-right">
              <div className="flex items-center justify-end gap-1 mb-1">
                <Icons.Smartphone size={12} />
                حجم مخصص لـ {currentDevice}
              </div>
              <div className="text-blue-600">
                الحجم الأساسي: {selectedElement.width}×{selectedElement.height}
              </div>
            </div>
          )}
        </div>



        <div className="pt-4 border-t space-y-2">
          {/* تم إخفاء زر توليد الكود - المشروع كله يتم تمريره للذكاء الاصطناعي عند الضغط على "تحويل AI" */}

          <button
            onClick={() => {
              const defaultProps = selectedElement.type === 'button'
                ? { color: '#ffffff', backgroundColor: '#3b82f6' }
                : { color: '#000000', backgroundColor: '#ffffff' };
              onUpdateElement(selectedElement.id, defaultProps);
            }}
            className="w-full p-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors text-sm"
          >
            <div className="flex items-center justify-center gap-2">
              <Icons.Reset size={16} />
              إعادة تعيين الألوان
            </div>
          </button>

          <button
            onClick={() => onDeleteElement(selectedElement.id)}
            className="w-full p-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors text-sm font-medium"
          >
            <div className="flex items-center justify-center gap-2">
              <Icons.Reset size={16} />
              حذف العنصر
            </div>
          </button>

          {selectedElement.properties.generatedCode && (
            <div className="mt-4 p-3 bg-gray-800 rounded text-white text-xs">
              <h4 className="text-sm font-bold mb-2 text-green-400 flex items-center gap-2">
                <Icons.Code size={16} />
                الكود المولد:
              </h4>
              <pre className="whitespace-pre-wrap overflow-auto max-h-32 text-xs">
                {selectedElement.properties.generatedCode}
              </pre>
              <div className="flex justify-end mt-2">
                <button
                  onClick={async () => {
                    const success = await copyToClipboard(selectedElement.properties.generatedCode || '');
                    if (success) {
                      alert('تم نسخ الكود! 📋');
                    } else {
                      alert('فشل في نسخ الكود. يرجى المحاولة مرة أخرى.');
                    }
                  }}
                  className="px-2 py-1 bg-blue-600 text-white rounded text-xs hover:bg-blue-700"
                >
                  📋 نسخ
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// دالة نسخ متوافقة مع جميع المتصفحات
const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    // محاولة استخدام Clipboard API الحديث
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(text);
      return true;
    } else {
      // fallback للمتصفحات القديمة أو البيئات غير الآمنة
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      // @ts-ignore - تجاهل تحذير TypeScript للـ deprecated method
      const result = document.execCommand('copy');
      document.body.removeChild(textArea);
      return result;
    }
  } catch (error) {
    console.error('فشل في النسخ:', error);
    return false;
  }
};

export default function EditorPage() {

  // منع التمرير العام للصفحة
  useEffect(() => {
    // إخفاء التمرير العام للصفحة
    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';

    // تنظيف عند إلغاء تحميل المكون
    return () => {
      document.body.style.overflow = 'auto';
      document.documentElement.style.overflow = 'auto';
    };
  }, []);



  const [elements, setElements] = useState<Element[]>([]);
  const [selectedElementId, setSelectedElementId] = useState<string | null>(null);
  const [isPageSelected, setIsPageSelected] = useState<boolean>(false);

  // حالة الصفحات
  const [currentPageId, setCurrentPageId] = useState<string>('page_home');
  const [project, setProject] = useState<Project>({
    id: 'project_' + Date.now(),
    name: 'مشروع جديد',
    description: '',
    pages: [
      {
        id: 'page_home',
        name: 'index',
        elements: []
      }
    ],
    canvasSize: { width: 375, height: 667 },
    settings: {
      theme: 'light',
      primaryColor: '#3b82f6',
      secondaryColor: '#10b981',
      fontFamily: 'Arial, sans-serif'
    }
  });
  const [showGrid, setShowGrid] = useState(true);
  const [snapToGrid, setSnapToGrid] = useState(true);
  const [gridSize] = useState(20);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [designAnalysis, setDesignAnalysis] = useState<any>(null);
  const [showAnalysis, setShowAnalysis] = useState(false);
  const [generatedFiles, setGeneratedFiles] = useState<any[]>([]);
  const [showFiles, setShowFiles] = useState(false);
  const [canvasSize, setCanvasSize] = useState({ width: 375, height: 667 });
  const [currentDevice, setCurrentDevice] = useState('mobile');
  const [showCanvasInfo, setShowCanvasInfo] = useState(false);
  const [customSize, setCustomSize] = useState({ width: 375, height: 667 });
  const [pageHeight, setPageHeight] = useState(667); // ارتفاع الصفحة المتغير
  const [isResizingPage, setIsResizingPage] = useState(false); // حالة سحب تغيير حجم الصفحة
  const [pageResizeStart, setPageResizeStart] = useState({ y: 0, height: 0 }); // نقطة بداية السحب
  const [tempPageHeight, setTempPageHeight] = useState(667); // ارتفاع مؤقت أثناء السحب

  // حالات طي القوائم الجانبية
  const [isLayersPanelCollapsed, setIsLayersPanelCollapsed] = useState(false);
  const [isElementPanelCollapsed, setIsElementPanelCollapsed] = useState(false);
  const [isPropertiesPanelCollapsed, setIsPropertiesPanelCollapsed] = useState(false);
  const canvasRef = useRef<HTMLDivElement>(null);
  const pageCanvasRef = useRef<HTMLDivElement>(null); // مرجع للكانفاس الفعلي للصفحة
  const resizeTimeoutRef = useRef<NodeJS.Timeout | null>(null); // مرجع للـ timeout

  // حالة ملء الشاشة
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showFloatingProperties, setShowFloatingProperties] = useState(false);
  const [floatingPropertiesPosition, setFloatingPropertiesPosition] = useState({ x: 100, y: 100 });
  const [showFloatingElements, setShowFloatingElements] = useState(false);
  const [floatingElementsPosition, setFloatingElementsPosition] = useState({ x: 50, y: 50 });
  const [showDeviceDropdown, setShowDeviceDropdown] = useState(false);
  const [showCustomSizeModal, setShowCustomSizeModal] = useState(false);
  const [tempCustomSize, setTempCustomSize] = useState({ width: 375, height: 667 });
  const [showLeftPanel, setShowLeftPanel] = useState(true);

  // حالة أداة اليد لتحريك Canvas باستخدام Ctrl
  const [isCtrlPressed, setIsCtrlPressed] = useState(false);
  const [isPanning, setIsPanning] = useState(false);
  const [panStart, setPanStart] = useState({ x: 0, y: 0 });

  // بدء الموضع من المركز مباشرة
  const [canvasPosition, setCanvasPosition] = useState({ x: 0, y: 0 });





  // دالة حساب الحجم الفعلي للحاوي المتجاوب (ثابت أو نسبة مئوية لكل جهاز)
  const calculateActualSize = useCallback((element: Element, canvasWidth: number, canvasHeight: number, device: string = currentDevice) => {
    // فقط للحاوي وإذا كان له نظام حجم ديناميكي
    if (element.type !== 'container' || !element.properties.responsiveSize?.[device]) {
      return {
        width: element.width,
        height: element.height
      };
    }

    const deviceSize = element.properties.responsiveSize[device];

    // حساب العرض - النسبة المئوية من عرض الصفحة مع تعديل للحدود والـ padding
    let actualWidth = element.width;
    if (deviceSize.widthMode === 'percentage') {
      // تقليل العرض بدقة لمطابقة الصفحة (375px -> 366px = تقليل 9px)
      const effectiveWidth = canvasWidth - 9; // تقليل 9px لمطابقة الصفحة
      actualWidth = (deviceSize.widthValue / 100) * effectiveWidth;
    } else {
      actualWidth = deviceSize.widthValue;
    }

    // حساب الارتفاع - النسبة المئوية من ارتفاع الصفحة مع تعديل للحدود والـ padding
    let actualHeight = element.height;
    if (deviceSize.heightMode === 'percentage') {
      // تقليل الارتفاع بدقة لمطابقة الصفحة (667px -> 657px = تقليل 10px)
      const effectiveHeight = canvasHeight - 10; // تقليل 10px لمطابقة الصفحة
      actualHeight = (deviceSize.heightValue / 100) * effectiveHeight;
    } else {
      actualHeight = deviceSize.heightValue;
    }

    return {
      width: actualWidth,
      height: actualHeight
    };
  }, [currentDevice]);

  // دالة توسيط صحيحة - تحاكي ما يحدث بالماوس
  const centerCanvas = useCallback(() => {
    const scrollArea = scrollAreaRef.current;
    if (!scrollArea) return;

    console.log('📊 معلومات التمرير:');
    console.log('scrollWidth:', scrollArea.scrollWidth);
    console.log('clientWidth:', scrollArea.clientWidth);
    console.log('scrollHeight:', scrollArea.scrollHeight);
    console.log('clientHeight:', scrollArea.clientHeight);
    console.log('scrollLeft الحالي:', scrollArea.scrollLeft);
    console.log('scrollTop الحالي:', scrollArea.scrollTop);

    // استخدام الإحداثيات المثالية المحفوظة من المستخدم
    const centerScrollLeft = -788.8; // الموضع المثالي الذي حدده المستخدم
    const centerScrollTop = 462.4;   // الموضع المثالي الذي حدده المستخدم

    console.log('🎯 الوسط المحسوب:');
    console.log('centerScrollLeft:', centerScrollLeft);
    console.log('centerScrollTop:', centerScrollTop);

    // تحريك أشرطة التمرير للوسط
    scrollArea.scrollTo({
      left: centerScrollLeft,
      top: centerScrollTop,
      behavior: 'smooth'
    });
  }, []);

  // حالة الزوم - البداية بـ 70%
  const [zoomLevel, setZoomLevel] = useState(0.7);
  const [forceRender, setForceRender] = useState(0); // لإجبار إعادة الرسم

  // تطبيق التوسيط المثالي تلقائياً عند فتح المشروع لأول مرة
  useEffect(() => {
    const timer = setTimeout(() => {
      centerCanvas(); // استخدام التوسيط المثالي
    }, 500); // انتظار قصير لضمان تحميل العناصر

    return () => clearTimeout(timer);
  }, []); // dependencies فارغة = تشغيل مرة واحدة فقط عند التحميل



  // حالة النسخ واللصق
  const [copiedElement, setCopiedElement] = useState<Element | null>(null);

  // حالة معاينة العنصر المنفرد
  const [showElementPreview, setShowElementPreview] = useState(false);
  const [elementPreviewCode, setElementPreviewCode] = useState('');


  const [isGeneratingElement, setIsGeneratingElement] = useState(false);

  // دالة لاستخراج الكود النظيف من HTML الكامل
  const extractCleanCode = (htmlCode: string, elementId: string): string => {
    try {
      // استخراج محتوى body
      const bodyMatch = htmlCode.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
      let bodyContent = bodyMatch ? bodyMatch[1].trim() : htmlCode;

      // استخراج CSS من style tag
      const styleMatch = htmlCode.match(/<style[^>]*>([\s\S]*?)<\/style>/i);
      let cssCode = '';
      if (styleMatch && styleMatch[1]) {
        cssCode = styleMatch[1].trim()
          // إزالة تعريفات body وhtml العامة
          .replace(/body\s*{[^}]*}/gi, '')
          .replace(/html\s*{[^}]*}/gi, '')
          .replace(/\*\s*{[^}]*}/gi, '');
      }

      // استخراج JavaScript من script tag
      const scriptMatch = htmlCode.match(/<script[^>]*>([\s\S]*?)<\/script>/i);
      let jsCode = '';
      if (scriptMatch && scriptMatch[1]) {
        jsCode = scriptMatch[1].trim();
        // استبدال معرفات العناصر بالمعرف الصحيح
        const idMatches = htmlCode.match(/id="([^"]+)"/g);
        if (idMatches) {
          idMatches.forEach(match => {
            const originalId = match.match(/id="([^"]+)"/)?.[1];
            if (originalId && originalId !== elementId) {
              jsCode = jsCode.replace(new RegExp(originalId.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), elementId);
            }
          });
        }
      }

      // تجميع الكود النظيف
      let cleanCode = '';

      // إضافة HTML مع تحديث المعرف
      if (bodyContent) {
        cleanCode += bodyContent.replace(/id="[^"]+"/g, `id="${elementId}"`);
      }

      // إضافة CSS إذا وجد
      if (cssCode.trim()) {
        cleanCode += '\n\n<style>\n' + cssCode + '\n</style>';
      }

      // إضافة JavaScript إذا وجد
      if (jsCode.trim()) {
        cleanCode += '\n\n<script>\n' + jsCode + '\n</script>';
      }

      return cleanCode.trim();
    } catch (error) {
      console.error('خطأ في استخراج الكود النظيف:', error);
      return htmlCode; // إرجاع الكود الأصلي في حالة الخطأ
    }
  };

  // دالة معاينة الكود المخزن
  const previewStoredCode = (elementId: string) => {
    const element = elements.find(el => el.id === elementId);
    if (!element || !element.properties.generatedCode) {
      alert('⚠️ لا يوجد كود مولد مخزن لهذا العنصر');
      return;
    }

    // تطبيق الفلتر لاستخراج الكود النظيف من الكود المخزن
    const cleanCode = extractCleanCode(element.properties.generatedCode, element.id);
    setElementPreviewCode(cleanCode);
    setShowElementPreview(true);
  };

  // دالة توليد HTML للمعاينة المباشرة
  const generateLivePreviewHTML = (mode: 'current-page' | 'full-project'): string => {
    try {
      const currentPageData = project.pages.find(p => p.id === currentPageId);
      if (!currentPageData) return '';

      // تحديث بيانات الصفحة الحالية بالعناصر الحالية
      const updatedCurrentPage = {
        ...currentPageData,
        elements: elements,
        pageHeight: pageHeight
      };

      const pagesToRender = mode === 'current-page'
        ? [updatedCurrentPage]
        : project.pages.map(page =>
            page.id === currentPageId ? updatedCurrentPage : page
          );

      // توليد CSS للصفحات (سيتم تحديثه ديناميكياً في صفحة المعاينة)
      const generatePageCSS = (page: Page, device: string = 'desktop') => {
        const pageProps = page.properties || {};
        const responsiveHeight = page.responsivePageHeights?.[device]?.height || page.pageHeight || 800;

        let css = `
          /* صفحة ${page.name} */
          .page-${page.id} {
            min-height: ${responsiveHeight}px;
            background-color: ${pageProps.canvasBackgroundColor || '#ffffff'};
            ${pageProps.backgroundImage ? `background-image: url(${pageProps.backgroundImage});` : ''}
            ${pageProps.backgroundSize ? `background-size: ${pageProps.backgroundSize};` : ''}
            ${pageProps.backgroundRepeat ? `background-repeat: ${pageProps.backgroundRepeat};` : ''}
            ${pageProps.backgroundPosition ? `background-position: ${pageProps.backgroundPosition};` : ''}
            color: ${pageProps.textColor || '#000000'};
            font-family: ${pageProps.fontFamily || 'Arial, sans-serif'};
            position: relative;
            overflow: hidden;
          }
        `;

        // CSS للعناصر
        page.elements.forEach(element => {
          const elementId = `element_${element.id}`;

          // الحصول على الموضع والحجم المتجاوب
          const responsivePos = element.responsivePositions?.[device];
          const responsiveSize = element.responsiveSizes?.[device];

          const x = responsivePos?.x ?? element.x;
          const y = responsivePos?.y ?? element.y;
          const width = responsiveSize?.width ?? element.width;
          const height = responsiveSize?.height ?? element.height;

          css += `
            #${elementId} {
              position: absolute;
              left: ${x}px;
              top: ${y}px;
              width: ${width}px;
              height: ${height}px;
              ${element.rotation ? `transform: rotate(${element.rotation}deg);` : ''}
              z-index: ${element.zIndex || 1};
              ${element.isVisible === false ? 'display: none;' : ''}

              /* خصائص العنصر */
              font-size: ${element.properties.fontSize || 14}px;
              color: ${element.properties.color || '#000000'};
              background-color: ${element.properties.backgroundColor || 'transparent'};
              border-radius: ${element.properties.borderRadius || 4}px;
              padding: ${element.properties.padding || 8}px;
              margin: ${element.properties.margin || 0}px;

              /* خصائص خاصة بنوع العنصر */
              ${element.type === 'button' ? `
                border: none;
                cursor: pointer;
                font-weight: 600;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: all 0.3s ease;
              ` : ''}

              ${element.type === 'text' ? `
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                word-wrap: break-word;
              ` : ''}

              ${element.type === 'input' || element.type === 'textarea' ? `
                border: 1px solid #d1d5db;
                outline: none;
              ` : ''}

              ${element.type === 'image' && element.properties.imageUrl ? `
                background-image: url(${element.properties.imageUrl});
                background-size: ${element.properties.imageMode || 'cover'};
                background-position: center;
                background-repeat: no-repeat;
              ` : ''}
            }

            #${elementId}:hover {
              ${element.type === 'button' ? 'opacity: 0.9; transform: scale(1.02);' : ''}
            }
          `;
        });

        return css;
      };

      // توليد HTML للصفحات
      const generatePageHTML = (page: Page) => {
        let html = `<div class="page-${page.id}" id="page-${page.id}">`;

        page.elements.forEach(element => {
          const elementId = `element_${element.id}`;

          switch (element.type) {
            case 'button':
              html += `<button id="${elementId}" ${element.properties.prompt ? `data-prompt="${element.properties.prompt}"` : ''} onclick="handleElementClick('${element.id}', '${element.properties.prompt || ''}')">${element.properties.text || 'زر جديد'}</button>`;
              break;

            case 'text':
              html += `<div id="${elementId}">${element.properties.text || 'نص تجريبي'}</div>`;
              break;

            case 'input':
              html += `<div id="${elementId}">
                ${element.properties.label ? `<label style="display: block; margin-bottom: 4px; font-size: 12px;">${element.properties.label}</label>` : ''}
                <input type="${element.properties.inputType || 'text'}" placeholder="${element.properties.placeholder || 'أدخل النص...'}" style="width: 100%; height: auto; border: 1px solid #d1d5db; border-radius: 4px; padding: 8px;" />
              </div>`;
              break;

            case 'textarea':
              html += `<div id="${elementId}">
                ${element.properties.title || element.properties.label ? `<label style="display: block; margin-bottom: 4px; font-size: 12px;">${element.properties.title || element.properties.label}</label>` : ''}
                <textarea placeholder="${element.properties.placeholder || 'أدخل النص الطويل...'}" style="width: 100%; height: auto; border: 1px solid #d1d5db; border-radius: 4px; padding: 8px; resize: none;">${element.properties.text || ''}</textarea>
              </div>`;
              break;

            case 'image':
              html += `<div id="${elementId}" style="background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                ${!element.properties.imageUrl ? '<div style="text-align: center; color: #6b7280;"><div style="font-size: 24px;">🖼️</div><div style="font-size: 12px;">صورة</div></div>' : ''}
              </div>`;
              break;

            default:
              html += `<div id="${elementId}" style="border: 2px dashed #d1d5db; display: flex; align-items: center; justify-content: center; color: #6b7280;">
                <div style="text-align: center;">
                  <div style="font-size: 24px;">${element.properties.icon || '📦'}</div>
                  <div style="font-size: 12px;">${element.properties.title || element.type}</div>
                </div>
              </div>`;
          }
        });

        html += '</div>';
        return html;
      };

      // توليد JavaScript للتفاعل
      const generateInteractiveJS = () => {
        return `
          // نظام التفاعل للمعاينة المباشرة
          function handleElementClick(elementId, prompt) {
            console.log('تم النقر على العنصر:', elementId, 'البرومبت:', prompt);

            if (!prompt || prompt.trim() === '') {
              showMessage('هذا العنصر غير تفاعلي', 'info');
              return;
            }

            // تنفيذ الوظائف التفاعلية بناءً على البرومبت
            executePromptFunction(elementId, prompt);
          }

          function executePromptFunction(elementId, prompt) {
            const element = document.getElementById('element_' + elementId);
            if (!element) return;

            const lowerPrompt = prompt.toLowerCase();

            // وظائف التواصل
            if (lowerPrompt.includes('واتساب') || lowerPrompt.includes('whatsapp')) {
              const phoneMatch = prompt.match(/\\\\d{10,15}/);
              const phone = phoneMatch ? phoneMatch[0] : '966501234567';
              const message = extractMessage(prompt) || 'مرحباً';
              window.open(\`https://wa.me/\${phone}?text=\${encodeURIComponent(message)}\`, '_blank');
              showMessage('تم فتح واتساب 📱', 'success');
            }
            else if (lowerPrompt.includes('ايميل') || lowerPrompt.includes('email')) {
              const emailMatch = prompt.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\\\.[a-zA-Z]{2,}/);
              const email = emailMatch ? emailMatch[0] : '<EMAIL>';
              const subject = extractSubject(prompt) || 'استفسار من الموقع';
              const body = extractMessage(prompt) || 'مرحباً، أود الاستفسار عن خدماتكم.';
              window.open(\`mailto:\${email}?subject=\${encodeURIComponent(subject)}&body=\${encodeURIComponent(body)}\`, '_blank');
              showMessage('تم فتح الإيميل 📧', 'success');
            }
            else if (lowerPrompt.includes('اتصال') || lowerPrompt.includes('call')) {
              const phoneMatch = prompt.match(/\\\\d{10,15}/);
              const phone = phoneMatch ? phoneMatch[0] : '966501234567';
              window.open(\`tel:\${phone}\`, '_blank');
              showMessage('جاري الاتصال 📞', 'success');
            }
            else if (lowerPrompt.includes('موقع') || lowerPrompt.includes('رابط')) {
              const urlMatch = prompt.match(/https?:\\\\/\\\\/[^\\\\s]+/);
              let url = urlMatch ? urlMatch[0] : 'https://www.google.com';
              if (!url.startsWith('http')) url = 'https://' + url;
              window.open(url, '_blank');
              showMessage('تم فتح الموقع 🌐', 'success');
            }
            // وظائف التصميم
            else if (lowerPrompt.includes('لون')) {
              const colors = {
                'أحمر': '#e74c3c', 'أزرق': '#3498db', 'أخضر': '#27ae60',
                'أصفر': '#f1c40f', 'بنفسجي': '#9b59b6', 'برتقالي': '#e67e22'
              };
              let targetColor = '#3498db';
              for (const [colorName, colorValue] of Object.entries(colors)) {
                if (lowerPrompt.includes(colorName)) {
                  targetColor = colorValue;
                  break;
                }
              }
              element.style.backgroundColor = targetColor;
              showMessage('تم تغيير اللون 🎨', 'success');
            }
            else if (lowerPrompt.includes('اخف') || lowerPrompt.includes('hide')) {
              element.style.display = 'none';
              showMessage('تم إخفاء العنصر 👻', 'info');
            }
            else if (lowerPrompt.includes('اظهر') || lowerPrompt.includes('show')) {
              element.style.display = 'block';
              showMessage('تم إظهار العنصر 👁️', 'info');
            }
            else {
              // تأثير عام
              element.style.boxShadow = '0 0 20px rgba(52, 152, 219, 0.8)';
              element.style.transform = 'scale(1.05)';
              setTimeout(() => {
                element.style.boxShadow = '';
                element.style.transform = 'scale(1)';
              }, 1000);
              showMessage(\`تم تنفيذ: \${prompt} ✨\`, 'success');
            }
          }
        `;
      };

      // تجميع HTML الكامل
      const fullHTML = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>${project.name || 'معاينة مباشرة'}</title>
          <style>
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }

            body {
              font-family: 'Arial', sans-serif;
              background-color: ${project.pages[0]?.properties?.backgroundColor || '#f5f5f5'};
              overflow-x: auto;
              overflow-y: auto;
            }

            ${pagesToRender.map(page => generatePageCSS(page, currentDevice)).join('\\n')}

            /* تنسيقات إضافية للمعاينة */
            .preview-header {
              position: fixed;
              top: 0;
              left: 0;
              right: 0;
              z-index: 10000;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              padding: 10px 20px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
              display: flex;
              justify-content: space-between;
              align-items: center;
            }

            .preview-title {
              font-size: 16px;
              font-weight: bold;
              display: flex;
              align-items: center;
              gap: 10px;
            }

            .preview-controls {
              display: flex;
              align-items: center;
              gap: 15px;
            }

            .device-controls {
              display: flex;
              align-items: center;
              gap: 10px;
              background: rgba(255,255,255,0.1);
              padding: 8px 12px;
              border-radius: 8px;
              border: 1px solid rgba(255,255,255,0.2);
            }

            .device-selector {
              background: rgba(255,255,255,0.2);
              border: 1px solid rgba(255,255,255,0.3);
              color: white;
              padding: 4px 8px;
              border-radius: 4px;
              font-size: 11px;
              min-width: 100px;
            }

            .device-selector option {
              background: #333;
              color: white;
            }

            .size-controls {
              display: flex;
              align-items: center;
              gap: 5px;
            }

            .size-input {
              background: rgba(255,255,255,0.2);
              border: 1px solid rgba(255,255,255,0.3);
              color: white;
              padding: 2px 6px;
              border-radius: 3px;
              font-size: 11px;
              width: 60px;
              text-align: center;
            }

            .size-input::placeholder {
              color: rgba(255,255,255,0.7);
            }

            .preset-buttons {
              display: flex;
              gap: 3px;
            }

            .preset-btn {
              background: rgba(255,255,255,0.2);
              border: 1px solid rgba(255,255,255,0.3);
              color: white;
              padding: 2px 6px;
              border-radius: 3px;
              cursor: pointer;
              font-size: 10px;
              transition: all 0.3s ease;
            }

            .preset-btn:hover {
              background: rgba(255,255,255,0.3);
            }

            .preset-btn.active {
              background: rgba(255,255,255,0.4);
              border-color: rgba(255,255,255,0.6);
            }

            .page-navigation {
              display: flex;
              gap: 5px;
            }

            .page-navigation button {
              background: rgba(255,255,255,0.2);
              color: white;
              border: 1px solid rgba(255,255,255,0.3);
              padding: 5px 10px;
              border-radius: 3px;
              cursor: pointer;
              font-size: 11px;
              transition: all 0.3s ease;
            }

            .page-navigation button:hover {
              background: rgba(255,255,255,0.3);
            }

            .page-navigation button.active {
              background: rgba(255,255,255,0.4);
              border-color: rgba(255,255,255,0.6);
            }

            .content-area {
              margin-top: 60px;
              min-height: calc(100vh - 60px);
            }

            /* دوال مساعدة للرسائل */
            .message {
              position: fixed;
              top: 20px;
              right: 20px;
              padding: 15px 20px;
              border-radius: 8px;
              color: white;
              font-weight: bold;
              z-index: 9999;
              transform: translateX(100%);
              transition: transform 0.3s ease;
            }
          </style>
        </head>
        <body>
          <!-- شريط التحكم العلوي -->
          <div class="preview-header">
            <div class="preview-title">
              <span>👁️</span>
              <span>معاينة مباشرة - ${project.name || 'مشروع'}</span>
              <span style="font-size: 12px; opacity: 0.8;">(${mode === 'current-page' ? 'الصفحة الحالية' : 'المشروع الكامل'})</span>
            </div>

            <div class="preview-controls">
              <!-- تحكم في الجهاز والحجم -->
              <div class="device-controls">
                <select class="device-selector" onchange="changeDevice(this.value)" id="deviceSelector">
                  <option value="desktop">🖥️ ديسكتوب</option>
                  <option value="tablet">📱 تابلت</option>
                  <option value="mobile">📱 موبايل</option>
                  <option value="custom">⚙️ مخصص</option>
                </select>

                <div class="size-controls">
                  <input type="number" class="size-input" id="widthInput" placeholder="العرض" min="200" max="2000" onchange="updateCustomSize()">
                  <span style="color: rgba(255,255,255,0.7); font-size: 10px;">×</span>
                  <input type="number" class="size-input" id="heightInput" placeholder="الارتفاع" min="200" max="2000" onchange="updateCustomSize()">
                </div>

                <div class="preset-buttons">
                  <button class="preset-btn" onclick="setPresetSize(1920, 1080)" title="Full HD">FHD</button>
                  <button class="preset-btn" onclick="setPresetSize(1366, 768)" title="Laptop">LAP</button>
                  <button class="preset-btn" onclick="setPresetSize(768, 1024)" title="iPad">iPAD</button>
                  <button class="preset-btn" onclick="setPresetSize(375, 667)" title="iPhone">iPH</button>
                </div>
              </div>

              ${mode === 'full-project' && pagesToRender.length > 1 ? `
                <div class="page-navigation">
                  ${pagesToRender.map((page, index) =>
                    `<button onclick="showPage('${page.id}')" id="nav-${page.id}" ${index === 0 ? 'class="active"' : ''}>${page.name}</button>`
                  ).join('')}
                </div>
              ` : ''}

              <button onclick="window.close()" style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white; padding: 8px 12px; border-radius: 4px; cursor: pointer; font-size: 11px;">
                ✕ إغلاق
              </button>
            </div>
          </div>

          <!-- منطقة المحتوى -->
          <div class="content-area">
            <div id="pages-container">
              ${pagesToRender.map((page, index) =>
                `<div id="page-container-${page.id}" style="display: ${index === 0 ? 'block' : 'none'};">
                  ${generatePageHTML(page)}
                </div>`
              ).join('')}
            </div>
          </div>

          <script>
            ${generateInteractiveJS()}

            function extractMessage(prompt) {
              if (prompt.includes('رسالة:')) return prompt.split('رسالة:')[1].trim();
              if (prompt.includes('message:')) return prompt.split('message:')[1].trim();
              return null;
            }

            function extractSubject(prompt) {
              if (prompt.includes('موضوع:')) return prompt.split('موضوع:')[1].split(/رسالة:|message:/)[0].trim();
              if (prompt.includes('subject:')) return prompt.split('subject:')[1].split(/رسالة:|message:/)[0].trim();
              return null;
            }

            function showMessage(message, type = 'info') {
              const messageDiv = document.createElement('div');
              messageDiv.textContent = message;
              messageDiv.className = 'message';
              messageDiv.style.backgroundColor = type === 'success' ? '#27ae60' : type === 'error' ? '#e74c3c' : '#3498db';

              document.body.appendChild(messageDiv);
              setTimeout(() => messageDiv.style.transform = 'translateX(0)', 100);
              setTimeout(() => {
                messageDiv.style.transform = 'translateX(100%)';
                setTimeout(() => messageDiv.remove(), 300);
              }, 3000);
            }

            // متغيرات التحكم في الحجم
            let currentDevice = 'desktop';
            let currentWidth = window.innerWidth;
            let currentHeight = window.innerHeight;

            // تحديث حجم النافذة
            function updateWindowSize(width, height) {
              try {
                window.resizeTo(width, height);
                currentWidth = width;
                currentHeight = height;

                // تحديث قيم الإدخال
                document.getElementById('widthInput').value = width;
                document.getElementById('heightInput').value = height;

                showMessage(\`تم تغيير الحجم إلى \${width}×\${height}\`, 'success');
              } catch (error) {
                console.log('لا يمكن تغيير حجم النافذة تلقائياً');
              }
            }

            // تغيير الجهاز
            function changeDevice(device) {
              currentDevice = device;

              // إزالة التحديد من جميع الأزرار المسبقة
              document.querySelectorAll('.preset-btn').forEach(btn => btn.classList.remove('active'));

              switch(device) {
                case 'desktop':
                  updateWindowSize(1200, 800);
                  break;
                case 'tablet':
                  updateWindowSize(768, 1024);
                  break;
                case 'mobile':
                  updateWindowSize(375, 667);
                  break;
                case 'custom':
                  // لا نغير الحجم، نتركه للمستخدم
                  break;
              }
            }

            // تحديث الحجم المخصص
            function updateCustomSize() {
              const width = parseInt(document.getElementById('widthInput').value);
              const height = parseInt(document.getElementById('heightInput').value);

              if (width && height && width >= 200 && height >= 200) {
                document.getElementById('deviceSelector').value = 'custom';
                currentDevice = 'custom';
                updateWindowSize(width, height);
              }
            }

            // تعيين حجم مسبق
            function setPresetSize(width, height) {
              // إزالة التحديد من جميع الأزرار
              document.querySelectorAll('.preset-btn').forEach(btn => btn.classList.remove('active'));

              // تحديد الزر الحالي
              event.target.classList.add('active');

              document.getElementById('deviceSelector').value = 'custom';
              currentDevice = 'custom';
              updateWindowSize(width, height);
            }

            // تهيئة القيم عند التحميل
            document.addEventListener('DOMContentLoaded', function() {
              document.getElementById('widthInput').value = currentWidth;
              document.getElementById('heightInput').value = currentHeight;
            });

            ${mode === 'full-project' && pagesToRender.length > 1 ? `
              function showPage(pageId) {
                // إخفاء جميع الصفحات
                document.querySelectorAll('[id^="page-container-"]').forEach(container => {
                  container.style.display = 'none';
                });

                // إظهار الصفحة المحددة
                const targetContainer = document.getElementById('page-container-' + pageId);
                if (targetContainer) {
                  targetContainer.style.display = 'block';
                }

                // تحديث أزرار التنقل
                document.querySelectorAll('[id^="nav-"]').forEach(btn => {
                  btn.classList.remove('active');
                });
                const targetBtn = document.getElementById('nav-' + pageId);
                if (targetBtn) {
                  targetBtn.classList.add('active');
                }
              }
            ` : ''}

            console.log('تم تحميل المعاينة المباشرة بنجاح ✅');
            console.log('عدد الصفحات:', ${pagesToRender.length});
            console.log('وضع المعاينة:', '${mode}');
          </script>
        </body>
        </html>
      `;

      return fullHTML;
    } catch (error) {
      console.error('خطأ في توليد HTML للمعاينة:', error);
      return `
        <!DOCTYPE html>
        <html>
        <head><title>خطأ في المعاينة</title></head>
        <body style="display: flex; align-items: center; justify-content: center; height: 100vh; font-family: Arial;">
          <div style="text-align: center; color: #e74c3c;">
            <h2>⚠️ خطأ في توليد المعاينة</h2>
            <p>حدث خطأ أثناء توليد المعاينة المباشرة</p>
            <p style="font-size: 12px; color: #666;">${error}</p>
          </div>
        </body>
        </html>
      `;
    }
  };

  // دوال المعاينة المباشرة
  const openLivePreview = (mode: 'current-page' | 'full-project') => {
    try {
      // التحقق من وجود عناصر للمعاينة
      const currentPageData = project.pages.find(p => p.id === currentPageId);
      if (!currentPageData) {
        alert('⚠️ لا توجد صفحة حالية للمعاينة');
        return;
      }

      const totalElements = mode === 'current-page'
        ? elements.length
        : project.pages.reduce((total, page) => total + (page.elements?.length || 0), 0);

      if (totalElements === 0) {
        alert('⚠️ لا توجد عناصر للمعاينة\n\nيرجى إضافة عناصر للتصميم أولاً');
        return;
      }

      // توليد HTML للمعاينة
      const html = generateLivePreviewHTML(mode);

      // فتح نافذة جديدة بحجم افتراضي
      const previewWindow = window.open('', '_blank', 'width=1200,height=800,scrollbars=yes,resizable=yes');

      if (previewWindow) {
        // كتابة HTML في النافذة الجديدة
        previewWindow.document.write(html);
        previewWindow.document.close();

        // إضافة عنوان للنافذة
        previewWindow.document.title = `معاينة مباشرة - ${project.name || 'مشروع'} (${mode === 'current-page' ? 'الصفحة الحالية' : 'المشروع الكامل'})`;

        // التركيز على النافذة الجديدة
        previewWindow.focus();
      } else {
        alert('⚠️ لم يتمكن من فتح نافذة المعاينة\n\nيرجى السماح للنوافذ المنبثقة في المتصفح');
      }
    } catch (error) {
      console.error('خطأ في فتح المعاينة المباشرة:', error);
      alert('حدث خطأ في فتح المعاينة المباشرة. يرجى المحاولة مرة أخرى.');
    }
  };

  // دالة توليد كود العنصر المنفرد
  const generateElementCode = async () => {
    const selectedElement = elements.find(el => el.id === selectedElementId);
    if (!selectedElement || !selectedElement.properties.prompt) {
      alert('⚠️ يرجى كتابة وصف الوظيفة في البرومبت أولاً');
      return;
    }

    setIsGeneratingElement(true);

    try {
      const response = await fetch('/api/generate-element', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          element: selectedElement,
          prompt: selectedElement.properties.prompt
        }),
      });

      if (!response.ok) {
        throw new Error('فشل في توليد الكود');
      }

      const data = await response.json();

      // حفظ الكود المولد في العنصر
      setElements(prev => prev.map(el =>
        el.id === selectedElement.id
          ? {
              ...el,
              properties: {
                ...el.properties,
                generatedCode: data.code
              }
            }
          : el
      ));

      // حفظ في المشروع أيضاً
      setProject(prev => ({
        ...prev,
        pages: prev.pages.map(page =>
          page.id === currentPageId
            ? {
                ...page,
                elements: page.elements.map(el =>
                  el.id === selectedElement.id
                    ? {
                        ...el,
                        properties: {
                          ...el.properties,
                          generatedCode: data.code
                        }
                      }
                    : el
                )
              }
            : page
        )
      }));

      // تطبيق الفلتر لاستخراج الكود النظيف للمعاينة
      const cleanCode = extractCleanCode(data.code, selectedElement.id);
      setElementPreviewCode(cleanCode);
      setShowElementPreview(true);
    } catch (error) {
      console.error('خطأ في توليد الكود:', error);
      alert('حدث خطأ في توليد الكود. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsGeneratingElement(false);
    }
  };



  // دالة معالجة الزوم المحسنة مع حرية التنقل
  const handleZoom = useCallback((event: WheelEvent) => {
    // التحقق من الضغط على Alt
    if (!event.altKey) return;

    event.preventDefault();
    event.stopPropagation();

    const scrollArea = scrollAreaRef.current;
    if (!scrollArea) return;

    // الحصول على موقع الماوس نسبة لمنطقة التمرير
    const rect = scrollArea.getBoundingClientRect();
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;

    // تحديد اتجاه الزوم
    const zoomDirection = event.deltaY < 0 ? 1 : -1;
    const zoomFactor = 0.1;
    const newZoomLevel = Math.max(0.1, Math.min(5, zoomLevel + (zoomDirection * zoomFactor)));

    // إذا لم يتغير الزوم، لا نفعل شيء
    if (newZoomLevel === zoomLevel) return;

    // زوم بسيط من المركز بدون تعقيدات
    setZoomLevel(newZoomLevel);

    // الحفاظ على الموضع النسبي بشكل مبسط
    const zoomRatio = newZoomLevel / zoomLevel;
    setCanvasPosition(prev => ({
      x: prev.x * zoomRatio,
      y: prev.y * zoomRatio
    }));
  }, [zoomLevel]);

  // إضافة مستمع الأحداث للزوم على منطقة التمرير
  useEffect(() => {
    const scrollArea = scrollAreaRef.current;
    if (!scrollArea) return;

    scrollArea.addEventListener('wheel', handleZoom, { passive: false });

    return () => {
      scrollArea.removeEventListener('wheel', handleZoom);
    };
  }, [handleZoom]);

  // دالة عرض العناصر للصفحات غير النشطة (معاينة فقط)
  const renderElementPreview = (element: Element) => {
    const properties = element.properties || {};
    const baseStyle: React.CSSProperties = {
      backgroundColor: properties.backgroundColor || 'transparent',
      color: properties.color || '#000',
      fontSize: `${properties.fontSize || 14}px`,
      borderRadius: `${properties.borderRadius || 0}px`,
      border: '1px solid #e5e7eb',
      fontWeight: 'normal',
    };

    switch (element.type) {
      case 'button':
        return (
          <div
            className="flex items-center justify-center bg-blue-500 text-white rounded border"
            style={baseStyle}
          >
            {element.properties.text || 'زر'}
          </div>
        );
      case 'text':
        return (
          <div
            className="flex items-center justify-center text-gray-800"
            style={baseStyle}
          >
            {element.properties.text || 'نص'}
          </div>
        );
      case 'input':
        return (
          <div
            className="border border-gray-300 bg-white rounded px-2 py-1 text-gray-500"
            style={baseStyle}
          >
            {element.properties.placeholder || 'حقل إدخال'}
          </div>
        );
      case 'image':
        return (
          <div
            className="border-2 border-dashed border-gray-300 bg-gray-50 flex items-center justify-center text-gray-500"
            style={baseStyle}
          >
            🖼️
          </div>
        );
      case 'circle':
        return (
          <div
            className="rounded-full flex items-center justify-center"
            style={{
              ...baseStyle,
              backgroundColor: properties.backgroundColor || '#3b82f6',
              color: properties.color || '#fff',
              borderRadius: '50%',
            }}
          >
            {properties.text || ''}
          </div>
        );
      case 'square':
        return (
          <div
            className="flex items-center justify-center"
            style={{
              ...baseStyle,
              backgroundColor: properties.backgroundColor || '#ef4444',
              color: properties.color || '#fff',
              borderRadius: 0,
            }}
          >
            {properties.text || ''}
          </div>
        );
      case 'arrow':
        return (
          <div
            className="flex items-center justify-center font-bold"
            style={{
              ...baseStyle,
              backgroundColor: properties.backgroundColor || '#10b981',
              color: properties.color || '#fff',
            }}
          >
            {properties.text || '→'}
          </div>
        );

      case 'star':
        return (
          <div
            className="flex items-center justify-center font-bold"
            style={{
              ...baseStyle,
              backgroundColor: properties.backgroundColor || '#8b5cf6',
              color: properties.color || '#fff',
            }}
          >
            {properties.text || '★'}
          </div>
        );

      case 'container':
        return (
          <div
            className="border-2 border-dashed border-blue-300 bg-blue-50 flex items-center justify-center text-blue-600 rounded"
            style={{
              ...baseStyle,
              backgroundColor: properties.backgroundColor || 'transparent',
              minHeight: '40px',
              fontSize: '10px'
            }}
          >
            📦 حاوي
          </div>
        );

      default:
        return (
          <div
            className="border border-gray-300 bg-gray-100 flex items-center justify-center text-gray-500"
            style={baseStyle}
          >
            {element.type}
          </div>
        );
    }
  };

  // دوال إدارة الصفحات
  const getCurrentPage = (): Page => {
    return project.pages.find(page => page.id === currentPageId) || project.pages[0];
  };

  // تحديث عنوان الصفحة في المتصفح بناءً على خصائص الصفحة الحالية
  useEffect(() => {
    const currentPage = getCurrentPage();
    const pageTitle = currentPage.properties?.title;

    if (pageTitle && pageTitle.trim() !== '') {
      document.title = `${pageTitle} - AI Website Builder`;
    } else {
      // استخدام اسم المشروع أو اسم الصفحة كبديل
      const fallbackTitle = project.name !== 'مشروع جديد' ? project.name : `صفحة ${currentPage.name}`;
      document.title = `${fallbackTitle} - AI Website Builder`;
    }
  }, [currentPageId, project.pages, project.name]);



  const addNewPage = (name: string) => {
    const newPage: Page = {
      id: 'page_' + Date.now(),
      name: name,
      elements: []
    };

    setProject(prev => ({
      ...prev,
      pages: [...prev.pages, newPage]
    }));

    setCurrentPageId(newPage.id);
    setElements([]);
    setSelectedElementId(null);
  };

  const switchToPage = (pageId: string) => {
    // حفظ العناصر الحالية في الصفحة الحالية
    setProject(prev => {
      const updatedPages = prev.pages.map(page =>
        page.id === currentPageId
          ? { ...page, elements: elements, pageHeight: pageHeight }
          : page
      );

      // العثور على الصفحة الجديدة
      const newPage = updatedPages.find(page => page.id === pageId);

      // تحديث العناصر للصفحة الجديدة
      setElements(newPage?.elements || []);
      setSelectedElementId(null);

      // تحديث ارتفاع الصفحة للصفحة الجديدة حسب الجهاز الحالي
      if (newPage) {
        const newPageHeight = getPageHeight(newPage);
        setPageHeight(newPageHeight);
        setTempPageHeight(newPageHeight);
      } else {
        setPageHeight(canvasSize.height);
        setTempPageHeight(canvasSize.height);
      }

      return {
        ...prev,
        pages: updatedPages
      };
    });

    // التبديل للصفحة الجديدة
    setCurrentPageId(pageId);
  };

  // بداية سحب تغيير حجم الصفحة
  const handlePageResizeStart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsResizingPage(true);
    setPageResizeStart({ y: e.clientY, height: pageHeight });
    setTempPageHeight(pageHeight);
  };

  // حركة سحب تغيير حجم الصفحة - تحديث مباشر مثل الإدخال المباشر
  const handlePageResizeMove = useCallback((e: MouseEvent) => {
    if (!isResizingPage) return;

    const deltaY = e.clientY - pageResizeStart.y;
    const newHeight = Math.max(canvasSize.height, pageResizeStart.height + deltaY);

    // تحديث فوري مثل الإدخال المباشر
    setPageHeight(newHeight);
    setTempPageHeight(newHeight);

    // حفظ فوري في المشروع مثل الإدخال المباشر
    setProject(prev => ({
      ...prev,
      pages: prev.pages.map(page => {
        if (page.id === currentPageId) {
          const responsivePageHeights = page.responsivePageHeights || {};
          const updatedResponsiveHeights = {
            ...responsivePageHeights,
            [currentDevice]: {
              height: newHeight,
              customized: true
            }
          };
          return {
            ...page,
            pageHeight: newHeight,
            responsivePageHeights: updatedResponsiveHeights
          };
        }
        return page;
      })
    }));
  }, [isResizingPage, pageResizeStart, canvasSize.height, currentPageId, currentDevice]);

  // انتهاء سحب تغيير حجم الصفحة - بسيط لأن الحفظ يتم أثناء السحب
  const handlePageResizeEnd = () => {
    if (!isResizingPage) return;

    // فقط إيقاف حالة السحب - الحفظ تم أثناء السحب
    setIsResizingPage(false);
  };

  const deletePage = (pageId: string) => {
    if (project.pages.length <= 1) {
      alert('لا يمكن حذف الصفحة الوحيدة');
      return;
    }

    if (confirm('هل أنت متأكد من حذف هذه الصفحة؟')) {
      setProject(prev => ({
        ...prev,
        pages: prev.pages.filter(page => page.id !== pageId)
      }));

      // إذا كانت الصفحة المحذوفة هي الحالية، انتقل للصفحة الأولى
      if (currentPageId === pageId) {
        const remainingPages = project.pages.filter(page => page.id !== pageId);
        setCurrentPageId(remainingPages[0].id);
        setElements(remainingPages[0].elements);
        setSelectedElementId(null);
      }
    }
  };

  // تحديث العناصر عند تغيير الصفحة فقط
  useEffect(() => {
    const currentPage = getCurrentPage();
    // تحسين الأداء: تحديث فقط إذا تغيرت العناصر فعلاً
    const newElements = currentPage.elements || [];
    if (JSON.stringify(newElements) !== JSON.stringify(elements)) {
      setElements(newElements);
    }
  }, [currentPageId]); // إزالة project.pages من dependencies لتجنب الحلقة اللا نهائية

  // حفظ العناصر في الصفحة الحالية عند تغييرها (بدون تحديث project.pages مباشرة)
  // سيتم الحفظ عند التبديل بين الصفحات أو التصدير

  // حالة قائمة السياق (Context Menu)
  const [contextMenu, setContextMenu] = useState<{
    show: boolean;
    x: number;
    y: number;
    elementId: string | null;
    pageId: string | null;
    type: 'element' | 'page' | null;
  }>({
    show: false,
    x: 0,
    y: 0,
    elementId: null,
    pageId: null,
    type: null,
  });

  // حالة نافذة اختيار الصفحات للتوليد
  const [showPageSelection, setShowPageSelection] = useState(false);
  const [selectedPagesForGeneration, setSelectedPagesForGeneration] = useState<string[]>([]);

  // حالة نافذة الطبقات (تم حذف النظام القديم)
  const [isGenerating, setIsGenerating] = useState(false);

  // حالة التحريك للمنطقة الرئيسية
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const [isScrollPanning, setIsScrollPanning] = useState(false);
  const [scrollPanStart, setScrollPanStart] = useState({ x: 0, y: 0, scrollLeft: 0, scrollTop: 0 });

  // دوال التحريك للمنطقة الرئيسية - تعمل بحرية في جميع الصفحات
  const handleScrollAreaMouseDown = (e: React.MouseEvent) => {
    // التحريك يعمل دائماً بـ Ctrl، بغض النظر عن الصفحة
    if (!isCtrlPressed) return;

    e.preventDefault();
    e.stopPropagation();

    const scrollArea = scrollAreaRef.current;
    if (!scrollArea) return;

    setIsScrollPanning(true);
    setScrollPanStart({
      x: e.clientX,
      y: e.clientY,
      scrollLeft: scrollArea.scrollLeft,
      scrollTop: scrollArea.scrollTop
    });

    // إضافة class للجسم لمنع التحديد أثناء السحب
    document.body.style.userSelect = 'none';
    document.body.style.cursor = 'grabbing';
  };

  const handleScrollAreaMouseMove = useCallback((e: MouseEvent) => {
    if (!isScrollPanning || !scrollAreaRef.current) return;

    e.preventDefault();

    const deltaX = e.clientX - scrollPanStart.x;
    const deltaY = e.clientY - scrollPanStart.y;

    // إصلاح التحريك الأفقي: تحديث مباشر بدون requestAnimationFrame
    scrollAreaRef.current.scrollLeft = scrollPanStart.scrollLeft - deltaX;
    scrollAreaRef.current.scrollTop = scrollPanStart.scrollTop - deltaY;
  }, [isScrollPanning, scrollPanStart]);

  const handleScrollAreaMouseUp = useCallback(() => {
    setIsScrollPanning(false);
    // إعادة تعيين cursor والتحديد
    document.body.style.userSelect = '';
    document.body.style.cursor = '';
  }, []);

  // إضافة مستمعي الأحداث للتحريك
  useEffect(() => {
    if (isScrollPanning) {
      document.addEventListener('mousemove', handleScrollAreaMouseMove);
      document.addEventListener('mouseup', handleScrollAreaMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleScrollAreaMouseMove);
        document.removeEventListener('mouseup', handleScrollAreaMouseUp);
      };
    }
  }, [isScrollPanning, handleScrollAreaMouseMove, handleScrollAreaMouseUp]);

  // معاينة وتنزيل المشروع بدون AI
  const previewAndDownloadProject = () => {
    // التحقق من وجود عناصر في أي صفحة
    const totalElements = project.pages.reduce((total, page) => total + (page.elements?.length || 0), 0);

    if (totalElements === 0) {
      alert('⚠️ لا توجد عناصر للمعاينة\n\nيرجى إضافة عناصر للتصميم أولاً');
      return;
    }

    // فتح نافذة اختيار الصفحات للمعاينة والتنزيل المباشر
    setSelectedPagesForGeneration(project.pages.map(page => page.id)); // تحديد جميع الصفحات افتراضياً
    setShowPageSelection(true);
  };

  // فتح نافذة اختيار الصفحات للتوليد بـ AI
  const openPageSelectionDialog = () => {
    // التحقق من وجود تحليل للتصميم
    if (!designAnalysis) {
      alert('⚠️ يجب استخدام AI أولاً!\n\nانقر على "🤖 استخدام AI" لتحليل وتحسين التصميم قبل التوليد.');
      return;
    }

    setSelectedPagesForGeneration(project.pages.map(page => page.id)); // تحديد جميع الصفحات افتراضياً
    setShowPageSelection(true);
  };

  // توليد الصفحات المختارة
  const generateSelectedPages = async () => {
    if (selectedPagesForGeneration.length === 0) {
      alert('يرجى اختيار صفحة واحدة على الأقل');
      return;
    }

    // حفظ العناصر الحالية قبل التوليد
    const updatedProject = {
      ...project,
      pages: project.pages.map(page =>
        page.id === currentPageId
          ? { ...page, elements: elements }
          : page
      )
    };

    // فلترة الصفحات المختارة
    const selectedPages = updatedProject.pages.filter(page =>
      selectedPagesForGeneration.includes(page.id)
    );

    setIsGenerating(true);
    setShowPageSelection(false);

    try {
      const response = await fetch('/api/generate-project', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          // إرسال البيانات الكاملة للذكاء الاصطناعي بنفس البنية
          name: updatedProject.name,
          description: updatedProject.description,
          pages: selectedPages, // الصفحات المختارة فقط
          allPages: updatedProject.pages, // جميع الصفحات للمرجع
          canvasSize,
          settings: {
            theme: "light",
            primaryColor: "#3b82f6",
            secondaryColor: "#10b981",
            fontFamily: "Arial, sans-serif"
          },
          exportDate: new Date().toISOString(),
          version: "2.0",
          analysis: designAnalysis,
          selectedPagesCount: selectedPages.length,
          totalPagesCount: updatedProject.pages.length
        }),
      });

      if (response.ok) {
        const result = await response.json();
        downloadProjectFiles(result.files, updatedProject.name);
      } else {
        throw new Error('فشل في توليد المشروع');
      }
    } catch (error) {
      console.error('خطأ في توليد المشروع:', error);
      alert('حدث خطأ أثناء توليد المشروع');
    } finally {
      setIsGenerating(false);
    }
  };

  // دالة مساعدة للـ Snap to Grid
  const snapValueToGrid = useCallback((value: number) => {
    if (!snapToGrid) return value;
    return Math.round(value / gridSize) * gridSize;
  }, [snapToGrid, gridSize]);

  // معالج أحداث مفتاح Ctrl لأداة اليد + اختصارات النسخ واللصق
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && !isCtrlPressed) {
        setIsCtrlPressed(true);
      }

      // اختصارات النسخ واللصق والتكرار
      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'c':
            if (selectedElementId) {
              e.preventDefault();
              copyElement(selectedElementId);
            }
            break;
          case 'v':
            if (copiedElement) {
              e.preventDefault();
              pasteElement();
            }
            break;
          case 'd':
            if (selectedElementId) {
              e.preventDefault();
              duplicateElement(selectedElementId);
            }
            break;
        }
      }

      // حذف العنصر المحدد
      if (e.key === 'Delete' && selectedElementId) {
        e.preventDefault();
        deleteElement(selectedElementId);
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (!e.ctrlKey && isCtrlPressed) {
        setIsCtrlPressed(false);
        setIsPanning(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('keyup', handleKeyUp);
    };
  }, [isCtrlPressed, selectedElementId, copiedElement]);

  // معالجات تحريك Canvas
  const handleCanvasMouseDown = useCallback((e: React.MouseEvent) => {
    if (isCtrlPressed) {
      e.preventDefault();
      e.stopPropagation();
      setIsPanning(true);
      setPanStart({ x: e.clientX, y: e.clientY });
    } else {
      // إلغاء تحديد العنصر وتحديد الصفحة عند النقر على مكان فارغ في Canvas
      const target = e.target as HTMLElement;
      const isCanvasOrBackground = e.target === e.currentTarget ||
                                   target.style.backgroundImage?.includes('linear-gradient') ||
                                   target.classList.contains('canvas-background');
      if (isCanvasOrBackground) {
        setSelectedElementId(null);
        setIsPageSelected(true); // تحديد الصفحة لعرض خصائصها
      }
    }
  }, [isCtrlPressed]);

  // معالج النقر بزر الماوس الأيمن على Canvas
  const handleCanvasContextMenu = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // إظهار قائمة السياق للـ Canvas
    setContextMenu({
      show: true,
      x: e.clientX,
      y: e.clientY,
      elementId: null, // null يعني Canvas وليس عنصر
      pageId: null,
      type: null,
    });
  }, []);

  const handleCanvasMouseMove = useCallback((e: MouseEvent) => {
    if (isPanning && isCtrlPressed) {
      const deltaX = e.clientX - panStart.x;
      const deltaY = e.clientY - panStart.y;

      // تطبيق التحريك الحر بدون قيود
      setCanvasPosition(prev => ({
        x: prev.x + deltaX,
        y: prev.y + deltaY
      }));

      setPanStart({ x: e.clientX, y: e.clientY });
    }
  }, [isPanning, isCtrlPressed, panStart]);

  const handleCanvasMouseUp = useCallback(() => {
    if (isPanning) {
      setIsPanning(false);
    }
  }, [isPanning]);

  // إضافة معالجات الماوس للتحريك
  useEffect(() => {
    if (isPanning) {
      document.addEventListener('mousemove', handleCanvasMouseMove);
      document.addEventListener('mouseup', handleCanvasMouseUp);

      return () => {
        document.removeEventListener('mousemove', handleCanvasMouseMove);
        document.removeEventListener('mouseup', handleCanvasMouseUp);
      };
    }
  }, [isPanning, handleCanvasMouseMove, handleCanvasMouseUp]);

  // وظائف ملء الشاشة
  const toggleFullscreen = useCallback(() => {
    if (!isFullscreen) {
      // الدخول في ملء الشاشة - إخفاء الأعمدة الجانبية
      setShowLeftPanel(false);
      setIsFullscreen(true);
      // فتح نافذة العناصر العائمة تلقائياً
      setShowFloatingElements(true);
    } else {
      // الخروج من ملء الشاشة - إظهار الأعمدة الجانبية
      setShowLeftPanel(true);
      setIsFullscreen(false);
      // إغلاق النوافذ العائمة
      setShowFloatingProperties(false);
      setShowFloatingElements(false);
    }
  }, [isFullscreen]);

  // فتح الخصائص العائمة
  const openFloatingProperties = useCallback((elementId: string, event?: React.MouseEvent) => {
    setSelectedElementId(elementId);
    setShowFloatingProperties(true);

    if (event) {
      // تحديد موقع النافذة العائمة بناءً على موقع النقر
      setFloatingPropertiesPosition({
        x: Math.min(event.clientX, window.innerWidth - 320),
        y: Math.min(event.clientY, window.innerHeight - 400)
      });
    }
  }, []);

  // إغلاق الخصائص العائمة
  const closeFloatingProperties = useCallback(() => {
    setShowFloatingProperties(false);
  }, []);

  // إغلاق نافذة العناصر العائمة
  const closeFloatingElements = useCallback(() => {
    setShowFloatingElements(false);
  }, []);

  // فتح نافذة الحجم المخصص
  const openCustomSizeModal = useCallback(() => {
    setTempCustomSize({ ...customSize });
    setShowCustomSizeModal(true);
    setShowDeviceDropdown(false);
  }, [customSize]);

  // تطبيق الحجم المخصص
  const applyCustomSizeFromModal = useCallback(() => {
    setCustomSize({ ...tempCustomSize });
    changeCanvasSize('custom', tempCustomSize.width, tempCustomSize.height);
    setShowCustomSizeModal(false);
  }, [tempCustomSize]);

  // إلغاء الحجم المخصص
  const cancelCustomSize = useCallback(() => {
    setShowCustomSizeModal(false);
  }, []);

  // تصدير المشروع
  const exportProject = useCallback(() => {
    // حفظ العناصر الحالية في الصفحة الحالية قبل التصدير
    const updatedProject = {
      ...project,
      pages: project.pages.map(page =>
        page.id === currentPageId
          ? { ...page, elements: elements, pageHeight: pageHeight }
          : page
      ),
      canvasSize: canvasSize
    };

    const projectData = {
      name: updatedProject.name,
      description: updatedProject.description,
      pages: updatedProject.pages,
      canvasSize: updatedProject.canvasSize,
      settings: updatedProject.settings,
      exportDate: new Date().toISOString(),
      version: '2.0' // نسخة جديدة تدعم الصفحات المتعددة
    };

    const dataStr = JSON.stringify(projectData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `${project.name.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    alert('✅ تم تصدير المشروع بنجاح!');
  }, [project, elements, canvasSize]);

  // استيراد المشروع
  const importProject = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const projectData = JSON.parse(e.target?.result as string);

          // التحقق من نوع الملف (قديم أم جديد)
          let importedProject: Project;

          if (projectData.pages && Array.isArray(projectData.pages)) {
            // ملف جديد يدعم الصفحات المتعددة
            importedProject = {
              id: 'project_' + Date.now(),
              name: projectData.name || 'مشروع مستورد',
              description: projectData.description || '',
              pages: projectData.pages,
              canvasSize: projectData.canvasSize || { width: 375, height: 667 },
              settings: projectData.settings || {
                theme: 'light',
                primaryColor: '#3b82f6',
                secondaryColor: '#10b981',
                fontFamily: 'Arial, sans-serif'
              }
            };
          } else if (projectData.elements && Array.isArray(projectData.elements)) {
            // ملف قديم - تحويل إلى نظام الصفحات الجديد
            importedProject = {
              id: 'project_' + Date.now(),
              name: projectData.name || 'مشروع مستورد',
              description: projectData.description || '',
              pages: [
                {
                  id: 'page_home',
                  name: 'index',
                  elements: projectData.elements
                }
              ],
              canvasSize: projectData.canvasSize || { width: 375, height: 667 },
              settings: projectData.settings || {
                theme: 'light',
                primaryColor: '#3b82f6',
                secondaryColor: '#10b981',
                fontFamily: 'Arial, sans-serif'
              }
            };
          } else {
            throw new Error('ملف غير صالح - لا يحتوي على عناصر أو صفحات');
          }

          // استيراد البيانات
          setProject(importedProject);
          setCurrentPageId(importedProject.pages[0].id);
          setElements(importedProject.pages[0].elements);

          if (projectData.canvasSize) {
            setCanvasSize(projectData.canvasSize);
            setCustomSize(projectData.canvasSize);
          }

          alert('✅ تم استيراد المشروع بنجاح!');
        } catch (error) {
          alert('❌ خطأ في استيراد المشروع. تأكد من صحة الملف.');
          console.error('خطأ في الاستيراد:', error);
        }
      };

      reader.readAsText(file);
    };

    input.click();
  }, []);

  // إغلاق القائمة المنسدلة عند النقر خارجها
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showDeviceDropdown && !(event.target as HTMLElement).closest('.relative')) {
        setShowDeviceDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showDeviceDropdown]);

  // دالة للحصول على الأبعاد الافتراضية للعنصر
  const getDefaultDimensions = (type: Element['type']) => {
    switch (type) {
      case 'button': return { width: 120, height: 40 };
      case 'input': return { width: 200, height: 40 };
      case 'textarea': return { width: 200, height: 80 };
      case 'select': return { width: 200, height: 40 };
      case 'table': return { width: 300, height: 150 };
      case 'form': return { width: 300, height: 200 };
      case 'div': return { width: 200, height: 100 };
      case 'content': return { width: 250, height: 150 };
      case 'image': return { width: 150, height: 100 };
      case 'text': return { width: 100, height: 30 };
      case 'container': return { width: 300, height: 200 };
      case 'circle': return { width: 80, height: 80 };
      case 'square': return { width: 80, height: 80 };
      case 'arrow': return { width: 100, height: 50 };
      case 'star': return { width: 80, height: 80 };
      default: return { width: 100, height: 30 };
    }
  };

  // دالة للحصول على الخصائص الافتراضية للعنصر
  const getDefaultProperties = (type: Element['type']) => {
    const baseProps = {
      color: '#000000',
      backgroundColor: 'transparent',
      fontSize: 14,
      borderRadius: 4,
      padding: 8,
      prompt: '',
      link: '', // رابط العنصر
    };

    switch (type) {
      case 'button':
        return {
          ...baseProps,
          text: 'زر جديد',
          backgroundColor: '#3b82f6',
          color: '#ffffff',
        };
      case 'input':
        return {
          ...baseProps,
          placeholder: 'أدخل النص...',
          label: 'حقل إدخال',
          inputType: 'text' as const,
          required: false,
        };
      case 'textarea':
        return {
          ...baseProps,
          text: 'هذا نص طويل يمكن تحريره من لوحة الخصائص. يمكنك كتابة أي محتوى تريده هنا.',
          placeholder: 'أدخل النص الطويل...',
          label: 'منطقة نص',
          title: 'منطقة نص',
          description: 'منطقة لإدخال النص الطويل',
          icon: '📝',
        };
      case 'select':
        return {
          ...baseProps,
          label: 'قائمة اختيار',
          title: 'قائمة اختيار',
          description: 'اختر من الخيارات المتاحة',
          icon: '🔽',
          options: ['الخيار الأول', 'الخيار الثاني', 'الخيار الثالث'],
        };
      case 'table':
        return {
          ...baseProps,
          columns: ['العمود الأول', 'العمود الثاني', 'العمود الثالث'],
          rows: [
            ['بيانات 1', 'بيانات 2', 'بيانات 3'],
            ['بيانات 4', 'بيانات 5', 'بيانات 6'],
          ],
        };
      case 'form':
        return {
          ...baseProps,
          title: 'نموذج',
          description: 'اسحب العناصر هنا',
          icon: '📋',
          backgroundColor: '#f9fafb',
        };
      case 'text':
        return {
          ...baseProps,
          text: 'نص تجريبي',
          fontSize: 16,
        };
      case 'div':
        return {
          ...baseProps,
          text: 'حاوي المحتوى',
          title: 'حاوي',
          description: 'منطقة المحتوى',
          icon: '📦',
          backgroundColor: '#f9fafb',
        };
      case 'content':
        return {
          ...baseProps,
          title: 'منطقة محتوى',
          description: 'منطقة محتوى تفاعلية',
          icon: '📄',
          backgroundColor: '#f0f9ff',
        };
      case 'image':
        return {
          ...baseProps,
          text: 'صورة',
          imageUrl: '',
          imageMode: 'cover' as const, // cover, contain, repeat, stretch
        };
      case 'circle':
        return {
          ...baseProps,
          text: '',
          backgroundColor: '#3b82f6',
          borderRadius: 50, // دائرة كاملة
        };
      case 'square':
        return {
          ...baseProps,
          text: '',
          backgroundColor: '#ef4444',
          borderRadius: 0,
        };
      case 'arrow':
        return {
          ...baseProps,
          text: '→',
          backgroundColor: '#10b981',
          color: '#ffffff',
          fontSize: 24,
        };

      case 'star':
        return {
          ...baseProps,
          text: '★',
          backgroundColor: '#8b5cf6',
          color: '#ffffff',
          fontSize: 32,
        };

      case 'container':
        return {
          ...baseProps,
          layoutType: 'flex-row' as const,
          justifyContent: 'flex-start' as const,
          alignItems: 'stretch' as const, // توحيد مع المحرر والتصدير
          gap: 8,
          gridColumns: 2,
          gridRows: 2,
          backgroundColor: 'transparent',
          text: '', // بدون نص افتراضي
          // تخطيط متجاوب افتراضي
          responsiveLayout: {
            mobile: {
              layoutType: 'flex-col' as const,
              justifyContent: 'flex-start' as const,
              alignItems: 'stretch' as const, // توحيد مع المحرر والتصدير
              gap: 8,
              gridColumns: 1,
              gridRows: 2,
              justifyItems: 'stretch' as const,
              alignContent: 'start' as const
            },
            tablet: {
              layoutType: 'flex-row' as const,
              justifyContent: 'flex-start' as const,
              alignItems: 'stretch' as const, // توحيد مع المحرر والتصدير
              gap: 12,
              gridColumns: 2,
              gridRows: 2,
              justifyItems: 'stretch' as const,
              alignContent: 'start' as const
            },
            desktop: {
              layoutType: 'flex-row' as const,
              justifyContent: 'flex-start' as const,
              alignItems: 'stretch' as const, // توحيد مع المحرر والتصدير
              gap: 16,
              gridColumns: 3,
              gridRows: 2,
              justifyItems: 'stretch' as const,
              alignContent: 'start' as const
            }
          },
          // القيم الافتراضية لنظام الحجم المتجاوب
          responsiveSize: {
            mobile: {
              widthMode: 'percentage' as const,
              heightMode: 'fixed' as const,
              widthValue: 90, // 90% على الموبايل
              heightValue: 200
            },
            tablet: {
              widthMode: 'percentage' as const,
              heightMode: 'fixed' as const,
              widthValue: 70, // 70% على التابلت
              heightValue: 200
            },
            desktop: {
              widthMode: 'percentage' as const,
              heightMode: 'fixed' as const,
              widthValue: 50, // 50% على الديسكتوب
              heightValue: 200
            }
          }
        };
      default:
        return baseProps;
    }
  };

  // إضافة عنصر جديد محسن
  const addElement = (type: Element['type']) => {
    const dimensions = getDefaultDimensions(type);
    const properties = getDefaultProperties(type);

    // حساب أعلى z-index موجود وإضافة 1
    const maxZIndex = Math.max(...elements.map(el => el.zIndex || 0), 0);

    const newElement: Element = {
      id: `element_${Date.now()}`,
      type,
      x: snapValueToGrid(100),
      y: snapValueToGrid(100),
      width: dimensions.width,
      height: dimensions.height,
      zIndex: maxZIndex + 1, // العنصر الجديد يظهر في المقدمة
      properties,
    };

    // تحديث فوري للعناصر
    setElements(prev => [...prev, newElement]);

    // تحديث المشروع مع الصفحة الحالية
    setProject(prev => ({
      ...prev,
      pages: prev.pages.map(page =>
        page.id === currentPageId
          ? { ...page, elements: [...(page.elements || []), newElement] }
          : page
      )
    }));

    setSelectedElementId(newElement.id);
  };

  // تحديث خصائص العنصر - محسن للتحديث الفوري
  const updateElement = (id: string, properties: Partial<Element['properties']>) => {
    // تحديث فوري للعناصر
    setElements(prev => prev.map(el =>
      el.id === id
        ? { ...el, properties: { ...el.properties, ...properties } }
        : el
    ));

    // تحديث المشروع مع الصفحة الحالية
    setProject(prev => ({
      ...prev,
      pages: prev.pages.map(page =>
        page.id === currentPageId
          ? {
              ...page,
              elements: page.elements.map(el =>
                el.id === id
                  ? { ...el, properties: { ...el.properties, ...properties } }
                  : el
              )
            }
          : page
      )
    }));
  };

  // تحديث خصائص الصفحة
  const updatePageProperties = (pageId: string, properties: Partial<Page['properties']>) => {
    setProject(prev => ({
      ...prev,
      pages: prev.pages.map(page =>
        page.id === pageId
          ? { ...page, properties: { ...page.properties, ...properties } }
          : page
      )
    }));
  };

  // دالة لتحديد العنصر وإلغاء تحديد الصفحة
  const selectElement = (elementId: string) => {
    setSelectedElementId(elementId);
    setIsPageSelected(false);
  };

  // تحديث موقع العنصر - محسن للسلاسة مع دعم المواضع المتجاوبة
  const updateElementPosition = useCallback((id: string, x: number, y: number) => {
    const snappedX = snapValueToGrid(x);
    const snappedY = snapValueToGrid(y);

    // تحديث المشروع فقط - مصدر واحد للحقيقة
    setProject(prev => {
      const updatedPages = prev.pages.map(page =>
        page.id === currentPageId
          ? {
              ...page,
              elements: page.elements.map(el => {
                if (el.id === id) {
                  // تحديث موحد لجميع الأجهزة - الموقع الأساسي + responsivePositions
                  const newResponsivePositions = {
                    ...el.responsivePositions,
                    [currentDevice]: {
                      x: snappedX,
                      y: snappedY,
                      customized: true
                    }
                  };

                  return {
                    ...el,
                    x: snappedX,
                    y: snappedY,
                    responsivePositions: newResponsivePositions
                  };
                }
                return el;
              })
            }
          : page
      );

      // تحديث العناصر المحلية فوراً من المشروع المحدث
      const currentPage = updatedPages.find(page => page.id === currentPageId);
      if (currentPage) {
        setElements(currentPage.elements);
      }

      return {
        ...prev,
        pages: updatedPages
      };
    });
  }, [snapValueToGrid, currentPageId, currentDevice]);

  // تحديث حجم العنصر - محسن للسلاسة مع دعم الأحجام المتجاوبة
  const updateElementSize = useCallback((id: string, width: number, height: number) => {
    const newWidth = Math.max(50, width);
    const newHeight = Math.max(30, height);

    // تحديث المشروع فقط - مصدر واحد للحقيقة
    setProject(prev => {
      const updatedPages = prev.pages.map(page =>
        page.id === currentPageId
          ? {
              ...page,
              elements: page.elements.map(el => {
                if (el.id === id) {
                  // تحديث موحد لجميع الأجهزة - الحجم الأساسي + responsiveSizes
                  const newResponsiveSizes = {
                    ...el.responsiveSizes,
                    [currentDevice]: {
                      width: newWidth,
                      height: newHeight,
                      customized: true
                    }
                  };

                  return {
                    ...el,
                    width: newWidth,
                    height: newHeight,
                    responsiveSizes: newResponsiveSizes
                  };
                }
                return el;
              })
            }
          : page
      );

      // تحديث العناصر المحلية فوراً من المشروع المحدث
      const currentPage = updatedPages.find(page => page.id === currentPageId);
      if (currentPage) {
        setElements(currentPage.elements);
      }

      return {
        ...prev,
        pages: updatedPages
      };
    });
  }, [currentPageId, currentDevice]);

  // تحديث دوران العنصر
  const updateElementRotation = (id: string, rotation: number) => {
    // تحديث فوري للعناصر
    setElements(prev => prev.map(el =>
      el.id === id
        ? { ...el, rotation: rotation }
        : el
    ));

    // تحديث المشروع مع الصفحة الحالية
    setProject(prev => ({
      ...prev,
      pages: prev.pages.map(page =>
        page.id === currentPageId
          ? {
              ...page,
              elements: page.elements.map(el =>
                el.id === id
                  ? { ...el, rotation: rotation }
                  : el
              )
            }
          : page
      )
    }));
  };

  // حذف العنصر
  const deleteElement = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
      // تحديث فوري للعناصر
      setElements(prev => prev.filter(el => el.id !== id));

      // تحديث المشروع مع الصفحة الحالية
      setProject(prev => ({
        ...prev,
        pages: prev.pages.map(page =>
          page.id === currentPageId
            ? { ...page, elements: page.elements.filter(el => el.id !== id) }
            : page
        )
      }));

      // إلغاء تحديد العنصر إذا كان محدداً
      if (selectedElementId === id) {
        setSelectedElementId(null);
      }
      setContextMenu({ show: false, x: 0, y: 0, elementId: null, pageId: null, type: null });
    }
  };

  // دوال التحكم في الطبقات
  const toggleElementVisibility = (id: string) => {
    setElements(prev => prev.map(el =>
      el.id === id ? { ...el, isVisible: el.isVisible === false ? true : false } : el
    ));

    setProject(prev => ({
      ...prev,
      pages: prev.pages.map(page =>
        page.id === currentPageId
          ? {
              ...page,
              elements: page.elements.map(el =>
                el.id === id ? { ...el, isVisible: el.isVisible === false ? true : false } : el
              )
            }
          : page
      )
    }));
  };

  const toggleElementLock = (id: string) => {
    setElements(prev => prev.map(el =>
      el.id === id ? { ...el, isLocked: !el.isLocked } : el
    ));

    setProject(prev => ({
      ...prev,
      pages: prev.pages.map(page =>
        page.id === currentPageId
          ? {
              ...page,
              elements: page.elements.map(el =>
                el.id === id ? { ...el, isLocked: !el.isLocked } : el
              )
            }
          : page
      )
    }));
  };

  const moveElementToParent = (elementId: string, newParentId: string | null, _newIndex: number) => {
    setElements(prev => prev.map(el => {
      if (el.id === elementId) {
        return { ...el, parentId: newParentId || undefined };
      }
      return el;
    }));

    setProject(prev => ({
      ...prev,
      pages: prev.pages.map(page =>
        page.id === currentPageId
          ? {
              ...page,
              elements: page.elements.map(el =>
                el.id === elementId ? { ...el, parentId: newParentId || undefined } : el
              )
            }
          : page
      )
    }));
  };



  // نسخ عنصر
  const copyElement = (id: string) => {
    const element = elements.find(el => el.id === id);
    if (element) {
      setCopiedElement(element);
      // إغلاق قائمة السياق فوراً
      setContextMenu({ show: false, x: 0, y: 0, elementId: null, pageId: null, type: null });
      // إجبار إعادة الرسم
      setSelectedElementId(id);
    }
  };

  // دالة لتحديث معرفات العناصر في الكود المولد
  const updateGeneratedCodeIds = (generatedCode: string, oldId: string, newId: string): string => {
    if (!generatedCode) return generatedCode;

    // استبدال جميع المعرفات في الكود
    let updatedCode = generatedCode;

    // استبدال في HTML attributes
    updatedCode = updatedCode.replace(new RegExp(`id="${oldId}"`, 'g'), `id="${newId}"`);
    updatedCode = updatedCode.replace(new RegExp(`id='${oldId}'`, 'g'), `id='${newId}'`);

    // استبدال في JavaScript
    updatedCode = updatedCode.replace(new RegExp(`getElementById\\("${oldId}"\\)`, 'g'), `getElementById("${newId}")`);
    updatedCode = updatedCode.replace(new RegExp(`getElementById\\('${oldId}'\\)`, 'g'), `getElementById('${newId}')`);

    // استبدال في CSS selectors
    updatedCode = updatedCode.replace(new RegExp(`#${oldId}`, 'g'), `#${newId}`);

    // استبدال أي مراجع أخرى للمعرف
    updatedCode = updatedCode.replace(new RegExp(`"${oldId}"`, 'g'), `"${newId}"`);
    updatedCode = updatedCode.replace(new RegExp(`'${oldId}'`, 'g'), `'${newId}'`);

    return updatedCode;
  };

  // لصق عنصر
  const pasteElement = () => {
    if (copiedElement) {
      const newId = `element_${Date.now()}`;
      const newElement: Element = {
        ...copiedElement,
        id: newId,
        x: copiedElement.x + 20, // إزاحة بسيطة لتجنب التداخل
        y: copiedElement.y + 20,
        properties: {
          ...copiedElement.properties,
          // تحديث الكود المولد إذا كان موجوداً
          generatedCode: copiedElement.properties.generatedCode
            ? updateGeneratedCodeIds(copiedElement.properties.generatedCode, copiedElement.id, newId)
            : undefined
        }
      };

      // تحديث العناصر والصفحة الحالية
      const updatedElements = [...elements, newElement];
      setElements(updatedElements);
      setSelectedElementId(newElement.id);

      // تحديث الصفحة في المشروع
      setProject(prev => ({
        ...prev,
        pages: prev.pages.map(page =>
          page.id === currentPageId
            ? { ...page, elements: updatedElements }
            : page
        )
      }));

      // إغلاق قائمة السياق
      setContextMenu({ show: false, x: 0, y: 0, elementId: null, pageId: null, type: null });
    }
  };

  // تكرار عنصر
  const duplicateElement = (id: string) => {
    const element = elements.find(el => el.id === id);
    if (element) {
      const newId = `element_${Date.now()}`;
      // حساب أعلى z-index موجود وإضافة 1
      const maxZIndex = Math.max(...elements.map(el => el.zIndex || 0), 0);

      const newElement: Element = {
        ...element,
        id: newId,
        x: element.x + 20, // إزاحة بسيطة لتجنب التداخل
        y: element.y + 20,
        zIndex: maxZIndex + 1, // العنصر المكرر يظهر في المقدمة
        properties: {
          ...element.properties,
          // تحديث الكود المولد إذا كان موجوداً
          generatedCode: element.properties.generatedCode
            ? updateGeneratedCodeIds(element.properties.generatedCode, element.id, newId)
            : undefined
        }
      };

      // تحديث العناصر والصفحة الحالية
      const updatedElements = [...elements, newElement];
      setElements(updatedElements);
      setSelectedElementId(newElement.id);

      // تحديث الصفحة في المشروع
      setProject(prev => ({
        ...prev,
        pages: prev.pages.map(page =>
          page.id === currentPageId
            ? { ...page, elements: updatedElements }
            : page
        )
      }));

      // إغلاق قائمة السياق
      setContextMenu({ show: false, x: 0, y: 0, elementId: null, pageId: null, type: null });
    }
  };

  // معالج قائمة السياق للعناصر
  const handleElementContextMenu = (elementId: string, x: number, y: number) => {
    setContextMenu({
      show: true,
      x,
      y,
      elementId,
      pageId: null,
      type: 'element',
    });
  };

  // معالج قائمة السياق للصفحات
  const handlePageContextMenu = (pageId: string, x: number, y: number) => {
    setContextMenu({
      show: true,
      x,
      y,
      elementId: null,
      pageId,
      type: 'page',
    });
  };

  // نسخ معرف العنصر
  const copyElementId = async (elementId: string) => {
    const success = await copyToClipboard(elementId);
    if (success) {
      alert(`✅ تم نسخ معرف العنصر:\n${elementId}`);
    } else {
      alert('فشل في نسخ المعرف. يرجى المحاولة مرة أخرى.');
    }
  };

  // نسخ اسم ملف الصفحة (مع رسالة تأكيد)
  const copyPageId = async (pageId: string) => {
    // البحث عن الصفحة للحصول على اسمها
    const page = project.pages.find(p => p.id === pageId);
    const pageName = page ? page.name : pageId;

    // تنسيق اسم الملف: اسم الصفحة + .html
    const fileName = `${pageName}.html`;

    const success = await copyToClipboard(fileName);
    if (success) {
      alert(`✅ تم نسخ اسم ملف الصفحة:\n${fileName}`);
    } else {
      alert('فشل في نسخ اسم الملف. يرجى المحاولة مرة أخرى.');
    }
  };





  // تحريك العنصر في الطبقات - نظام محسن
  const moveElementLayer = (elementId: string, direction: 'up' | 'down') => {
    const currentElements = elements;
    const elementIndex = currentElements.findIndex(el => el.id === elementId);
    if (elementIndex === -1) return;

    const element = currentElements[elementIndex];
    const currentZIndex = element.zIndex || 0;

    // الحصول على جميع z-index المستخدمة
    const allZIndexes = currentElements.map(el => el.zIndex || 0).sort((a, b) => a - b);
    const uniqueZIndexes = [...new Set(allZIndexes)];

    let newZIndex = currentZIndex;

    if (direction === 'up') {
      // البحث عن أول z-index أكبر من الحالي
      const higherIndexes = uniqueZIndexes.filter(z => z > currentZIndex);
      if (higherIndexes.length > 0) {
        newZIndex = higherIndexes[0] + 1;
      } else {
        // إذا لم يوجد، ضعه في المقدمة
        newZIndex = Math.max(...uniqueZIndexes) + 1;
      }
    } else {
      // البحث عن أول z-index أصغر من الحالي
      const lowerIndexes = uniqueZIndexes.filter(z => z < currentZIndex);
      if (lowerIndexes.length > 0) {
        newZIndex = lowerIndexes[lowerIndexes.length - 1] - 1;
      } else {
        // إذا لم يوجد، ضعه في المؤخرة
        newZIndex = Math.min(...uniqueZIndexes) - 1;
      }
    }

    // تحديث العنصر محلياً
    setElements(prev => prev.map(el =>
      el.id === elementId ? { ...el, zIndex: newZIndex } : el
    ));

    // تحديث المشروع
    setProject(prev => ({
      ...prev,
      pages: prev.pages.map(page =>
        page.id === currentPageId
          ? {
              ...page,
              elements: page.elements.map(el =>
                el.id === elementId ? { ...el, zIndex: newZIndex } : el
              )
            }
          : page
      )
    }));

    console.log(`تحريك العنصر ${elementId} ${direction === 'up' ? 'للأمام' : 'للخلف'}: z-index من ${currentZIndex} إلى ${newZIndex}`);
  };

  // إعادة ترتيب العناصر بالكامل (للسحب والإفلات)
  const reorderElements = (newElements: Element[]) => {
    // تحديث العناصر محلياً
    setElements(newElements);

    // تحديث المشروع
    setProject(prev => ({
      ...prev,
      pages: prev.pages.map(page =>
        page.id === currentPageId
          ? { ...page, elements: newElements }
          : page
      )
    }));

    console.log('تم إعادة ترتيب العناصر بنجاح');
  };



  // إخفاء قائمة السياق عند النقر في مكان آخر (مع تأخير لتجنب التداخل)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // تجاهل النقرات داخل قائمة السياق نفسها
      const contextMenuElement = (event.target as HTMLElement).closest('.context-menu');
      if (contextMenuElement) {
        return;
      }

      // إغلاق القائمة مع تأخير بسيط للسماح للأزرار بالعمل
      setTimeout(() => {
        setContextMenu({ show: false, x: 0, y: 0, elementId: null, pageId: null, type: null });
      }, 100);
    };

    if (contextMenu.show) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [contextMenu.show]);



  // إعادة تعيين المشروع بالكامل
  const resetProject = () => {
    const newProject: Project = {
      id: 'project_' + Date.now(),
      name: 'مشروع جديد',
      description: '',
      pages: [
        {
          id: 'page_home',
          name: 'index',
          elements: []
        }
      ],
      canvasSize: { width: 375, height: 667 },
      settings: {
        theme: 'light',
        primaryColor: '#3b82f6',
        secondaryColor: '#10b981',
        fontFamily: 'Arial, sans-serif'
      }
    };

    setProject(newProject);
    setCurrentPageId('page_home');
    setElements([]);
    setSelectedElementId(null);
    setCanvasSize({ width: 375, height: 667 });
    setCustomSize({ width: 375, height: 667 });
    setCurrentDevice('mobile');

    // إعادة تعيين حالات أخرى
    setIsFullscreen(false);
    setShowCustomSizeModal(false);
    setShowFiles(false);
    setGeneratedFiles([]);
    setContextMenu({ show: false, x: 0, y: 0, elementId: null, pageId: null, type: null });
    setCopiedElement(null);

    // إعادة تعيين الزوم إلى 70% عند إنشاء مشروع جديد
    setZoomLevel(0.7);
    // عدم تغيير الموضع - يبقى كما هو
  };

  // تغيير حجم ورقة العمل مع دعم المواضع المتجاوبة
  const changeCanvasSize = (device: string, width: number, height: number) => {
    console.log(`🔄 تغيير الشاشة من ${currentDevice} إلى ${device}`);

    // حفظ ارتفاع الصفحة الحالية للجهاز السابق فقط إذا كان مخصص
    if (currentDevice !== device) {
      setProject(prev => ({
        ...prev,
        pages: prev.pages.map(page => {
          if (page.id === currentPageId) {
            const responsivePageHeights = page.responsivePageHeights || {};
            // فقط حفظ إذا كان الارتفاع الحالي مختلف عن الافتراضي للجهاز السابق
            const currentDeviceDefaultHeight = (() => {
              if (currentDevice === 'mobile') return 667;
              if (currentDevice === 'tablet') return 1024;
              if (currentDevice === 'desktop') return 720;
              return 667;
            })();

            if (pageHeight !== currentDeviceDefaultHeight) {
              const updatedResponsiveHeights = {
                ...responsivePageHeights,
                [currentDevice]: {
                  height: pageHeight,
                  customized: true
                }
              };
              return {
                ...page,
                responsivePageHeights: updatedResponsiveHeights
              };
            }
          }
          return page;
        })
      }));
    }

    setCanvasSize({ width, height });
    setCurrentDevice(device);
    setCustomSize({ width, height }); // تحديث الحجم المخصص أيضاً

    console.log(`🔄 تحديث canvasSize إلى: ${width}×${height}`);

    // تحديث ارتفاع جميع الصفحات للجهاز الجديد مع دعم الارتفاعات المتجاوبة
    setProject(prev => ({
      ...prev,
      pages: prev.pages.map(page => {
        // إنشاء responsivePageHeights إذا لم تكن موجودة
        const responsivePageHeights = page.responsivePageHeights || {};

        // الحصول على الارتفاع المحفوظ للجهاز الجديد
        const savedHeightForDevice = responsivePageHeights[device];

        if (savedHeightForDevice) {
          // إذا كان هناك ارتفاع محفوظ لهذا الجهاز، استخدمه
          return {
            ...page,
            pageHeight: savedHeightForDevice.height,
            responsivePageHeights
          };
        } else {
          // إذا لم يكن هناك ارتفاع محفوظ لهذا الجهاز، استخدم ارتفاع الجهاز الجديد فقط
          const newHeight = height; // استخدام ارتفاع الجهاز الجديد بدلاً من الارتفاع السابق

          // حفظ الارتفاع الجديد للجهاز الجديد
          const updatedResponsiveHeights = {
            ...responsivePageHeights,
            [device]: {
              height: newHeight,
              customized: false
            }
          };

          return {
            ...page,
            pageHeight: newHeight,
            responsivePageHeights: updatedResponsiveHeights
          };
        }
      })
    }));

    console.log(`📱 تم تحديث جميع الصفحات (${project.pages.length}) للجهاز ${device}`);

    // تحديث ارتفاع الصفحة الحالية للجهاز الجديد فقط
    const currentPage = project.pages.find(page => page.id === currentPageId);
    const savedHeightForNewDevice = currentPage?.responsivePageHeights?.[device];

    if (savedHeightForNewDevice) {
      // استخدام الارتفاع المحفوظ للجهاز الجديد
      setPageHeight(savedHeightForNewDevice.height);
      setTempPageHeight(savedHeightForNewDevice.height);
    } else {
      // استخدام ارتفاع الجهاز الجديد الافتراضي
      setPageHeight(height);
      setTempPageHeight(height);
    }

    // إجبار إعادة رسم جميع الصفحات
    setForceRender(prev => prev + 1);

    setTimeout(() => {
      // تحديث مؤجل لضمان تطبيق التغييرات على جميع الصفحات
      setProject(prev => ({
        ...prev,
        pages: prev.pages.map(page => ({ ...page }))
      }));
      setForceRender(prev => prev + 1);
    }, 100);

    // تطبيق الحجم مباشرة على العنصر
    if (canvasRef.current) {
      const canvas = canvasRef.current;
      canvas.style.setProperty('width', `${width}px`, 'important');
      canvas.style.setProperty('height', `${pageHeight}px`, 'important');
      canvas.style.setProperty('min-width', `${width}px`, 'important');
      canvas.style.setProperty('min-height', `${pageHeight}px`, 'important');
      canvas.style.setProperty('max-width', `${width}px`, 'important');
    }

    // تحديث العناصر لإظهار المواضع والأحجام المناسبة للشاشة الجديدة
    // إعادة رسم فورية لضمان عرض المواضع والأحجام الصحيحة
    setElements(prev => prev.map(el => {
      // إنشاء أحجام افتراضية للجهاز الجديد إذا لم تكن موجودة
      if (!el.responsiveSizes || !el.responsiveSizes[device]) {
        const newResponsiveSizes = {
          ...el.responsiveSizes,
          [device]: {
            width: el.width,
            height: el.height,
            customized: false
          }
        };
        return { ...el, responsiveSizes: newResponsiveSizes };
      }
      return { ...el };
    }));

    // تحديث مؤجل للتأكد من التطبيق
    setTimeout(() => {
      setElements(prev => prev.map(el => ({ ...el })));

      // تم إزالة إعادة التوسيط بعد تغيير الجهاز لتجنب مشاكل الزوم
      // const centerPos = calculateCenterPosition();
      // setCanvasPosition(centerPos);
    }, 150);
  };



  // تطبيق الحجم على العنصر عند التغيير
  useEffect(() => {
    if (pageCanvasRef.current) {
      const canvas = pageCanvasRef.current;

      canvas.style.setProperty('width', `${canvasSize.width}px`, 'important');
      canvas.style.setProperty('height', `${pageHeight}px`, 'important');
      canvas.style.setProperty('min-width', `${canvasSize.width}px`, 'important');
      canvas.style.setProperty('min-height', `${pageHeight}px`, 'important');
      canvas.style.setProperty('max-width', `${canvasSize.width}px`, 'important');
      // إزالة max-height للسماح بالصفحات الطويلة
    }
  }, [canvasSize, pageHeight]);

  // تحديث customSize عند تغيير canvasSize من الأزرار
  useEffect(() => {
    if (currentDevice !== 'custom') {
      setCustomSize({ width: canvasSize.width, height: canvasSize.height });
    }
  }, [canvasSize, currentDevice]);

  // تم إزالة إعادة التوسيط عند تغيير حجم الكانفاس لتجنب مشاكل الزوم
  // useEffect(() => {
  //   const timer = setTimeout(() => {
  //     const centerPos = calculateCenterPosition();
  //     setCanvasPosition(centerPos);
  //   }, 200);
  //   return () => clearTimeout(timer);
  // }, [canvasSize, calculateCenterPosition]);

  // التعامل مع سحب تغيير حجم الصفحة
  useEffect(() => {
    if (isResizingPage) {
      // تغيير مؤشر الماوس أثناء السحب
      document.body.style.cursor = 'ns-resize';

      document.addEventListener('mousemove', handlePageResizeMove);
      document.addEventListener('mouseup', handlePageResizeEnd);

      return () => {
        document.body.style.cursor = 'auto';
        document.removeEventListener('mousemove', handlePageResizeMove);
        document.removeEventListener('mouseup', handlePageResizeEnd);
      };
    }
  }, [isResizingPage, pageResizeStart]);

  // تحديث ارتفاع الصفحة فقط عند تحميل صفحة جديدة (بدون توسع تلقائي)
  useEffect(() => {
    const currentPage = project.pages.find(page => page.id === currentPageId);
    const savedPageHeight = currentPage?.pageHeight;

    // تحديث الارتفاع فقط إذا كان هناك ارتفاع محفوظ ومختلف عن الحالي
    if (savedPageHeight && savedPageHeight !== pageHeight) {
      setPageHeight(savedPageHeight);
    }
  }, [currentPageId]); // إزالة canvasSize من التبعيات

  // أحجام الشاشات المبسطة (3 فقط)
  const deviceSizes = [
    { name: 'موبايل', key: 'mobile', width: 375, height: 667, icon: '📱' },
    { name: 'تابلت', key: 'tablet', width: 768, height: 1024, icon: '📟' },
    { name: 'كمبيوتر', key: 'desktop', width: 1280, height: 720, icon: '🖥️' },
  ];



  // دالة للحصول على الارتفاع المناسب للصفحة حسب الجهاز الحالي
  const getPageHeight = (page: Page) => {
    if (page.responsivePageHeights && page.responsivePageHeights[currentDevice]) {
      return page.responsivePageHeights[currentDevice].height;
    }
    return page.pageHeight || canvasSize.height;
  };

  // دالة للحصول على التخطيط المتجاوب للحاوي
  const getResponsiveLayout = (element: Element, device: string = currentDevice) => {
    if (element.type !== 'container') {
      return {
        layoutType: 'relative' as const,
        justifyContent: 'flex-start' as const,
        alignItems: 'flex-start' as const,
        gap: 0
      };
    }

    // إذا كان هناك تخطيط متجاوب للجهاز الحالي
    if (element.properties.responsiveLayout?.[device]) {
      return element.properties.responsiveLayout[device];
    }

    // العودة للتخطيط الافتراضي
    return {
      layoutType: element.properties.layoutType || 'flex-row' as const,
      justifyContent: element.properties.justifyContent || 'flex-start' as const,
      alignItems: element.properties.alignItems || 'stretch' as const, // توحيد مع المحرر والتصدير
      gap: element.properties.gap || 8,
      gridColumns: element.properties.gridColumns || 2,
      gridRows: element.properties.gridRows || 2,
      justifyItems: element.properties.justifyItems || 'stretch' as const,
      alignContent: element.properties.alignContent || 'start' as const
    };
  };



  // إضافة الحجم المخصص فقط للعرض
  const allDeviceSizes = [
    ...deviceSizes,
    { name: 'حجم مخصص', key: 'custom', width: canvasSize.width, height: canvasSize.height, icon: '🎯' },
  ];

  // تحليل التصميم بالذكاء الاصطناعي - إرسال البيانات الكاملة
  const analyzeDesign = async () => {
    // التحقق من وجود عناصر في أي صفحة
    const totalElements = project.pages.reduce((total, page) => total + (page.elements?.length || 0), 0);

    if (totalElements === 0) {
      alert('⚠️ لا توجد عناصر للتحليل في أي صفحة\n\nيرجى إضافة عناصر للتصميم أولاً');
      return;
    }

    setIsAnalyzing(true);
    try {
      // فلترة العناصر لإرسال العناصر الجديدة فقط التي لها برومبت
      const filteredPages = project.pages.map(page => ({
        ...page,
        elements: page.elements?.filter(element =>
          // يجب أن يكون للعنصر برومبت
          element.properties.prompt &&
          element.properties.prompt.trim() !== '' &&
          // ولم يتم توليد كود له مسبقاً
          !element.properties.generatedCode
        ) || []
      }));

      // حساب العناصر غير المعالجة فقط
      const unprocessedElements = filteredPages.reduce((total, page) => total + (page.elements?.length || 0), 0);

      if (unprocessedElements === 0) {
        alert('⚠️ لا توجد عناصر جديدة للتحليل!\n\nالأسباب المحتملة:\n• لا توجد عناصر لها برومبت\n• جميع العناصر تم معالجتها مسبقاً\n• جميع العناصر تم توليد كود لها\n\nأضف برومبت لعناصر جديدة أو استخدم زر "🔄" لنسيان التوليد السابق.');
        setIsAnalyzing(false);
        return;
      }

      // إرسال المشروع مع العناصر غير المعالجة فقط للذكاء الاصطناعي
      const projectData = {
        name: project.name,
        description: project.description,
        pages: filteredPages,
        canvasSize,
        settings: {
          theme: "light",
          primaryColor: "#3b82f6",
          secondaryColor: "#10b981",
          fontFamily: "Arial, sans-serif"
        },
        exportDate: new Date().toISOString(),
        version: "2.0",
        totalElements: unprocessedElements,
        pagesCount: filteredPages.length,
        processedElementsCount: totalElements - unprocessedElements
      };

      const response = await fetch('/api/analyze-design', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(projectData),
      });

      const data = await response.json();

      if (data.success) {
        setDesignAnalysis(data.analysis);
        setShowAnalysis(true);
        const processedCount = totalElements - unprocessedElements;
        let message = `✅ تم تحليل التصميم بنجاح! 🎉\n\n📊 تم تحليل ${project.pages.length} صفحة\n🧩 عناصر جديدة تم تحليلها: ${unprocessedElements}`;

        if (processedCount > 0) {
          message += `\n🔒 عناصر تم تجاهلها: ${processedCount}`;
          message += `\n   • عناصر بدون برومبت`;
          message += `\n   • عناصر تم توليد كود لها مسبقاً`;
          message += `\n   • عناصر تم تطبيق كود عليها`;
        }

        message += `\n\nيمكنك الآن رؤية التحليل والمتابعة لتوليد المشروع.`;
        alert(message);
      } else {
        alert('حدث خطأ في تحليل التصميم: ' + data.error);
      }
    } catch (error) {
      console.error('خطأ في تحليل التصميم:', error);
      alert('حدث خطأ في الاتصال بالخادم');
    } finally {
      setIsAnalyzing(false);
    }
  };



  // تحميل ملفات المشروع
  const downloadProjectFiles = (files: any[], projectName: string) => {
    files.forEach(file => {
      const blob = new Blob([file.content], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = file.path;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    });
  };

  // تم إزالة وظيفة generateCode - المشروع كله يتم تمريره للذكاء الاصطناعي عند الضغط على "تحويل AI"

  // العنصر المحدد
  const selectedElement = elements.find(el => el.id === selectedElementId) || null;

  return (
    <>
      {/* CSS مخصص لضمان ظهور شريط التمرير */}
      <style jsx>{`
        .scroll-area::-webkit-scrollbar {
          width: 6px;
          height: 6px;
        }
        .scroll-area::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }
        .scroll-area::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;
        }
        .scroll-area::-webkit-scrollbar-thumb:hover {
          background: #a8a8a8;
        }

        /* شريط التمرير للمحرر الرئيسي - أقرب للحافة */
        .editor-scroll::-webkit-scrollbar {
          width: 8px;
          height: 8px;
        }
        .editor-scroll::-webkit-scrollbar-track {
          background: rgba(0,0,0,0.05);
          border-radius: 4px;
        }
        .editor-scroll::-webkit-scrollbar-thumb {
          background: rgba(0,0,0,0.2);
          border-radius: 4px;
        }
        .editor-scroll::-webkit-scrollbar-thumb:hover {
          background: rgba(0,0,0,0.3);
        }
        .editor-scroll::-webkit-scrollbar-corner {
          background: transparent;
        }

        /* إزالة أي مسافات إضافية حول شريط التمرير */
        .editor-scroll {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        /* تحسين موضع شريط التمرير - يلتصق بالحافة */
        .editor-scroll::-webkit-scrollbar {
          background: transparent;
          border: none;
          margin: 0;
          padding: 0;
        }

        /* إزاحة شريط التمرير للداخل (لليسار) */
        .editor-scroll {
          scrollbar-gutter: stable;
          /* توسيع العرض وإزاحة لليسار لدفع شريط التمرير للداخل */
          width: calc(100% + 30px);
          margin-left: -30px;
          padding-left: 30px;
          box-sizing: border-box;
        }

        /* CSS مخصص للصفحة الحالية - معطل في المحرر */
        /* ${project.pages.find(p => p.id === currentPageId)?.properties?.customCSS || ''} */
        .scroll-area {
          scrollbar-width: auto !important;
          scrollbar-color: #c1c1c1 #f1f1f1 !important;
        }
      `}</style>

      <div className="h-screen w-screen flex flex-col overflow-hidden" dir="rtl">
      {/* شريط الأدوات العلوي */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 shadow-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <h1 className="text-xl font-bold">🎨 AI Website Builder</h1>
            <div className="flex items-center gap-2 text-sm">
              <input
                type="text"
                value={project.name}
                onChange={(e) => setProject(prev => ({ ...prev, name: e.target.value }))}
                className="bg-white/20 text-white placeholder-white/70 px-3 py-1 rounded border-none outline-none w-48"
                placeholder="اسم المشروع"
              />
              <input
                type="text"
                value={project.description}
                onChange={(e) => setProject(prev => ({ ...prev, description: e.target.value }))}
                className="bg-white/20 text-white placeholder-white/70 px-3 py-1 rounded border-none outline-none w-64"
                placeholder="وصف المشروع (اختياري)"
              />
            </div>
          </div>

          <div className="flex items-center gap-3">
            {/* أزرار إدارة المشروع */}
            <div className="flex items-center gap-2 border-r border-white/20 pr-3">
              <button
                onClick={() => {
                  if (confirm('⚠️ هل تريد إنشاء مشروع جديد؟\n\nسيتم فقدان جميع التغييرات غير المحفوظة.')) {
                    resetProject();
                  }
                }}
                className="px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded font-medium transition-colors flex items-center gap-2"
                title="مشروع جديد"
              >
                <Icons.FilePlus size={16} />
                <span className="text-sm">جديد</span>
              </button>
              <button
                onClick={importProject}
                className="px-3 py-2 bg-green-500 hover:bg-green-600 text-white rounded font-medium transition-colors flex items-center gap-2"
                title="استيراد مشروع"
              >
                <Icons.Upload size={16} />
                <span className="text-sm">استيراد</span>
              </button>
              <button
                onClick={exportProject}
                className="px-3 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded font-medium transition-colors flex items-center gap-2"
                title="تصدير المشروع"
              >
                <Icons.Download size={16} />
                <span className="text-sm">تصدير</span>
              </button>

              {/* أزرار المعاينة المباشرة */}
              <div className="flex gap-1">
                <button
                  onClick={() => openLivePreview('current-page')}
                  className="px-3 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded font-medium transition-colors flex items-center gap-2"
                  title="معاينة الصفحة الحالية"
                >
                  <Icons.Eye size={16} />
                  <span className="text-sm">معاينة الصفحة</span>
                </button>
                <button
                  onClick={() => openLivePreview('full-project')}
                  className="px-3 py-2 bg-indigo-500 hover:bg-indigo-600 text-white rounded font-medium transition-colors flex items-center gap-2"
                  title="معاينة المشروع الكامل"
                >
                  <Icons.Eye size={16} />
                  <span className="text-sm">معاينة المشروع</span>
                </button>
              </div>
            </div>

            {/* زر ملء الشاشة */}
            <button
              onClick={toggleFullscreen}
              className={`px-3 py-2 rounded font-medium transition-colors ${
                isFullscreen ? 'bg-white/20' : 'bg-white/10'
              } hover:bg-white/30`}
              title={isFullscreen ? "الخروج من ملء الشاشة" : "ملء الشاشة"}
            >
              {isFullscreen ? '🔲' : '⛶'}
            </button>

            <button
              onClick={analyzeDesign}
              disabled={isAnalyzing}
              className={`px-4 py-2 rounded font-medium transition-colors ${
                isAnalyzing
                  ? 'bg-gray-400 cursor-not-allowed'
                  : designAnalysis
                  ? 'bg-green-500 hover:bg-green-600'
                  : 'bg-blue-500 hover:bg-blue-600'
              }`}
            >
              <div className="flex items-center gap-2">
                {isAnalyzing ? (
                  <>
                    <Icons.Magic size={16} className="animate-spin" />
                    <span>جاري التحليل...</span>
                  </>
                ) : designAnalysis ? (
                  <>
                    <Icons.Magic size={16} />
                    <span>تم التحليل</span>
                  </>
                ) : (
                  <>
                    <Icons.Bot size={16} />
                    <span>استخدام AI</span>
                  </>
                )}
              </div>
            </button>
            <button
              onClick={previewAndDownloadProject}
              disabled={isGenerating}
              className={`px-4 py-2 rounded font-medium transition-colors text-white ${
                isGenerating
                  ? 'bg-gray-400 cursor-not-allowed'
                  : 'bg-green-500 hover:bg-green-600'
              }`}
              title="معاينة وتنزيل المشروع بدون AI"
            >
              <div className="flex items-center gap-2">
                <Icons.Eye size={16} />
                <span>معاينة وتنزيل</span>
              </div>
            </button>




          </div>
        </div>
      </div>



      <div className={`flex-1 ${isFullscreen ? 'fixed inset-0 z-50 bg-white' : 'flex'}`} style={{ minHeight: '100vh' }}>
        {/* إخفاء الشريط العلوي في ملء الشاشة */}
        {isFullscreen && (
          <div className="absolute top-4 right-4 z-50">
            <button
              onClick={toggleFullscreen}
              className="bg-black/50 text-white px-3 py-2 rounded-lg hover:bg-black/70 transition-colors"
              title="الخروج من ملء الشاشة"
            >
              ✕ خروج
            </button>
          </div>
        )}

        {/* لوحة العناصر والطبقات - مخفية في ملء الشاشة */}
        {!isFullscreen && showLeftPanel && (
          <div className="flex">
            <LayersPanel
              elements={elements}
              selectedElementId={selectedElementId}
              onSelectElement={setSelectedElementId}
              onToggleVisibility={toggleElementVisibility}
              onToggleLock={toggleElementLock}
              onMoveElement={moveElementToParent}
              onDeleteElement={deleteElement}
              onMoveLayer={moveElementLayer}
              onReorderElements={reorderElements}
              currentPageId={currentPageId}
              isCollapsed={isLayersPanelCollapsed}
              setIsCollapsed={setIsLayersPanelCollapsed}
            />
            <ElementPanel
              onAddElement={addElement}
              isCollapsed={isElementPanelCollapsed}
              setIsCollapsed={setIsElementPanelCollapsed}
            />
          </div>
        )}



        {/* منطقة التصميم */}
        <div
          className={`${isFullscreen ? 'w-full h-full' : 'flex-1'} bg-gradient-to-br from-gray-50 to-gray-100 relative overflow-hidden ${isFullscreen ? 'p-4' : 'p-8'}`}
          style={{ minHeight: isFullscreen ? '100vh' : 'calc(100vh - 80px)' }}
        >






          {/* خلفية شبكية للمساعدة في التصميم */}
          <div
            className="absolute inset-0 opacity-30 canvas-background"
            style={{
              backgroundImage: 'linear-gradient(rgba(0,0,0,0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0,0,0,0.1) 1px, transparent 1px)',
              backgroundSize: '20px 20px',
              cursor: isCtrlPressed ? (isPanning ? 'grabbing' : 'grab') : 'default',
            }}
            onMouseDown={handleCanvasMouseDown}
            onContextMenu={handleCanvasContextMenu}
            onDragOver={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            onDrop={(e) => {
              e.preventDefault();
              e.stopPropagation();

              // التحقق من وجود عنصر مسحوب
              const draggedElementId = e.dataTransfer.getData('text/plain');
              if (draggedElementId) {
                // إزالة العنصر من أي حاوي وجعله عنصر جذر
                moveElementToParent(draggedElementId, null, 0);
              }
            }}
          ></div>

          {/* منطقة تمرير Canvas - عرض جميع الصفحات أفقياً */}
          <div
            ref={scrollAreaRef}
            className="relative w-full overflow-auto editor-scroll"
            style={{
              height: 'calc(100vh - 80px)', // ارتفاع مناسب مع مساحة لشريط الأدوات
              cursor: isCtrlPressed ? (isScrollPanning ? 'grabbing' : 'grab') : 'auto',
              scrollBehavior: isScrollPanning ? 'auto' : 'smooth',
              // ضمان ظهور شريط التمرير
              scrollbarWidth: 'auto', // Firefox
              msOverflowStyle: 'auto', // IE/Edge
            }}
            onMouseDown={handleScrollAreaMouseDown}
            onContextMenu={handleCanvasContextMenu}
          >
            {/* حاوي الزوم للمشروع بالكامل مع مساحة متوازنة */}
            <div
              ref={canvasRef}
              className="relative"
              style={{
                // مساحة متوازنة تضمن ظهور شريط التمرير الأفقي والعمودي
                width: `${Math.max(
                  (scrollAreaRef.current?.clientWidth || 1200) * 3,
                  (canvasSize.width + 400) * project.pages.length + 2000
                )}px`,
                minHeight: `${Math.max(
                  (scrollAreaRef.current?.clientHeight || 800) * 2,
                  Math.max(
                    ...project.pages.map(page => getPageHeight(page)),
                    isResizingPage ? tempPageHeight : 0
                  ) + 1200
                )}px`,
                transform: `translate(${canvasPosition.x}px, ${canvasPosition.y}px) scale(${zoomLevel})`,
                transformOrigin: 'center center',
                transition: isPanning ? 'none' : 'transform 0.1s ease-out',
              }}
            >
              {/* حاوي الصفحات الأفقي مع مساحة متوازنة للتنقل */}
              <div
                className="flex items-start justify-center gap-8"
                style={{
                  // عرض يتناسب مع المحتوى + مساحة إضافية كبيرة لضمان التمرير الأفقي
                  width: `${Math.max((canvasSize.width + 100) * project.pages.length + 800, 2000)}px`,
                  minHeight: '100vh',
                  // مساحة متوازنة للتنقل مع ضمان وجود تمرير أفقي
                  padding: '50vh 50vw', // أعلى/أسفل + يمين/يسار كبير لضمان التمرير
                  minWidth: 'max-content',
                }}
                onMouseDown={(e) => {
                  // التحريك يعمل على الخلفية أيضاً
                  if (isCtrlPressed && e.target === e.currentTarget) {
                    handleScrollAreaMouseDown(e);
                  }
                }}
              >
              {project.pages.map((page, index) => (
                <div key={`${page.id}-${canvasSize.width}-${canvasSize.height}-${forceRender}`} className="flex items-start gap-4">
                  {/* زر + قبل كل صفحة في ارتفاع مقبول للعين */}
                  <div className="flex flex-col items-center" style={{ paddingTop: '120px' }}>
                    <button
                      onClick={() => {
                        const name = prompt('اسم الصفحة الجديدة:');
                        if (name && name.trim()) {
                          addNewPage(name.trim());
                        }
                      }}
                      className="w-12 h-12 bg-blue-500 hover:bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105 flex-shrink-0"
                      title="إضافة صفحة جديدة"
                    >
                      +
                    </button>
                  </div>

                  {/* Canvas الصفحة */}
                  <div className="flex-shrink-0 relative">
                    {/* اسم الصفحة فوق الكانفاس */}
                    <div className="absolute -top-8 left-0 z-10">
                      <div
                        className={`px-3 py-1 rounded-t-lg text-sm font-medium shadow-md cursor-pointer ${
                          currentPageId === page.id
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-400 text-white'
                        }`}
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          // تبديل الصفحة الحالية وإظهار LayersPanel الجديد
                          setCurrentPageId(page.id);
                          setShowLeftPanel(true);
                        }}
                        onContextMenu={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handlePageContextMenu(page.id, e.clientX, e.clientY);
                        }}
                        title={`انقر للانتقال إلى الصفحة وعرض طبقاتها: ${page.name} (${page.elements?.length || 0} عنصر)`}
                      >
                        <div className="flex items-center gap-1">
                          <Icons.FileText size={14} className="text-white" />
                          <span>{page.name} ({page.elements?.length || 0})</span>
                        </div>
                      </div>
                    </div>

                    {/* زر حذف الصفحة */}
                    {project.pages.length > 1 && (
                      <button
                        onClick={() => deletePage(page.id)}
                        className="absolute -top-2 -right-2 z-20 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center text-xs transition-colors"
                        title="حذف الصفحة"
                      >
                        ×
                      </button>
                    )}

                    {/* Canvas الفعلي */}
                    <div
                      ref={currentPageId === page.id ? pageCanvasRef : null}
                      onClick={() => switchToPage(page.id)}
                      className={`relative bg-white shadow-2xl border-4 cursor-pointer transition-all duration-200 overflow-visible ${
                        currentPageId === page.id
                          ? 'border-blue-500 shadow-blue-200'
                          : 'border-gray-300 hover:border-gray-400'
                      }`}
                      style={{
                        width: `${canvasSize.width}px`,
                        height: `${currentPageId === page.id ? (isResizingPage ? tempPageHeight : pageHeight) : getPageHeight(page)}px`,
                        backgroundColor: page.properties?.canvasBackgroundColor || '#ffffff',
                        backgroundImage: page.properties?.backgroundImage ? `url(${page.properties.backgroundImage})` : undefined,
                        backgroundSize: page.properties?.backgroundSize || 'cover',
                        backgroundPosition: page.properties?.backgroundPosition || 'center',
                        backgroundRepeat: page.properties?.backgroundRepeat || 'no-repeat',
                        color: page.properties?.textColor || '#000000',
                      }}
                      onMouseDown={(e) => {
                        // تحسين السلاسة: معالجة سريعة للأحداث
                        if (isCtrlPressed) {
                          // Ctrl + click = تحريك المنطقة الرئيسية
                          e.preventDefault();
                          handleScrollAreaMouseDown(e);
                        } else if (currentPageId === page.id) {
                          // click عادي = تحريك الكانفاس الفردي (إذا كان نشط)
                          // لا نمنع الحدث هنا للسماح للعناصر بالعمل
                          if (e.target === e.currentTarget) {
                            handleCanvasMouseDown(e);
                          }
                        } else {
                          // click على صفحة غير نشطة = تفعيلها
                          e.preventDefault();
                          switchToPage(page.id);
                        }
                      }}
                      onContextMenu={currentPageId === page.id ? handleCanvasContextMenu : undefined}
                    >
                      {/* الشبكة */}
                      {showGrid && currentPageId === page.id && (
                        <div
                          className="absolute pointer-events-none opacity-20"
                          style={{
                            top: `${gridSize}px`,
                            left: `${gridSize}px`,
                            right: `${gridSize}px`,
                            bottom: `${gridSize}px`,
                            backgroundImage: `
                              linear-gradient(to right, #ddd 1px, transparent 1px),
                              linear-gradient(to bottom, #ddd 1px, transparent 1px)
                            `,
                            backgroundSize: `${gridSize}px ${gridSize}px`
                          }}
                        />
                      )}

                      {/* خط مرجعي لحدود الشاشة الأصلية */}
                      {currentPageId === page.id && pageHeight > canvasSize.height && (
                        <div
                          className="absolute left-0 right-0 pointer-events-none border-t-2 border-dashed border-blue-400 opacity-50"
                          style={{
                            top: `${canvasSize.height}px`
                          }}
                        >
                          <div className="absolute -top-6 left-2 bg-blue-400 text-white px-2 py-1 rounded text-xs">
                            حدود الشاشة ({canvasSize.width}×{canvasSize.height})
                          </div>
                        </div>
                      )}

                      {/* العناصر */}
                      {currentPageId === page.id ? (
                        // الصفحة النشطة: عناصر قابلة للتحرير (فقط العناصر الجذر مرتبة حسب z-index)
                        (page.elements || [])
                          .filter(element => !element.parentId)
                          .sort((a, b) => (a.zIndex || 0) - (b.zIndex || 0)) // ترتيب من الأسفل للأعلى في الرسم
                          .map(element => (
                          <DraggableElement
                            key={element.id}
                            element={element}
                            elements={page.elements || []}
                            onSelect={selectElement}
                            isSelected={selectedElementId === element.id}
                            onUpdatePosition={updateElementPosition}
                            onUpdateSize={updateElementSize}
                            onUpdateRotation={updateElementRotation}
                            onDoubleClick={isFullscreen ? openFloatingProperties : undefined}
                            onContextMenu={handleElementContextMenu}
                            onMoveToParent={moveElementToParent}
                            canvasSize={canvasSize}
                            currentDevice={currentDevice}
                            currentPage={page}
                          />
                        ))
                      ) : (
                        // الصفحة غير النشطة: عناصر للعرض مع تفاعل محدود (مرتبة حسب z-index)
                        (page.elements || [])
                          .filter(element => !element.parentId)
                          .sort((a, b) => (a.zIndex || 0) - (b.zIndex || 0)) // ترتيب من الأسفل للأعلى في الرسم
                          .slice(0, 10)
                          .map(element => (
                          <div
                            key={element.id}
                            className="absolute opacity-60 cursor-pointer hover:opacity-80 transition-opacity"
                            style={{
                              left: `${element.x}px`,
                              top: `${element.y}px`,
                              width: `${calculateActualSize(element, canvasSize.width, pageHeight).width}px`,
                              height: `${calculateActualSize(element, canvasSize.width, pageHeight).height}px`,
                              transform: element.rotation ? `rotate(${element.rotation}deg)` : undefined,
                              zIndex: element.zIndex || 0,
                            }}
                            onContextMenu={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              // تفعيل الصفحة وإظهار قائمة السياق فوراً
                              if (currentPageId !== page.id) {
                                switchToPage(page.id);
                              }
                              // إظهار قائمة السياق مباشرة
                              handleElementContextMenu(element.id, e.clientX, e.clientY);
                            }}
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              if (currentPageId !== page.id) {
                                switchToPage(page.id);
                              }
                            }}
                          >
                            {renderElementPreview(element)}
                          </div>
                        ))
                      )}

                      {/* مقبض سحب تغيير حجم الصفحة - فقط للصفحة النشطة */}
                      {currentPageId === page.id && (
                        <div
                          className="absolute left-0 right-0 h-4 cursor-ns-resize bg-transparent hover:bg-blue-100 hover:bg-opacity-70 transition-all duration-200 group border-t-2 border-dashed border-blue-300 hover:border-blue-500"
                          style={{
                            top: `${currentPageId === page.id ? pageHeight : getPageHeight(page)}px`
                          }}
                          onMouseDown={handlePageResizeStart}
                          title="اسحب لتغيير ارتفاع الصفحة"
                        >
                          {/* مؤشر مرئي للمقبض */}
                          <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium opacity-70 group-hover:opacity-100 transition-opacity shadow-md">
                            ⇅ اسحب لتغيير الارتفاع
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}

              {/* زر + في النهاية بنفس الارتفاع */}
              <div className="flex flex-col items-center" style={{ paddingTop: '120px' }}>
                <button
                  onClick={() => {
                    const name = prompt('اسم الصفحة الجديدة:');
                    if (name && name.trim()) {
                      addNewPage(name.trim());
                    }
                  }}
                  className="w-12 h-12 bg-blue-500 hover:bg-blue-600 text-white rounded-full flex items-center justify-center text-xl font-bold transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105 flex-shrink-0"
                  title="إضافة صفحة جديدة"
                >
                  +
                </button>
              </div>
              </div>
            </div>
          </div>

          {/* شريط معلومات ورقة العمل وأدوات الزوم - خارج المحرر */}
          <div className="absolute top-4 left-4 z-40">
            <div className="flex items-start gap-2">
              {/* أدوات التحكم في الزوم */}
              <div className="flex items-center gap-1 bg-gray-300 text-gray-700 px-2 py-1 rounded shadow">
                <Icons.Search size={12} />
                <span className="text-xs font-medium min-w-[35px]">
                  {Math.round(zoomLevel * 100)}%
                </span>
                <button
                  onClick={() => {
                    const newZoomLevel = Math.max(0.1, zoomLevel - 0.1);
                    setZoomLevel(newZoomLevel);
                  }}
                  className="w-4 h-4 bg-gray-400 hover:bg-gray-500 text-gray-700 rounded text-xs flex items-center justify-center transition-colors"
                  title="تصغير (Alt + دولاب الماوس)"
                >
                  <Icons.ZoomOut size={10} />
                </button>
                <button
                  onClick={() => {
                    setZoomLevel(1);
                    // عدم تغيير الموضع - يبقى كما هو
                  }}
                  className="px-1 h-4 bg-gray-400 hover:bg-gray-500 text-gray-700 rounded text-xs flex items-center justify-center transition-colors"
                  title="إعادة تعيين الزوم إلى 100%"
                >
                  100%
                </button>
                <button
                  onClick={() => {
                    const newZoomLevel = Math.min(5, zoomLevel + 0.1);
                    setZoomLevel(newZoomLevel);
                  }}
                  className="w-4 h-4 bg-gray-400 hover:bg-gray-500 text-gray-700 rounded text-xs flex items-center justify-center transition-colors"
                  title="تكبير (Alt + دولاب الماوس)"
                >
                  <Icons.ZoomIn size={10} />
                </button>
              </div>

              {/* زر معلومات ورقة العمل */}
              <div className="relative">
                <button
                  onClick={() => setShowCanvasInfo(!showCanvasInfo)}
                  className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-2 py-1 rounded shadow transition-colors flex items-center gap-1"
                  title="عرض معلومات ورقة العمل"
                >
                  <Icons.Info size={12} />
                  <span className="text-xs font-medium">معلومات</span>
                </button>

                {/* النافذة المنبثقة */}
                {showCanvasInfo && (
                  <div className="absolute top-full left-0 mt-1 bg-white/95 backdrop-blur-sm rounded shadow-lg border border-blue-200 z-50 min-w-[280px]">
                    {/* شريط العنوان مع زر الإغلاق */}
                    <div className="flex items-center justify-between px-3 py-1 border-b border-gray-200 bg-blue-50 rounded-t">
                      <div className="flex items-center gap-1">
                        <Icons.Info size={14} className="text-blue-600" />
                        <span className="text-xs font-medium text-gray-800">معلومات</span>
                      </div>
                      <button
                        onClick={() => setShowCanvasInfo(false)}
                        className="text-gray-500 hover:text-gray-700 hover:bg-gray-200 p-0.5 rounded transition-colors"
                        title="إغلاق"
                      >
                        <div style={{ transform: 'rotate(45deg)' }}>
                          <Icons.Plus size={12} />
                        </div>
                      </button>
                    </div>

                    {/* محتوى المعلومات */}
                    <div className="px-3 py-2 text-xs text-gray-700">
                      {/* معلومات أساسية */}
                      <div className="grid grid-cols-2 gap-2 mb-3">
                        <div className="space-y-1">
                          <div className="flex items-center gap-1">
                            <Icons.Plus size={12} className="text-blue-600" />
                            <span>العناصر: {elements.length}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Icons.ZoomIn size={12} className="text-green-600" />
                            <span>{canvasSize.width}×{canvasSize.height}</span>
                          </div>
                        </div>

                        <div className="space-y-1">
                          <div className="flex items-center gap-1">
                            <Icons.Smartphone size={12} className="text-purple-600" />
                            <span>{allDeviceSizes.find(d => d.key === currentDevice)?.name || 'مخصص'}</span>
                          </div>
                          <div className="text-blue-600 text-xs opacity-75 flex items-center gap-1">
                            <Icons.Hand size={10} />
                            <span>اسحب الزاوية لتوسيع</span>
                          </div>
                        </div>
                      </div>

                      {/* أزرار التحكم */}
                      <div className="border-t border-gray-200 pt-2 space-y-2">
                        <div className="text-xs font-medium text-gray-600 mb-2 flex items-center gap-1">
                          <Icons.Settings size={12} />
                          <span>إعدادات التصميم</span>
                        </div>

                        {/* زر الشبكة */}
                        <button
                          onClick={() => setShowGrid(!showGrid)}
                          className={`w-full p-2 text-right rounded border transition-colors ${
                            showGrid
                              ? 'bg-blue-50 border-blue-300 text-blue-700'
                              : 'bg-white border-gray-200 hover:bg-gray-50'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <span className="text-xs font-medium">{showGrid ? 'مفعل' : 'معطل'}</span>
                            <div className="flex items-center gap-1">
                              <span className="text-xs">عرض الشبكة</span>
                              <span>📐</span>
                            </div>
                          </div>
                          <div className="text-xs text-gray-500 text-right mt-1">
                            شبكة مساعدة لترتيب العناصر بدقة
                          </div>
                        </button>

                        {/* زر المحاذاة */}
                        <button
                          onClick={() => setSnapToGrid(!snapToGrid)}
                          className={`w-full p-2 text-right rounded border transition-colors ${
                            snapToGrid
                              ? 'bg-green-50 border-green-300 text-green-700'
                              : 'bg-white border-gray-200 hover:bg-gray-50'
                          }`}
                        >
                          <div className="flex items-center justify-between">
                            <span className="text-xs font-medium">{snapToGrid ? 'مفعل' : 'معطل'}</span>
                            <div className="flex items-center gap-1">
                              <span className="text-xs">المحاذاة التلقائية</span>
                              <span>🧲</span>
                            </div>
                          </div>
                          <div className="text-xs text-gray-500 text-right mt-1">
                            محاذاة العناصر تلقائياً للشبكة
                          </div>
                        </button>

                        {/* زر التوسيط اليدوي */}
                        <button
                          onClick={centerCanvas}
                          className="w-full p-2 text-right rounded border transition-colors bg-white border-gray-200 hover:bg-gray-50"
                        >
                          <div className="flex items-center justify-between">
                            <span className="text-xs font-medium">توسيط</span>
                            <div className="flex items-center gap-1">
                              <span className="text-xs">توسيط الصفحة</span>
                              <span>🎯</span>
                            </div>
                          </div>
                          <div className="text-xs text-gray-500 text-right mt-1">
                            توسيط الصفحة في منتصف الشاشة
                          </div>
                        </button>


                      </div>

                      {/* قسم الاختصارات */}
                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <h4 className="text-xs font-medium text-gray-800 mb-2 flex items-center gap-1">
                          <Icons.Settings size={14} />
                          اختصارات لوحة المفاتيح
                        </h4>
                        <div className="space-y-1 text-xs max-h-48 overflow-y-auto">
                          {/* إدارة العناصر */}
                          <div className="bg-green-50 px-2 py-1 rounded mb-2">
                            <span className="text-green-700 font-medium text-xs flex items-center gap-1">
                              <Icons.Plus size={12} />
                              إدارة العناصر
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">نسخ العنصر</span>
                            <span className="bg-gray-100 px-1 rounded font-mono">Ctrl + C</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">لصق العنصر</span>
                            <span className="bg-gray-100 px-1 rounded font-mono">Ctrl + V</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">تكرار العنصر</span>
                            <span className="bg-gray-100 px-1 rounded font-mono">Ctrl + D</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">حذف العنصر</span>
                            <span className="bg-gray-100 px-1 rounded font-mono">Delete</span>
                          </div>

                          {/* التنقل والعرض */}
                          <div className="bg-purple-50 px-2 py-1 rounded mb-2 mt-3">
                            <span className="text-purple-700 font-medium text-xs flex items-center gap-1">
                              <Icons.Hand size={12} />
                              التنقل والعرض
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">أداة اليد (تحريك Canvas)</span>
                            <span className="bg-gray-100 px-1 rounded font-mono">Ctrl + سحب</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">زوم تفاعلي</span>
                            <span className="bg-gray-100 px-1 rounded font-mono">Alt + دولاب</span>
                          </div>

                          {/* نصائح إضافية */}
                          <div className="bg-yellow-50 px-2 py-1 rounded mb-2 mt-3">
                            <span className="text-yellow-700 font-medium text-xs flex items-center gap-1">
                              <Icons.Info size={12} />
                              نصائح سريعة
                            </span>
                          </div>
                          <div className="text-xs text-gray-500 leading-relaxed space-y-1">
                            <div>• انقر على اسم الصفحة للانتقال إليها وعرض طبقاتها</div>
                            <div>• استخدم الزر الأيمن للوصول لقائمة السياق</div>
                            <div>• اسحب الزوايا الزرقاء لتغيير حجم العناصر</div>
                            <div>• انقر مرتين على النص لتحريره مباشرة</div>
                          </div>
                        </div>
                      </div>

                      {selectedElementId && (
                        <div className="mt-3 pt-3 border-t border-gray-200">
                          <div className="text-blue-600 font-medium text-xs">العنصر المحدد:</div>
                          <div className="text-xs text-gray-600 mt-1">{selectedElementId}</div>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* مؤشرات سريعة خارج المحرر */}
              <div className="flex items-center gap-1">
                {/* قائمة منسدلة لأحجام الشاشة */}
                <div className="relative">
                  <button
                    onClick={() => setShowDeviceDropdown(!showDeviceDropdown)}
                    className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-2 py-1 rounded text-xs font-medium shadow opacity-75 hover:opacity-100 transition-all flex items-center gap-1"
                  >
                    <Icons.Smartphone size={12} />
                    <span>{allDeviceSizes.find(d => d.key === currentDevice)?.name || 'مخصص'}</span>
                    <Icons.ChevronDown size={10} />
                  </button>

                  {/* القائمة المنسدلة */}
                  {showDeviceDropdown && (
                    <div className="absolute top-full left-0 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg z-50 min-w-48">
                      <div className="py-1">
                        {deviceSizes.map((device) => (
                          <button
                            key={device.key}
                            onClick={() => {
                              changeCanvasSize(device.key, device.width, device.height);
                              setShowDeviceDropdown(false);
                            }}
                            className={`w-full text-right px-3 py-2 text-xs hover:bg-gray-100 flex items-center justify-between ${
                              currentDevice === device.key ? 'bg-blue-50 text-blue-600' : 'text-gray-700'
                            }`}
                          >
                            <span className="text-xs text-gray-500">{device.width}×{device.height}</span>
                            <div className="flex items-center gap-2">
                              <span>{device.name}</span>
                              <span>{device.icon}</span>
                            </div>
                          </button>
                        ))}

                        {/* خط فاصل */}
                        <div className="border-t border-gray-200 my-1"></div>

                        {/* حجم مخصص */}
                        <button
                          onClick={openCustomSizeModal}
                          className={`w-full text-right px-3 py-2 text-xs hover:bg-gray-100 flex items-center justify-between ${
                            currentDevice === 'custom' ? 'bg-green-50 text-green-600' : 'text-gray-700'
                          }`}
                        >
                          <span className="text-xs text-gray-500">{customSize.width}×{customSize.height}</span>
                          <div className="flex items-center gap-2">
                            <span>حجم مخصص</span>
                            <span>🎯</span>
                          </div>
                        </button>
                      </div>
                    </div>
                  )}
                </div>

                {/* مؤشر الحجم - قابل للتحرير */}
                <div className={`flex items-center gap-1 px-2 py-1 rounded text-xs font-mono shadow transition-colors ${
                  isResizingPage ? 'bg-blue-200 border border-blue-400' : 'bg-gray-300'
                }`}>
                  <span className="text-gray-700">{canvasSize.width}×</span>
                  <input
                    type="number"
                    value={Math.round(pageHeight)}
                    onChange={(e) => {
                      const newHeight = Math.max(canvasSize.height, parseInt(e.target.value) || canvasSize.height);
                      setPageHeight(newHeight);
                      setTempPageHeight(newHeight);

                      // حفظ فوري في المشروع
                      setProject(prev => ({
                        ...prev,
                        pages: prev.pages.map(page => {
                          if (page.id === currentPageId) {
                            const responsivePageHeights = page.responsivePageHeights || {};
                            const updatedResponsiveHeights = {
                              ...responsivePageHeights,
                              [currentDevice]: {
                                height: newHeight,
                                customized: true
                              }
                            };
                            return {
                              ...page,
                              pageHeight: newHeight,
                              responsivePageHeights: updatedResponsiveHeights
                            };
                          }
                          return page;
                        })
                      }));
                    }}
                    className="w-12 bg-transparent text-gray-700 text-center text-[10px] font-mono border-none outline-none focus:bg-white focus:border focus:border-blue-400 focus:rounded px-1"
                    min={canvasSize.height}
                    max="3000"
                    title="انقر لتحرير الارتفاع"
                  />
                  {pageHeight > canvasSize.height && (
                    <span className="text-blue-600">📏</span>
                  )}
                  {isResizingPage && (
                    <span className="text-blue-600 animate-pulse">⇅</span>
                  )}
                </div>

                {/* مؤشر أداة اليد */}
                {isCtrlPressed && (
                  <div className="bg-gray-300 text-gray-700 px-2 py-1 rounded text-sm shadow-lg flex items-center justify-center">
                    <Icons.Hand size={14} />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* نافذة الخصائص */}
        <PropertiesPanel
          selectedElement={selectedElement}
          onUpdateElement={updateElement}
          onDeleteElement={deleteElement}
          onUpdateRotation={updateElementRotation}
          isGeneratingElement={isGeneratingElement}
          onGenerateElement={generateElementCode}
          onPreviewStoredCode={previewStoredCode}
          isPageSelected={isPageSelected}
          currentPage={project.pages.find(p => p.id === currentPageId) || null}
          onUpdatePageProperties={updatePageProperties}
          currentDevice={currentDevice}
          onUpdateElementSize={updateElementSize}
          canvasSize={canvasSize}
          pageHeight={pageHeight}
        />
      </div>

      {/* نافذة التحليل */}
      {showAnalysis && designAnalysis && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" dir="rtl">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl max-h-[90vh] overflow-y-auto m-4">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
                  <Icons.Bot size={24} />
                  تحليل AI للتصميم
                </h2>
                <button
                  onClick={() => setShowAnalysis(false)}
                  className="text-gray-500 hover:text-gray-700 text-2xl"
                >
                  <Icons.Reset size={20} />
                </button>
              </div>
            </div>

            <div className="p-6 space-y-6">
              {/* نوع المشروع */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="text-lg font-bold text-blue-800 mb-2 flex items-center gap-2">
                  <Icons.Grid size={18} />
                  نوع المشروع
                </h3>
                <p className="text-blue-700">{designAnalysis.projectType}</p>
              </div>

              {/* المكونات */}
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="text-lg font-bold text-green-800 mb-2 flex items-center gap-2">
                  <Icons.Grid size={18} />
                  تحليل المكونات
                </h3>
                <div className="space-y-2">
                  {designAnalysis.components?.map((comp: any, index: number) => (
                    <div key={index} className="bg-white p-3 rounded border">
                      <div className="font-medium">{comp.type} - {comp.purpose}</div>
                      <div className="text-sm text-gray-600">{comp.functionality}</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* قاعدة البيانات */}
              {designAnalysis.databaseSchema?.length > 0 && (
                <div className="bg-purple-50 p-4 rounded-lg">
                  <h3 className="text-lg font-bold text-purple-800 mb-2">🗄️ قاعدة البيانات</h3>
                  <div className="space-y-2">
                    {designAnalysis.databaseSchema.map((table: any, index: number) => (
                      <div key={index} className="bg-white p-3 rounded border">
                        <div className="font-medium">جدول: {table.name}</div>
                        <div className="text-sm text-gray-600">
                          الحقول: {table.fields?.map((f: any) => f.name).join(', ')}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* API Endpoints */}
              {designAnalysis.apiEndpoints?.length > 0 && (
                <div className="bg-orange-50 p-4 rounded-lg">
                  <h3 className="text-lg font-bold text-orange-800 mb-2">🔗 واجهات البرمجة</h3>
                  <div className="space-y-2">
                    {designAnalysis.apiEndpoints.map((endpoint: any, index: number) => (
                      <div key={index} className="bg-white p-3 rounded border">
                        <div className="font-medium">{endpoint.method} {endpoint.path}</div>
                        <div className="text-sm text-gray-600">{endpoint.purpose}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* الميزات المقترحة */}
              {designAnalysis.suggestedFeatures?.length > 0 && (
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h3 className="text-lg font-bold text-yellow-800 mb-2 flex items-center gap-2">
                    <Icons.Magic size={18} />
                    الميزات المقترحة
                  </h3>
                  <ul className="list-disc list-inside space-y-1">
                    {designAnalysis.suggestedFeatures.map((feature: string, index: number) => (
                      <li key={index} className="text-yellow-700">{feature}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            <div className="p-6 border-t border-gray-200 bg-gray-50">
              <div className="flex gap-3 justify-end">
                <button
                  onClick={() => setShowAnalysis(false)}
                  className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                >
                  إغلاق
                </button>
                <button
                  onClick={() => {
                    setShowAnalysis(false);
                    openPageSelectionDialog();
                  }}
                  className="px-6 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors font-medium"
                >
                  <div className="flex items-center gap-2">
                    <Icons.Bot size={16} />
                    توليد المشروع
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نافذة عرض الملفات المولدة */}
      {showFiles && generatedFiles.length > 0 && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" dir="rtl">
          <div className="bg-white rounded-lg shadow-xl max-w-6xl max-h-[90vh] overflow-hidden m-4 flex">
            {/* قائمة الملفات */}
            <div className="w-1/3 bg-gray-50 border-r border-gray-200">
              <div className="p-4 border-b border-gray-200">
                <h3 className="text-lg font-bold text-gray-800">📁 ملفات المشروع</h3>
                <p className="text-sm text-gray-600">{generatedFiles.length} ملف</p>
              </div>
              <div className="overflow-y-auto max-h-[70vh]">
                {generatedFiles.map((file, index) => (
                  <div
                    key={index}
                    className="p-3 border-b border-gray-100 hover:bg-gray-100 cursor-pointer"
                    onClick={() => {
                      const preview = document.getElementById('file-preview');
                      if (preview) {
                        preview.textContent = file.content;
                      }
                      document.getElementById('file-name')!.textContent = file.path;
                    }}
                  >
                    <div className="font-medium text-sm">{file.path}</div>
                    <div className="text-xs text-gray-500">
                      {file.content.length} حرف
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* معاينة الملف */}
            <div className="flex-1 flex flex-col">
              <div className="p-4 border-b border-gray-200 bg-gray-50">
                <div className="flex items-center justify-between">
                  <h3 id="file-name" className="text-lg font-bold text-gray-800">اختر ملف للمعاينة</h3>
                  <button
                    onClick={() => setShowFiles(false)}
                    className="text-gray-500 hover:text-gray-700 text-2xl"
                  >
                    ×
                  </button>
                </div>
              </div>

              <div className="flex-1 overflow-auto">
                <pre
                  id="file-preview"
                  className="p-4 text-sm font-mono bg-gray-900 text-green-400 h-full overflow-auto"
                  style={{ direction: 'ltr' }}
                >
                  اختر ملف من القائمة لمعاينة محتواه...
                </pre>
              </div>
            </div>
          </div>

          {/* أزرار التحكم */}
          <div className="absolute bottom-4 right-4 flex gap-3">
            <button
              onClick={() => setShowFiles(false)}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
            >
              إغلاق
            </button>
            <button
              onClick={() => {
                downloadProjectFiles(generatedFiles, project.name);
                setShowFiles(false);
                alert('🎉 تم تحميل جميع ملفات المشروع!\nراجع مجلد التحميلات وشغل المشروع.');
              }}
              className="px-6 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors font-medium"
            >
              📥 تحميل جميع الملفات
            </button>
          </div>
        </div>
      )}

      {/* النافذة العائمة للخصائص في ملء الشاشة */}
      {isFullscreen && showFloatingProperties && selectedElementId && (
        <div
          className="fixed bg-white border border-gray-300 rounded-lg shadow-2xl w-80 max-h-96 overflow-hidden flex flex-col"
          style={{
            left: floatingPropertiesPosition.x,
            top: floatingPropertiesPosition.y,
            zIndex: 9999,
          }}
        >
          {/* شريط العنوان */}
          <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50 rounded-t-lg relative">
            {/* منطقة السحب */}
            <div
              className="absolute inset-0 cursor-move"
              onMouseDown={(e) => {
                // تجنب السحب إذا تم النقر على زر الإغلاق
                if ((e.target as HTMLElement).closest('button')) {
                  return;
                }

                const startX = e.clientX - floatingPropertiesPosition.x;
                const startY = e.clientY - floatingPropertiesPosition.y;

                const handleMouseMove = (e: MouseEvent) => {
                  setFloatingPropertiesPosition({
                    x: Math.max(0, Math.min(e.clientX - startX, window.innerWidth - 320)),
                    y: Math.max(0, Math.min(e.clientY - startY, window.innerHeight - 400))
                  });
                };

                const handleMouseUp = () => {
                  document.removeEventListener('mousemove', handleMouseMove);
                  document.removeEventListener('mouseup', handleMouseUp);
                };

                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);
              }}
            />

            <h3 className="text-sm font-bold text-gray-800 relative z-10 flex items-center gap-2">
              <Icons.Settings size={16} className="text-blue-600" />
              خصائص العنصر
            </h3>
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                closeFloatingProperties();
              }}
              className="text-gray-500 hover:text-gray-700 text-lg hover:bg-gray-200 rounded px-2 py-1 transition-colors z-20 relative"
              title="إغلاق"
            >
              ×
            </button>
          </div>

          {/* محتوى الخصائص */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-4">
              <PropertiesPanel
                selectedElement={elements.find(el => el.id === selectedElementId) || null}
                onUpdateElement={updateElement}
                onDeleteElement={deleteElement}
                onUpdateRotation={updateElementRotation}
                isFloating={true}
                isGeneratingElement={isGeneratingElement}
                onGenerateElement={generateElementCode}
                onPreviewStoredCode={previewStoredCode}
                isPageSelected={isPageSelected}
                currentPage={project.pages.find(p => p.id === currentPageId) || null}
                onUpdatePageProperties={updatePageProperties}
                currentDevice={currentDevice}
                onUpdateElementSize={updateElementSize}
                canvasSize={canvasSize}
                pageHeight={pageHeight}
              />
            </div>
          </div>


        </div>
      )}

      {/* النافذة العائمة لمكتبة العناصر في ملء الشاشة */}
      {isFullscreen && showFloatingElements && (
        <div
          className="fixed bg-white border border-gray-300 rounded-lg shadow-2xl w-80 max-h-96 overflow-hidden flex flex-col"
          style={{
            left: floatingElementsPosition.x,
            top: floatingElementsPosition.y,
            zIndex: 9998,
          }}
        >
          {/* شريط العنوان */}
          <div className="flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50 rounded-t-lg relative">
            {/* منطقة السحب */}
            <div
              className="absolute inset-0 cursor-move"
              onMouseDown={(e) => {
                // تجنب السحب إذا تم النقر على زر الإغلاق
                if ((e.target as HTMLElement).closest('button')) {
                  return;
                }

                const startX = e.clientX - floatingElementsPosition.x;
                const startY = e.clientY - floatingElementsPosition.y;

                const handleMouseMove = (e: MouseEvent) => {
                  setFloatingElementsPosition({
                    x: Math.max(0, Math.min(e.clientX - startX, window.innerWidth - 320)),
                    y: Math.max(0, Math.min(e.clientY - startY, window.innerHeight - 400))
                  });
                };

                const handleMouseUp = () => {
                  document.removeEventListener('mousemove', handleMouseMove);
                  document.removeEventListener('mouseup', handleMouseUp);
                };

                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);
              }}
            />

            <h3 className="text-sm font-bold text-gray-800 relative z-10 flex items-center gap-2">
              <Icons.Grid size={16} className="text-blue-600" />
              مكتبة العناصر
            </h3>
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                closeFloatingElements();
              }}
              className="text-gray-500 hover:text-gray-700 text-lg hover:bg-gray-200 rounded px-2 py-1 transition-colors z-20 relative"
              title="إغلاق"
            >
              ×
            </button>
          </div>

          {/* محتوى مكتبة العناصر */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-4">
              <ElementPanel onAddElement={addElement} isFloating={true} />
            </div>
          </div>
        </div>
      )}

      {/* النافذة العائمة للحجم المخصص */}
      {showCustomSizeModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" dir="rtl">
          <div className="bg-white rounded-lg shadow-xl p-6 w-96 max-w-[90vw]">
            {/* شريط العنوان */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-800">🎯 حجم مخصص</h3>
              <button
                onClick={cancelCustomSize}
                className="text-gray-500 hover:text-gray-700 text-xl"
                title="إغلاق"
              >
                ×
              </button>
            </div>

            {/* المحتوى */}
            <div className="space-y-4">
              <p className="text-sm text-gray-600 text-right">
                أدخل الأبعاد المطلوبة لورقة العمل
              </p>

              {/* مدخل العرض */}
              <div>
                <label className="block text-sm font-medium text-gray-700 text-right mb-2">
                  العرض (بكسل)
                </label>
                <input
                  type="number"
                  value={tempCustomSize.width}
                  onChange={(e) => setTempCustomSize(prev => ({
                    ...prev,
                    width: Math.max(200, parseInt(e.target.value) || 200)
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none text-center"
                  min="200"
                  max="3000"
                  placeholder="العرض"
                />
              </div>

              {/* مدخل الارتفاع */}
              <div>
                <label className="block text-sm font-medium text-gray-700 text-right mb-2">
                  الارتفاع (بكسل)
                </label>
                <input
                  type="number"
                  value={tempCustomSize.height}
                  onChange={(e) => setTempCustomSize(prev => ({
                    ...prev,
                    height: Math.max(200, parseInt(e.target.value) || 200)
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none text-center"
                  min="200"
                  max="3000"
                  placeholder="الارتفاع"
                />
              </div>

              {/* معاينة الحجم */}
              <div className="bg-gray-50 p-3 rounded-lg">
                <div className="text-sm text-gray-600 text-right mb-1">معاينة الحجم:</div>
                <div className="text-lg font-mono text-center text-blue-600">
                  {tempCustomSize.width} × {tempCustomSize.height}
                </div>
                <div className="text-xs text-gray-500 text-center mt-1">
                  النسبة: {(tempCustomSize.width / tempCustomSize.height).toFixed(2)}
                </div>
              </div>
            </div>

            {/* أزرار التحكم */}
            <div className="flex gap-3 mt-6">
              <button
                onClick={cancelCustomSize}
                className="flex-1 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              >
                إلغاء
              </button>
              <button
                onClick={applyCustomSizeFromModal}
                className="flex-1 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-medium"
              >
                تطبيق الحجم
              </button>
            </div>
          </div>
        </div>
      )}

      {/* قائمة السياق (Context Menu) */}
      {contextMenu.show && (
        <div
          className="context-menu fixed bg-white border border-gray-300 rounded-lg shadow-lg py-2 z-50"
          style={{
            left: contextMenu.x,
            top: contextMenu.y,
          }}
          onClick={(e) => e.stopPropagation()}
        >
          {contextMenu.type === 'element' && contextMenu.elementId ? (
            // قائمة العنصر
            <>
              <button
                onClick={() => copyElement(contextMenu.elementId!)}
                className="w-full px-4 py-2 text-right hover:bg-gray-100 flex items-center gap-2 text-gray-700"
              >
                <Icons.Plus size={14} />
                <span>نسخ</span>
              </button>

              {copiedElement && (
                <button
                  onClick={() => pasteElement()}
                  className="w-full px-4 py-2 text-right hover:bg-gray-100 flex items-center gap-2 text-gray-700"
                >
                  <Icons.FileText size={14} />
                  <span>لصق</span>
                </button>
              )}

              <button
                onClick={() => duplicateElement(contextMenu.elementId!)}
                className="w-full px-4 py-2 text-right hover:bg-gray-100 flex items-center gap-2 text-gray-700"
              >
                <Icons.Repeat size={14} />
                <span>تكرار</span>
              </button>

              <hr className="my-1 border-gray-200" />

              <button
                onClick={() => copyElementId(contextMenu.elementId!)}
                className="w-full px-4 py-2 text-right hover:bg-blue-100 text-blue-600 flex items-center gap-2"
              >
                <Icons.Tag size={14} />
                <span>نسخ المعرف</span>
              </button>

              <hr className="my-1 border-gray-200" />

              <button
                onClick={() => deleteElement(contextMenu.elementId!)}
                className="w-full px-4 py-2 text-right hover:bg-red-100 text-red-600 flex items-center gap-2"
              >
                <Icons.Reset size={14} />
                <span>حذف</span>
              </button>
            </>
          ) : contextMenu.type === 'page' && contextMenu.pageId ? (
            // قائمة الصفحة
            <>
              <button
                onClick={() => copyPageId(contextMenu.pageId!)}
                className="w-full px-4 py-2 text-right hover:bg-blue-100 text-blue-600 flex items-center gap-2"
              >
                <Icons.FileText size={14} />
                <span>نسخ اسم ملف الصفحة</span>
              </button>

              <hr className="my-1 border-gray-200" />

              <button
                onClick={() => switchToPage(contextMenu.pageId!)}
                className="w-full px-4 py-2 text-right hover:bg-green-100 text-green-600 flex items-center gap-2"
              >
                <Icons.Eye size={14} />
                <span>انتقل للصفحة</span>
              </button>
            </>
          ) : (
            // قائمة Canvas الفارغ
            copiedElement && (
              <button
                onClick={() => pasteElement()}
                className="w-full px-4 py-2 text-right hover:bg-gray-100 flex items-center gap-2 text-gray-700"
              >
                <Icons.FileText size={14} />
                <span>لصق</span>
              </button>
            )
          )}
        </div>
      )}

      {/* نافذة الطبقات القديمة - تم حذفها */}


      {/* نافذة اختيار الصفحات للتوليد */}
      {showPageSelection && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" dir="rtl">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
            <div className="p-6">
              <h3 className="text-xl font-bold text-gray-800 mb-4 text-center">
                <div className="flex items-center justify-center gap-2">
                  <Icons.FileText size={20} />
                  اختيار الصفحات للتوليد
                </div>
              </h3>

              <p className="text-gray-600 text-sm mb-4 text-center">
                اختر الصفحات التي تريد توليدها أو اختر الكل
              </p>

              <div className="space-y-3 mb-6">
                {project.pages.map((page) => (
                  <label key={page.id} className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={selectedPagesForGeneration.includes(page.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedPagesForGeneration(prev => [...prev, page.id]);
                        } else {
                          setSelectedPagesForGeneration(prev => prev.filter(id => id !== page.id));
                        }
                      }}
                      className="w-4 h-4 text-blue-600"
                    />
                    <div className="flex-1">
                      <div className="font-medium text-gray-800">{page.name}</div>
                      <div className="text-sm text-gray-500">
                        {page.elements.length} عنصر
                      </div>
                    </div>
                  </label>
                ))}
              </div>

              <div className="flex gap-2 mb-4">
                <button
                  onClick={() => setSelectedPagesForGeneration(project.pages.map(page => page.id))}
                  className="flex-1 px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors text-sm"
                >
                  تحديد الكل
                </button>
                <button
                  onClick={() => setSelectedPagesForGeneration([])}
                  className="flex-1 px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors text-sm"
                >
                  إلغاء التحديد
                </button>
              </div>

              <div className="flex gap-3">
                <button
                  onClick={() => setShowPageSelection(false)}
                  className="flex-1 px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                >
                  إلغاء
                </button>
                <button
                  onClick={generateSelectedPages}
                  disabled={selectedPagesForGeneration.length === 0}
                  className={`flex-1 px-4 py-2 rounded transition-colors font-medium ${
                    selectedPagesForGeneration.length === 0
                      ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                      : 'bg-green-500 text-white hover:bg-green-600'
                  }`}
                >
                  <div className="flex items-center gap-2">
                    <Icons.Bot size={16} />
                    توليد ({selectedPagesForGeneration.length})
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* نافذة معاينة العنصر المنفرد */}
      {showElementPreview && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" dir="rtl">
          <div className="bg-white rounded-lg shadow-xl max-w-4xl max-h-[90vh] overflow-y-auto m-4 w-full">
            <div className="p-6 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <button
                  onClick={() => setShowElementPreview(false)}
                  className="text-gray-500 hover:text-gray-700 text-2xl"
                >
                  <Icons.Reset size={20} />
                </button>
                <h2 className="text-2xl font-bold text-gray-800 flex items-center gap-2">
                  <Icons.Search size={24} />
                  معاينة العنصر
                </h2>
              </div>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* المعاينة المباشرة */}
                <div>
                  <h3 className="text-lg font-semibold mb-4 text-right text-gray-400 flex items-center justify-end gap-2">
                    <Icons.Smartphone size={18} />
                    المعاينة المباشرة
                  </h3>
                  <div className="border border-gray-300 rounded-lg p-4 bg-gray-50 min-h-[300px]">
                    <iframe
                      srcDoc={elementPreviewCode}
                      className="w-full h-full min-h-[280px] border-0 rounded"
                      title="معاينة العنصر"
                    />
                  </div>
                </div>

                {/* الكود المولد */}
                <div>
                  <h3 className="text-lg font-semibold mb-4 text-right text-gray-400 flex items-center justify-end gap-2">
                    <Icons.Code size={18} />
                    الكود المولد
                  </h3>
                  <div className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-auto max-h-[300px] text-sm font-mono">
                    <pre className="whitespace-pre-wrap">{elementPreviewCode}</pre>
                  </div>

                  <div className="mt-4 flex gap-2 justify-end">
                    <button
                      onClick={async () => {
                        const success = await copyToClipboard(elementPreviewCode);
                        if (success) {
                          alert('تم نسخ الكود! 📋');
                        } else {
                          alert('فشل في نسخ الكود. يرجى المحاولة مرة أخرى.');
                        }
                      }}
                      className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                    >
                      <div className="flex items-center gap-2">
                        <Icons.Plus size={16} />
                        نسخ الكود
                      </div>
                    </button>
                    <button
                      onClick={() => {
                        // تطبيق الكود على العنصر الفعلي وتسجيله كمعالج
                        const selectedElement = elements.find(el => el.id === selectedElementId);
                        if (selectedElement) {
                          updateElement(selectedElement.id, {
                            generatedCode: elementPreviewCode
                          });
                          setShowElementPreview(false);
                        }
                      }}
                      className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
                    >
                      <div className="flex items-center gap-2">
                        <Icons.Settings size={16} />
                        تطبيق على العنصر
                      </div>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      </div>
    </>
  );
}
