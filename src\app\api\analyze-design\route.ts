import { NextRequest, NextResponse } from 'next/server';

// نوع العنصر
interface Element {
  id: string;
  type: string;
  x: number;
  y: number;
  width: number;
  height: number;
  properties: any;
}

// نوع تحليل التصميم
interface DesignAnalysis {
  projectType: string;
  layout: string;
  components: ComponentAnalysis[];
  relationships: Relationship[];
  suggestedFeatures: string[];
  databaseSchema: DatabaseTable[];
  apiEndpoints: APIEndpoint[];
}

interface ComponentAnalysis {
  id: string;
  type: string;
  purpose: string;
  functionality: string;
  validation?: string;
  events: string[];
}

interface Relationship {
  from: string;
  to: string;
  type: 'triggers' | 'updates' | 'validates' | 'displays';
  description: string;
}

interface DatabaseTable {
  name: string;
  fields: DatabaseField[];
  relationships: string[];
}

interface DatabaseField {
  name: string;
  type: string;
  required: boolean;
  unique?: boolean;
}

interface APIEndpoint {
  method: string;
  path: string;
  purpose: string;
  parameters: string[];
  response: string;
}

export async function POST(request: NextRequest) {
  try {
    const requestData = await request.json();

    // دعم البنية القديمة والجديدة
    let elements: Element[] = [];
    let projectDescription = '';
    let projectName = '';

    if (requestData.pages) {
      // البنية الجديدة - مع الصفحات المتعددة
      projectName = requestData.name || '';
      projectDescription = requestData.description || '';

      // جمع جميع العناصر من جميع الصفحات
      requestData.pages.forEach((page: any) => {
        if (page.elements && Array.isArray(page.elements)) {
          elements = elements.concat(page.elements);
        }
      });
    } else {
      // البنية القديمة - مع العناصر مباشرة
      elements = requestData.elements || [];
      projectDescription = requestData.projectDescription || '';
      projectName = requestData.projectName || '';
    }

    if (!elements || !Array.isArray(elements) || elements.length === 0) {
      return NextResponse.json({ error: 'عناصر التصميم مطلوبة' }, { status: 400 });
    }

    // تحليل التصميم بالذكاء الاصطناعي مع السياق
    const analysis = await analyzeDesignWithAI(elements, projectDescription, projectName);

    return NextResponse.json({
      success: true,
      analysis
    });

  } catch (error) {
    console.error('خطأ في تحليل التصميم:', error);
    return NextResponse.json(
      { error: 'حدث خطأ في تحليل التصميم' },
      { status: 500 }
    );
  }
}

// تحليل سياق المشروع من الاسم والوصف
function analyzeProjectContext(projectName: string, description: string) {
  const name = (projectName || '').toLowerCase();
  const desc = (description || '').toLowerCase();
  const combined = `${name} ${desc}`;

  // تحديد نوع المشروع
  let projectType = 'عام';
  let domain = 'general';
  let suggestedFeatures: string[] = [];
  let suggestedTables: string[] = [];

  // تحليل المجال
  if (combined.includes('متجر') || combined.includes('تسوق') || combined.includes('منتج') || combined.includes('shop') || combined.includes('store')) {
    projectType = 'متجر إلكتروني';
    domain = 'ecommerce';
    suggestedFeatures = ['سلة التسوق', 'نظام الدفع', 'إدارة المنتجات', 'تتبع الطلبات', 'تقييم المنتجات'];
    suggestedTables = ['products', 'orders', 'cart', 'customers', 'payments'];
  }
  else if (combined.includes('مدرسة') || combined.includes('تعليم') || combined.includes('طالب') || combined.includes('school') || combined.includes('education')) {
    projectType = 'نظام تعليمي';
    domain = 'education';
    suggestedFeatures = ['إدارة الطلاب', 'نظام الدرجات', 'متابعة الحضور', 'جداول الحصص', 'تقارير الأداء'];
    suggestedTables = ['students', 'teachers', 'courses', 'grades', 'attendance'];
  }
  else if (combined.includes('مطعم') || combined.includes('طعام') || combined.includes('حجز') || combined.includes('restaurant') || combined.includes('food')) {
    projectType = 'نظام مطعم';
    domain = 'restaurant';
    suggestedFeatures = ['قائمة الطعام', 'حجز الطاولات', 'نظام الطلبات', 'التوصيل', 'إدارة المخزون'];
    suggestedTables = ['menu_items', 'reservations', 'orders', 'tables', 'delivery'];
  }
  else if (combined.includes('شركة') || combined.includes('موظف') || combined.includes('عمل') || combined.includes('company') || combined.includes('business')) {
    projectType = 'نظام إدارة شركة';
    domain = 'business';
    suggestedFeatures = ['إدارة الموظفين', 'نظام المشاريع', 'إدارة العملاء', 'التقارير المالية', 'نظام الحضور'];
    suggestedTables = ['employees', 'projects', 'clients', 'departments', 'attendance'];
  }
  else if (combined.includes('مستشفى') || combined.includes('طبي') || combined.includes('مريض') || combined.includes('hospital') || combined.includes('medical')) {
    projectType = 'نظام طبي';
    domain = 'medical';
    suggestedFeatures = ['إدارة المرضى', 'نظام المواعيد', 'السجلات الطبية', 'إدارة الأطباء', 'نظام الفواتير'];
    suggestedTables = ['patients', 'doctors', 'appointments', 'medical_records', 'prescriptions'];
  }
  else if (combined.includes('مكتبة') || combined.includes('كتاب') || combined.includes('library') || combined.includes('book')) {
    projectType = 'نظام مكتبة';
    domain = 'library';
    suggestedFeatures = ['فهرس الكتب', 'نظام الاستعارة', 'إدارة الأعضاء', 'البحث المتقدم', 'تتبع المواعيد'];
    suggestedTables = ['books', 'members', 'borrowings', 'authors', 'categories'];
  }
  else if (combined.includes('فندق') || combined.includes('حجز') || combined.includes('غرف') || combined.includes('hotel') || combined.includes('booking')) {
    projectType = 'نظام فندق';
    domain = 'hotel';
    suggestedFeatures = ['حجز الغرف', 'إدارة النزلاء', 'نظام الفواتير', 'خدمات إضافية', 'تقارير الإشغال'];
    suggestedTables = ['rooms', 'guests', 'reservations', 'services', 'payments'];
  }

  return {
    projectType,
    domain,
    suggestedFeatures,
    suggestedTables,
    context: {
      name: projectName,
      description: description,
      isSpecialized: domain !== 'general'
    }
  };
}

async function analyzeDesignWithAI(elements: Element[], description: string, projectName: string = ''): Promise<DesignAnalysis> {
  // إنشاء وصف مفصل للتصميم
  const designDescription = createDesignDescription(elements, description);

  // تحليل السياق من اسم المشروع والوصف
  const contextAnalysis = analyzeProjectContext(projectName, description);

  // البرومبت المحسن للذكاء الاصطناعي
  const systemPrompt = `أنت محلل تصميم ذكي متخصص في تحليل واجهات المستخدم وتحويلها إلى مشاريع برمجية متكاملة.

مهمتك:
1. تحليل التصميم البصري وفهم الغرض من كل عنصر
2. استخدام سياق المشروع (الاسم والوصف) لفهم المجال والوظائف المطلوبة
3. تصميم قاعدة بيانات متخصصة للمجال
4. تحديد API endpoints مناسبة للنوع
5. إضافة وظائف ذكية حسب طبيعة المشروع

قواعد التحليل المتقدمة:
- استخدم اسم المشروع ووصفه لفهم المجال (تجاري، تعليمي، طبي، إلخ)
- إذا كان مشروع متجر: أضف سلة تسوق، دفع، إدارة منتجات
- إذا كان مشروع مدرسة: أضف نظام طلاب، درجات، حضور
- إذا كان مشروع مطعم: أضف قائمة طعام، حجز طاولات، طلبات
- إذا كان مشروع شركة: أضف نظام موظفين، مشاريع، عملاء
- إذا كان مشروع طبي: أضف نظام مرضى، مواعيد، تقارير

تحليل العناصر:
- حقول إدخال + زر = نموذج متخصص للمجال
- جداول = عرض بيانات متخصصة
- أزرار = وظائف متخصصة للمجال
- نصوص = محتوى ديناميكي

أجب بصيغة JSON فقط مع تركيز على التخصص في المجال.`;

  const userPrompt = `حلل هذا التصميم مع مراعاة السياق:

=== معلومات المشروع ===
اسم المشروع: ${projectName || 'غير محدد'}
وصف المشروع: ${description || 'غير محدد'}
نوع المشروع المتوقع: ${contextAnalysis.projectType}
المجال: ${contextAnalysis.domain}

=== الميزات المقترحة للمجال ===
${contextAnalysis.suggestedFeatures.join(', ')}

=== الجداول المقترحة ===
${contextAnalysis.suggestedTables.join(', ')}

=== التصميم البصري ===
${designDescription}

=== المطلوب ===
أريد تحليل شامل ومتخصص يتضمن:
1. نوع المشروع (مع التركيز على المجال المحدد)
2. الوظائف المطلوبة (متخصصة للمجال)
3. قاعدة البيانات (جداول متخصصة)
4. API endpoints (مناسبة للمجال)
5. العلاقات بين العناصر
6. ميزات إضافية مقترحة للمجال

ملاحظة: ركز على جعل المشروع متخصص في مجال ${contextAnalysis.domain} وليس عام.`;

  try {
    // استدعاء OpenAI API
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: "gpt-4.1-nano",
        messages: [
          { role: "system", content: systemPrompt },
          { role: "user", content: userPrompt }
        ],
        max_tokens: 2000,
        temperature: 0.3,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const completion = await response.json();
    const analysisText = completion.choices[0]?.message?.content || '';

    // تنظيف وتحليل الاستجابة
    const cleanedResponse = analysisText
      .replace(/```json\n?/g, '')
      .replace(/```\n?/g, '')
      .trim();

    let analysis: DesignAnalysis;
    try {
      analysis = JSON.parse(cleanedResponse);
    } catch (parseError) {
      // إذا فشل التحليل، أنشئ تحليل افتراضي
      analysis = createFallbackAnalysis(elements, description);
    }

    return analysis;

  } catch (error) {
    console.error('خطأ في استدعاء OpenAI:', error);
    // إنشاء تحليل افتراضي في حالة الخطأ
    return createFallbackAnalysis(elements, description);
  }
}

function createDesignDescription(elements: Element[], description: string): string {
  let desc = `التصميم يحتوي على ${elements.length} عنصر:\n\n`;

  elements.forEach((element, index) => {
    desc += `${index + 1}. ${element.type} في الموقع (${element.x}, ${element.y}) بحجم ${element.width}x${element.height}\n`;
    
    if (element.properties.text) {
      desc += `   - النص: "${element.properties.text}"\n`;
    }
    if (element.properties.label) {
      desc += `   - التسمية: "${element.properties.label}"\n`;
    }
    if (element.properties.placeholder) {
      desc += `   - النص التوضيحي: "${element.properties.placeholder}"\n`;
    }
    if (element.properties.inputType) {
      desc += `   - نوع الإدخال: ${element.properties.inputType}\n`;
    }
    if (element.properties.options) {
      desc += `   - الخيارات: ${element.properties.options.join(', ')}\n`;
    }
    if (element.properties.columns) {
      desc += `   - أعمدة الجدول: ${element.properties.columns.join(', ')}\n`;
    }
    desc += '\n';
  });

  return desc;
}

function createFallbackAnalysis(elements: Element[], description: string): DesignAnalysis {
  // تحليل بسيط بناءً على العناصر الموجودة
  const hasInputs = elements.some(el => el.type === 'input' || el.type === 'textarea');
  const hasButtons = elements.some(el => el.type === 'button');
  const hasTables = elements.some(el => el.type === 'table');
  const hasForms = elements.some(el => el.type === 'form');

  let projectType = 'موقع ويب بسيط';
  if (hasInputs && hasButtons && hasTables) {
    projectType = 'نظام إدارة بيانات';
  } else if (hasInputs && hasButtons) {
    projectType = 'نموذج تفاعلي';
  } else if (hasTables) {
    projectType = 'عرض بيانات';
  }

  return {
    projectType,
    layout: 'تخطيط مخصص',
    components: elements.map(el => ({
      id: el.id,
      type: el.type,
      purpose: `عنصر ${el.type}`,
      functionality: `وظيفة ${el.type} أساسية`,
      events: ['click', 'change']
    })),
    relationships: [],
    suggestedFeatures: ['واجهة مستخدم تفاعلية', 'تصميم متجاوب'],
    databaseSchema: hasInputs ? [{
      name: 'data',
      fields: [
        { name: 'id', type: 'INTEGER', required: true, unique: true },
        { name: 'created_at', type: 'TIMESTAMP', required: true }
      ],
      relationships: []
    }] : [],
    apiEndpoints: hasInputs ? [{
      method: 'POST',
      path: '/api/data',
      purpose: 'حفظ البيانات',
      parameters: ['data'],
      response: 'success message'
    }] : []
  };
}
