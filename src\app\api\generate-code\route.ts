import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { prompt, elementType, elementId, elementProperties } = await request.json();

    if (!prompt) {
      return NextResponse.json({ error: 'البرومبت مطلوب' }, { status: 400 });
    }

    // استدعاء OpenAI API مباشرة
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: "gpt-4.1-nano",
        messages: [
          {
            role: "system",
            content: "Expert JavaScript developer. Create clean interactive code."
          },
          {
            role: "user",
            content: `Create JavaScript for: ${prompt}. Element: ${elementType} (id: ${elementId}). Properties: ${JSON.stringify(elementProperties)}. Return code only.`
          }
        ],
        max_tokens: 1000,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const completion = await response.json();
    const generatedCode = completion.choices[0]?.message?.content || '';

    // تنظيف الكود وإزالة علامات markdown والنصوص الإضافية
    const cleanCode = cleanJavaScriptResponse(generatedCode);

    return NextResponse.json({
      success: true,
      code: cleanCode,
      explanation: `تم توليد الكود للعنصر ${elementType} بناءً على الوصف المطلوب.`
    });

  } catch (error) {
    console.error('خطأ في توليد الكود:', error);
    return NextResponse.json(
      { error: 'حدث خطأ في توليد الكود' },
      { status: 500 }
    );
  }
}

// دالة لتنظيف رد JavaScript من الذكاء الاصطناعي
function cleanJavaScriptResponse(response: string): string {
  try {
    console.log('🧹 تنظيف رد JavaScript...');

    let cleanedResponse = response.trim();

    // إزالة النصوص التفسيرية في البداية
    const introPatterns = [
      /^.*?بالطبع[^:]*:/i,
      /^.*?هنا هو الكود[^:]*:/i,
      /^.*?إليك الكود[^:]*:/i,
      /^.*?الكود المطلوب[^:]*:/i,
      /^.*?يمكنك استخدام[^:]*:/i
    ];

    for (const pattern of introPatterns) {
      cleanedResponse = cleanedResponse.replace(pattern, '');
    }

    // البحث عن كود JavaScript داخل ```javascript blocks
    const jsCodeBlockMatch = cleanedResponse.match(/```javascript\s*([\s\S]*?)\s*```/i);
    if (jsCodeBlockMatch && jsCodeBlockMatch[1]) {
      console.log('✅ تم العثور على كود JavaScript داخل code block');
      return jsCodeBlockMatch[1].trim();
    }

    // البحث عن كود JavaScript داخل ``` blocks عامة
    const codeBlockMatch = cleanedResponse.match(/```\s*([\s\S]*?)\s*```/);
    if (codeBlockMatch && codeBlockMatch[1]) {
      console.log('✅ تم العثور على كود داخل code block عام');
      return codeBlockMatch[1].trim();
    }

    // إزالة علامات markdown المتبقية
    cleanedResponse = cleanedResponse
      .replace(/```javascript\n?/g, '')
      .replace(/```\n?/g, '')
      .trim();

    // إزالة النصوص التفسيرية في النهاية
    const outroPatterns = [
      /هذا الكود[\s\S]*$/i,
      /يعمل هذا[\s\S]*$/i,
      /ستحصل على[\s\S]*$/i,
      /بهذا الشكل[\s\S]*$/i
    ];

    for (const pattern of outroPatterns) {
      cleanedResponse = cleanedResponse.replace(pattern, '');
    }

    console.log('✅ تم تنظيف الكود بنجاح');
    return cleanedResponse.trim();

  } catch (error) {
    console.error('❌ خطأ في تنظيف JavaScript:', error);
    return response.trim();
  }
}
