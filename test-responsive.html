<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار التجاوب</title>
    <style>
        /* تم إنشاء هذا الملف بواسطة AI Website Builder */
        /* أبعاد ورقة العمل: 1280×720px */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            width: 1280px;
            min-height: 720px;
            max-width: 1280px;
            position: relative;
            background: white;
            border-radius: 20px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.15);
            overflow: hidden;
            margin: auto;
            border: 3px solid rgba(255,255,255,0.2);
        }

        .main-content {
            position: relative;
            width: 100%;
            min-height: 720px;
            padding: 0;
            margin: 0;
            box-sizing: border-box;
        }

        /* العناصر الأساسية */
        #element_123 {
            position: absolute;
            left: 100px;
            top: 50px;
            width: 200px;
            height: 60px;
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
        }

        #element_456 {
            position: absolute;
            left: 400px;
            top: 150px;
            width: 300px;
            height: 40px;
            background: #10b981;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
        }

        /* تصميم متجاوب للموبايل - يطبق عندما تكون الشاشة أصغر من 768px */
        @media (max-width: 768px) {
            body {
                padding: 0 !important;
                margin: 0 !important;
            }

            .container {
                margin: 0 !important;
                padding: 0 !important;
                width: 100vw !important;
                min-height: 100vh !important;
                max-width: 100vw !important;
                border-radius: 0 !important;
                box-shadow: none !important;
                border: none !important;
            }

            .main-content {
                min-height: 100vh !important;
                padding: 20px !important;
                margin: 0 !important;
            }

            #element_123 {
                left: 20px !important;
                top: 100px !important;
                position: absolute !important;
                width: calc(100vw - 40px) !important;
                max-width: 300px !important;
            }

            #element_456 {
                left: 20px !important;
                top: 200px !important;
                position: absolute !important;
                width: calc(100vw - 40px) !important;
                max-width: 300px !important;
            }
        }

        /* تصميم متجاوب للتابلت - يطبق عندما تكون الشاشة بين 769px و 1024px */
        @media (min-width: 769px) and (max-width: 1024px) {
            .container {
                width: 100vw !important;
                max-width: 100vw !important;
                border-radius: 0 !important;
                box-shadow: none !important;
            }

            .main-content {
                padding: 20px !important;
            }
        }

        @media (max-width: 480px) {
            #element_123 {
                left: 20px !important;
                top: 100px !important;
                position: absolute !important;
            }

            #element_456 {
                left: 20px !important;
                top: 200px !important;
                position: absolute !important;
            }
        }

        /* CSS للعناصر المدورة - تم إنشاؤها تلقائياً */

        /* CSS للعناصر المولدة فردياً */

    </style>
</head>
<body>
    <div class="container">
        <main class="main-content">
            <button id="element_123">زر اختبار 1</button>
            <button id="element_456">زر اختبار 2</button>
        </main>
    </div>

    <script>
        // إضافة مؤشر لحجم الشاشة للاختبار
        function showScreenSize() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            console.log(`حجم الشاشة: ${width}×${height}px`);
            
            if (width <= 480) {
                console.log('📱 شاشة صغيرة جداً');
            } else if (width <= 768) {
                console.log('📱 شاشة موبايل');
            } else if (width <= 1024) {
                console.log('📟 شاشة تابلت');
            } else {
                console.log('🖥️ شاشة ديسكتوب');
            }
        }

        // عرض حجم الشاشة عند التحميل
        showScreenSize();

        // عرض حجم الشاشة عند تغيير الحجم
        window.addEventListener('resize', showScreenSize);
    </script>
</body>
</html>
